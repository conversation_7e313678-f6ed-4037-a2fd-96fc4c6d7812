generator client {
  provider        = "prisma-client-js"
  previewFeatures = ["typedSql"]
}

generator fabbrica {
  provider = "prisma-fabbrica"
  output   = "../test/factories/fabbrica"
}

datasource db {
  provider = "mysql"
  url      = env("DATABASE_URL")
}

model Association {
  associationId         String   @id @map("association_id")
  associationInternalId BigInt   @unique @default(autoincrement()) @map("association_internal_id")
  associationName       String   @map("association_name")
  associationType       String?  @map("association_type")
  createdAt             DateTime @default(now()) @map("created_at")
  updatedAt             DateTime @updatedAt @map("updated_at")

  facilities    Facility[]
  organizations Organization[]

  @@map("associations")
}

model Facility {
  facilityId         String   @id @map("facility_id")
  facilityInternalId BigInt   @unique @default(autoincrement()) @map("facility_internal_id")
  associationId      String   @map("association_id")
  facilityName       String   @map("facility_name")
  createdAt          DateTime @default(now()) @map("created_at")
  updatedAt          DateTime @updatedAt @map("updated_at")

  association                    Association                     @relation(fields: [associationId], references: [associationId])
  trainingCourseMasters          TrainingCourseMaster[]
  reportSectionWorkoutConditions ReportSectionWorkoutCondition[]
  horseTrainingRecords           HorseTrainingRecord[]
  organizations                  Organization[]

  @@map("facilities")
}

model TrainingCourseMaster {
  trainingCourseMasterId         String   @id @map("training_course_master_id")
  trainingCourseMasterInternalId BigInt   @unique @default(autoincrement()) @map("training_course_master_internal_id")
  courseName                     String   @map("course_name")
  facilityName                   String?  @map("facility_name")
  facilityId                     String?  @map("facility_id")
  createdAt                      DateTime @default(now()) @map("created_at")
  updatedAt                      DateTime @updatedAt @map("updated_at")
  tempTrainingFacilityMasterUuid Bytes?   @map("temp_training_facility_master_uuid")

  trainingCourses TrainingCourse[]
  facility        Facility?        @relation(fields: [facilityId], references: [facilityId])

  @@map("training_course_masters")
}

model TrainingCourse {
  courseId               String    @id @map("course_id")
  courseInternalId       BigInt    @unique @default(autoincrement()) @map("course_internal_id")
  trainingCourseMasterId String?   @map("training_course_master_id")
  courseName             String    @map("course_name")
  distance               Float?
  lastNFurlong           Int?      @default(2) @map("last_n_furlong")
  openedAt               DateTime? @map("opened_at")
  closedAt               DateTime? @map("closed_at")
  createdAt              DateTime  @default(now()) @map("created_at")
  updatedAt              DateTime  @updatedAt @map("updated_at")
  tempFacilityId         BigInt?   @map("temp_facility_id")

  trainingCourseMaster           TrainingCourseMaster?           @relation(fields: [trainingCourseMasterId], references: [trainingCourseMasterId])
  reportSectionWorkoutConditions ReportSectionWorkoutCondition[]
  horseTrainingRecords           HorseTrainingRecord[]
  HorseCoursePitchAverage        HorseCoursePitchAverage[]
  TrainingPeriod                 TrainingPeriod[]

  @@map("training_courses")
}

enum FurlongLineDirection {
  left
  right
  straight
}

model FurlongLine {
  furlongLineInternalId BigInt               @id @default(autoincrement()) @map("furlong_line_internal_id")
  facilityId            BigInt               @map("facility_id")
  courseId              String?              @map("course_id") @db.VarChar(50)
  furlong               Int                  @map("furlong")
  direction             FurlongLineDirection @map("direction")
  line                  Bytes                @map("line")
  createdAt             DateTime             @default(now()) @map("created_at")
  updatedAt             DateTime             @updatedAt @map("updated_at")

  @@unique([facilityId, furlong, direction], map: "idx_facility_id_furlong_direction")
  @@map("furlong_lines")
}

enum Gender {
  male
  female
  gelding
}

model Horse {
  horseId          BigInt    @id @default(autoincrement()) @map("horse_id")
  organizationUuid Bytes?    @map("organization_uuid") @db.Binary(16)
  stableUuid       Bytes     @map("stable_uuid") @db.Binary(16)
  stable           Stable    @relation(fields: [stableUuid], references: [stableUuid])
  name             String    @map("name")
  gender           Gender?   @map("gender")
  birthYear        Int?      @map("birth_year")
  fatherId         Int?      @map("father_id")
  motherId         Int?      @map("mother_id")
  rfId             Int?      @map("rfid")
  profilePicPath   String?   @map("profile_pic_path")
  masterHorseId    String?   @map("master_horse_id")
  manageStatus     String    @default("managed") @map("manage_status")
  deletedAt        DateTime? @map("deleted_at")
  createdAt        DateTime  @default(now()) @map("created_at")
  updatedAt        DateTime  @updatedAt @map("updated_at")

  organization Organization? @relation(fields: [organizationUuid], references: [organizationUuid])
  masterHorse  MasterHorse?  @relation(fields: [masterHorseId], references: [masterHorseId])

  horseStableHistories            HorseStableHistory[]
  organizationOwnerHorseRelations OrganizationOwnerHorseRelation[]
  reports                         Report[]
  pendingSendReports              PendingSendReport[]
  horseDailyRecords               HorseDailyRecord[]
  horseHandoverNotes              HorseHandoverNote[]
  trainingPartners                TrainingPartner[]
  ReportGenerateRequest           ReportGenerateRequest[]
  trainings                       Training[]
  businessTripHistories           BusinessTripHistoryHorse[]
  StableTmTransportRecord         StableTmTransportRecord[]
  horseStatus                     HorseStatus?

  @@map("horses")
}

model HorseStableHistory {
  horseStableHistoryUuid Bytes    @id @map("horse_stable_history_uuid") @db.Binary(16)
  horseId                BigInt   @map("horse_id")
  stableUuid             Bytes    @map("stable_uuid") @db.Binary(16)
  inStable               Boolean  @default(true) @map("in_stable")
  createdAt              DateTime @default(now()) @map("created_at")

  horse  Horse  @relation(fields: [horseId], references: [horseId])
  stable Stable @relation(fields: [stableUuid], references: [stableUuid])

  @@map("horse_stable_history")
}

model HorseStatus {
  horseStatusId         Bytes     @id @map("horse_status_id") @db.Binary(16)
  horseStatusInternalId BigInt    @unique @default(autoincrement()) @map("horse_status_internal_id")
  horseId               BigInt    @unique @map("horse_id")
  inStable              Boolean   @default(true) @map("in_stable")
  outsideFarmId         Bytes?    @map("outside_farm_id") @db.Binary(16)
  latestStableInDate    DateTime? @map("latest_stable_in_date")
  latestStableOutDate   DateTime? @map("latest_stable_out_date")
  createdAt             DateTime  @default(now()) @map("created_at")
  updatedAt             DateTime  @updatedAt @map("updated_at")

  horse       Horse                @relation(fields: [horseId], references: [horseId])
  outsideFarm StableTmOutsideFarm? @relation(fields: [outsideFarmId], references: [outsideFarmId])

  @@map("horse_statuses")
}

model MasterHorse {
  masterHorseId String   @id @map("master_horse_id")
  horseName     String   @map("horse_name")
  horseNameEn   String?  @map("horse_name_en")
  motherName    String   @map("mother_name")
  gender        String   @map("gender")
  birthYear     Int      @map("birth_year")
  stableName    String?  @map("stable_name")
  createdAt     DateTime @default(now()) @map("created_at")
  updatedAt     DateTime @updatedAt @map("updated_at")

  horses                  Horse[]
  ownerHorses             OwnerHorse[]
  HorseCoursePitchAverage HorseCoursePitchAverage[]

  @@map("master_horses")
}

model Organization {
  organizationUuid Bytes    @id @map("organization_uuid") @db.Binary(16)
  organizationId   Int      @unique @default(autoincrement()) @map("organization_id")
  associationId    String?  @map("association_id")
  name             String   @map("name")
  home_facility_id String?  @map("home_facility_id")
  createdAt        DateTime @default(now()) @map("created_at")
  updatedAt        DateTime @updatedAt @map("updated_at")

  facility    Facility?    @relation(fields: [home_facility_id], references: [facilityId])
  association Association? @relation(fields: [associationId], references: [associationId])

  stables                   Stable[]
  userRoles                 UserRole[]
  horses                    Horse[]
  organizationOwners        OrganizationOwner[]
  reports                   Report[]
  organizationVeterinarians OrganizationVeterinarian[]
  organizationFarriers      OrganizationFarrier[]
  staffs                    Staff[]
  contracts                 Contract[]
  featureFlag               FeatureFlag?
  horseDailyRecords         HorseDailyRecord[]
  businessTripHistories     BusinessTripHistory[]
  StableTmOutsideFarm       StableTmOutsideFarm[]

  @@map("organizations")
}

model Stable {
  stableUuid       Bytes    @id @map("stable_uuid") @db.Binary(16)
  organizationUuid Bytes    @map("organization_uuid") @db.Binary(16)
  name             String   @map("name")
  createdAt        DateTime @default(now()) @map("created_at")
  updatedAt        DateTime @updatedAt @map("updated_at")

  organization Organization @relation(fields: [organizationUuid], references: [organizationUuid])

  stableStatus StableStatus?

  userRoles                    UserRole[]
  horses                       Horse[]
  horseStableHistories         HorseStableHistory[]
  staffs                       Staff[]
  trainingMenus                TrainingMenu[]
  trainings                    Training[]
  stableTmSections             StableTmSection[]
  stableTmTransportDailyRecord StableTmTransportDailyRecord[]

  @@map("stables")
}

model StableStatus {
  stableStatusId BigInt   @id @default(autoincrement()) @map("stable_status_id")
  stableUuid     Bytes    @unique @map("stable_uuid") @db.Binary(16)
  stallNum       Int      @map("stall_num")
  createdAt      DateTime @default(now()) @map("created_at")
  updatedAt      DateTime @updatedAt @map("updated_at")

  stable Stable? @relation(fields: [stableUuid], references: [stableUuid])

  @@map("stable_status")
}

model User {
  userUuid    Bytes     @id @map("user_uuid") @db.Binary(16)
  userId      Int       @unique @default(autoincrement()) @map("user_id")
  firebaseUid String    @unique @map("firebase_uid")
  firstName   String    @map("first_name")
  middleName  String?   @map("middle_name")
  lastName    String    @map("last_name")
  deletedAt   DateTime? @map("deleted_at")
  createdAt   DateTime  @default(now()) @map("created_at")
  updatedAt   DateTime  @updatedAt @map("updated_at")

  userRoles            UserRole[]
  staffs               Staff[]
  HorseNoteUserDevice  HorseNoteUserDevice[]
  TrainersUserSettings TrainersUserSettings[]
  UserLangSettings     UserLangSetting[]
  horseDailyRecords    HorseDailyRecord[]

  @@map("users")
}

enum Role {
  admin
  staff
  jockey
}

model UserRole {
  roleUuid         Bytes    @id @map("role_uuid") @db.Binary(16)
  roleId           Int      @unique @default(autoincrement()) @map("role_id")
  userUuid         Bytes?   @map("user_uuid") @db.Binary(16)
  role             Role     @map("role")
  organizationUuid Bytes?   @map("organization_uuid") @db.Binary(16)
  stableUuid       Bytes?   @map("stable_uuid") @db.Binary(16)
  createdAt        DateTime @default(now()) @map("created_at")
  updatedAt        DateTime @updatedAt @map("updated_at")

  organization Organization? @relation(fields: [organizationUuid], references: [organizationUuid])
  stable       Stable?       @relation(fields: [stableUuid], references: [stableUuid])
  user         User?         @relation(fields: [userUuid], references: [userUuid])

  @@map("user_roles")
}

model RacePlace {
  racePlaceId Int     @id @default(autoincrement()) @map("race_place_id")
  name        String  @db.VarChar(255)
  fullName    String? @map("full_name") @db.VarChar(255)
  code        String? @unique @db.VarChar(20)
  region      String? @db.VarChar(255)

  horseRaceResultRecords HorseRaceResultRecord[]

  @@map("race_places")
}

model OrganizationOwner {
  organizationOwnerId         String   @id @map("organization_owner_id")
  organizationOwnerInternalId BigInt   @unique @default(autoincrement()) @map("organization_owner_internal_id")
  organizationUuid            Bytes    @map("organization_uuid")
  ownerId                     String   @map("owner_id")
  organizationOwnerName       String   @map("organization_owner_name")
  createdAt                   DateTime @default(now()) @map("created_at")
  updatedAt                   DateTime @updatedAt @map("updated_at")

  organization Organization @relation(fields: [organizationUuid], references: [organizationUuid])
  owner        Owner        @relation(fields: [ownerId], references: [ownerId])

  invitations                     Invitation[]
  organizationOwnerHorseRelations OrganizationOwnerHorseRelation[]
  sentReports                     SentReport[]
  pendingSendReports              PendingSendReport[]

  @@unique([organizationUuid, ownerId])
  @@map("organization_owners")
}

model OrganizationOwnerHorseRelation {
  organizationOwnerHorseRelationId         String   @id @map("organization_owner_horse_relation_id")
  organizationOwnerHorseRelationInternalId BigInt   @unique @default(autoincrement()) @map("organization_owner_horse_relation_internal_id")
  organizationOwnerId                      String   @map("organization_owner_id")
  horseId                                  BigInt   @map("horse_id")
  createdAt                                DateTime @default(now()) @map("created_at")
  updatedAt                                DateTime @updatedAt @map("updated_at")

  organizationOwner OrganizationOwner @relation(fields: [organizationOwnerId], references: [organizationOwnerId])
  horse             Horse             @relation(fields: [horseId], references: [horseId])

  @@map("organization_owner_horse_relations")
}

enum InvitationMethod {
  email
  link
}

model Invitation {
  invitationId         String           @id @map("invitation_id")
  invitationInternalId BigInt           @unique @default(autoincrement()) @map("invitation_internal_id")
  organizationOwnerId  String           @map("organization_owner_id")
  token                String
  acceptedAt           DateTime?        @map("accepted_at")
  method               InvitationMethod
  inviteEmail          String?          @map("invite_email")
  email_dispatch_count Int              @default(0) @map("email_dispatch_count")
  createdAt            DateTime         @default(now()) @map("created_at")
  updatedAt            DateTime         @updatedAt @map("updated_at")

  organizationOwner OrganizationOwner @relation(fields: [organizationOwnerId], references: [organizationOwnerId])

  @@unique([organizationOwnerId, token])
  @@map("invitations")
}

model EmailLog {
  emailLogInternalId   BigInt   @unique @default(autoincrement()) @map("email_log_internal_id")
  email                String   @map("email")
  recipientFirebaseUid String?  @map("recipient_firebase_uid")
  senderFirebaseUid    String?  @map("sender_firebase_uid")
  emailTemplateKey     String   @map("email_template_key")
  createdAt            DateTime @default(now()) @map("created_at")

  @@map("email_logs")
}

model Owner {
  ownerId         String    @id @map("owner_id")
  ownerInternalId BigInt    @unique @default(autoincrement()) @map("owner_internal_id")
  ownerName       String?   @map("owner_name")
  firebaseUid     String?   @unique @map("firebase_uid")
  verifiedAt      DateTime? @map("verified_at")
  createdAt       DateTime  @default(now()) @map("created_at")
  updatedAt       DateTime  @updatedAt @map("updated_at")

  organizationOwners   OrganizationOwner[]
  ownerHorses          OwnerHorse[]
  userTermsAcceptances UserTermsAcceptances[]

  @@map("owners")
}

model OwnerHorse {
  ownerHorseId         String   @id @map("owner_horse_id")
  ownerHorseInternalId BigInt   @unique @default(autoincrement()) @map("owner_horse_internal_id")
  name                 String   @map("name")
  ownerId              String   @map("owner_id")
  masterHorseId        String   @map("master_horse_id")
  createdAt            DateTime @default(now()) @map("created_at")
  updatedAt            DateTime @updatedAt @map("updated_at")

  owner       Owner       @relation(fields: [ownerId], references: [ownerId])
  masterHorse MasterHorse @relation(fields: [masterHorseId], references: [masterHorseId])

  sentReports SentReport[]

  @@unique([ownerId, masterHorseId])
  @@map("owner_horses")
}

model OnetimeCode {
  onetimeCodeInternalId BigInt    @id @default(autoincrement()) @map("onetime_code_internal_id")
  code                  String    @map("code") @db.VarChar(50)
  sessionId             String    @unique @map("session_id") @db.VarChar(50)
  expiresAt             DateTime  @map("expires_at")
  verifiedAt            DateTime? @map("verified_at")
  usedAt                DateTime? @map("used_at")
  createdAt             DateTime  @default(now()) @map("created_at")
  updatedAt             DateTime  @updatedAt @map("updated_at")

  @@map("onetime_code")
}

model SentReport {
  sentReportId         String    @id @map("sent_report_id")
  sentReportInternalId BigInt    @unique @default(autoincrement()) @map("sent_report_internal_id")
  reportId             String    @map("report_id")
  organizationOwnerId  String    @map("organization_owner_id")
  ownerHorseId         String    @map("owner_horse_id")
  sentAt               DateTime  @default(now()) @map("sent_at")
  firstReadAt          DateTime? @map("first_read_at")
  createdAt            DateTime  @default(now()) @map("created_at")
  updatedAt            DateTime  @updatedAt @map("updated_at")

  report            Report            @relation(fields: [reportId], references: [reportId])
  organizationOwner OrganizationOwner @relation(fields: [organizationOwnerId], references: [organizationOwnerId])
  ownerHorse        OwnerHorse        @relation(fields: [ownerHorseId], references: [ownerHorseId])

  @@map("sent_reports")
}

model ShareReport {
  shareReportId         String   @id @map("share_report_id")
  shareReportInternalId BigInt   @unique @default(autoincrement()) @map("share_report_internal_id")
  reportId              String   @map("report_id")
  createdAt             DateTime @default(now()) @map("created_at")
  updatedAt             DateTime @updatedAt @map("updated_at")

  report Report @relation(fields: [reportId], references: [reportId])

  @@map("share_reports")
}

model PendingSendReport {
  pendingSendReportInternalId BigInt    @unique @default(autoincrement()) @map("pending_send_report_internal_id")
  reportId                    String    @map("report_id")
  organizationOwnerId         String    @map("organization_owner_id")
  horseId                     BigInt    @map("horse_id")
  sentAt                      DateTime? @map("sent_at")
  createdAt                   DateTime  @default(now()) @map("created_at")
  updatedAt                   DateTime  @updatedAt @map("updated_at")

  report            Report            @relation(fields: [reportId], references: [reportId])
  organizationOwner OrganizationOwner @relation(fields: [organizationOwnerId], references: [organizationOwnerId])
  horse             Horse             @relation(fields: [horseId], references: [horseId])

  @@map("pending_send_reports")
}

model AiGenerateLog {
  aiGenerateLogId         String   @id @map("ai_generate_log_id") @db.VarChar(50)
  aiGenerateLogInternalId BigInt   @unique @default(autoincrement()) @map("ai_generate_log_internal_id") @db.UnsignedBigInt
  model                   String?  @map("model") @db.VarChar(100)
  requestPrompt           String?  @map("request_prompt") @db.Text
  response                String?  @map("response") @db.Text
  promptTokens            Int?     @map("prompt_tokens") @db.UnsignedInt
  completionTokens        Int?     @map("completion_tokens") @db.UnsignedInt
  totalTokens             Int?     @map("total_tokens") @db.UnsignedInt
  finishReason            String?  @map("finish_reason") @db.VarChar(100)
  createdAt               DateTime @default(now()) @map("created_at")
  updatedAt               DateTime @default(now()) @updatedAt @map("updated_at")

  reportGenerateRequests ReportGenerateRequest[]

  @@map("ai_generate_logs")
}

model ReportGenerateRequest {
  reportGenerateRequestId         String   @id @map("report_generate_request_id") @db.VarChar(50)
  reportGenerateRequestInternalId BigInt   @unique @default(autoincrement()) @map("report_generate_request_internal_id") @db.UnsignedBigInt
  horseId                         BigInt   @map("horse_id") @db.UnsignedBigInt
  reportId                        String?  @map("report_id") @db.VarChar(50)
  requestMemo                     String?  @map("request_memo") @db.Text
  generatedContent                String?  @map("generated_content") @db.Text
  aiGenerateLogId                 String?  @map("ai_generate_log_id") @db.VarChar(50)
  createdAt                       DateTime @default(now()) @map("created_at")
  updatedAt                       DateTime @default(now()) @updatedAt @map("updated_at")

  horse         Horse          @relation(fields: [horseId], references: [horseId])
  report        Report?        @relation(fields: [reportId], references: [reportId])
  aiGenerateLog AiGenerateLog? @relation(fields: [aiGenerateLogId], references: [aiGenerateLogId])

  @@map("report_generate_requests")
}

model Report {
  reportId         String    @id @map("report_id")
  reportInternalId BigInt    @unique @default(autoincrement()) @map("report_internal_id")
  organizationUuid Bytes     @map("organization_uuid") @db.Binary(16)
  horseId          BigInt    @map("horse_id")
  title            String    @map("title")
  templateId       String    @map("template_id") @db.VarChar(255)
  isDraft          Boolean   @default(true) @map("is_draft")
  printed          Boolean   @default(false) @map("printed")
  firstSentAt      DateTime? @map("first_sent_at")
  createdAt        DateTime  @default(now()) @map("created_at")
  updatedAt        DateTime  @updatedAt @map("updated_at")

  horse                 Horse                   @relation(fields: [horseId], references: [horseId])
  organization          Organization            @relation(fields: [organizationUuid], references: [organizationUuid])
  reportSections        ReportSection[]
  sentReports           SentReport[]
  pendingSendReports    PendingSendReport[]
  reportGenerateRequest ReportGenerateRequest[]
  shareReport           ShareReport[]

  @@map("reports")
}

model ReportSection {
  reportSectionId         Bytes    @id @default(dbgenerated("(UUID_TO_BIN(UUID()))")) @map("report_section_id") @db.Binary(16)
  reportSectionInternalId BigInt   @unique @default(autoincrement()) @map("report_section_internal_id")
  reportId                String   @map("report_id")
  type                    String   @map("type")
  templateInnerId         String   @map("template_inner_id")
  createdAt               DateTime @default(now()) @map("created_at")
  updatedAt               DateTime @updatedAt @map("updated_at")

  report                         Report                          @relation(fields: [reportId], references: [reportId])
  reportSectionHorseConditions   ReportSectionHorseCondition[]
  reportSectionPlainTexts        ReportSectionPlainText[]
  reportSectionImages            ReportSectionImage[]
  reportSectionWorkoutConditions ReportSectionWorkoutCondition[]
  reportSectionMonthlySummaries  ReportSectionMonthlySummary[]
  reportSectionMonthlyTimelines  ReportSectionMonthlyTimeline[]
  reportSectionMedicalTreatments ReportSectionMedicalTreatment[]

  @@unique([reportId, templateInnerId])
  @@map("report_sections")
}

model ReportSectionPlainText {
  reportSectionPlainTextId Bytes    @id @default(dbgenerated("(UUID_TO_BIN(UUID()))")) @map("report_section_plain_text_id") @db.Binary(16)
  reportSectionId          Bytes    @map("report_section_id") @db.Binary(16)
  body                     String?  @map("body")
  createdAt                DateTime @default(now()) @map("created_at")
  updatedAt                DateTime @updatedAt @map("updated_at")

  reportSection ReportSection @relation(fields: [reportSectionId], references: [reportSectionId])

  @@map("report_section_plain_texts")
}

model ReportSectionImage {
  reportSectionImageId Bytes     @id @default(dbgenerated("(UUID_TO_BIN(UUID()))")) @map("report_section_image_id") @db.Binary(16)
  reportSectionId      Bytes     @map("report_section_id") @db.Binary(16)
  imagePath            String?   @map("image_path")
  capturedAt           DateTime? @map("captured_at")
  createdAt            DateTime  @default(now()) @map("created_at")
  updatedAt            DateTime  @updatedAt @map("updated_at")

  reportSection ReportSection @relation(fields: [reportSectionId], references: [reportSectionId])

  @@map("report_section_images")
}

model ReportSectionHorseCondition {
  reportSectionHorseConditionId Bytes    @id @default(dbgenerated("(UUID_TO_BIN(UUID()))")) @map("report_section_horse_condition_id") @db.Binary(16)
  reportSectionId               Bytes    @map("report_section_id") @db.Binary(16)
  horseWeight                   Int?     @map("horse_weight")
  horseWeightMeasuredDate       String?  @map("horse_weight_measured_date")
  isGaitAbnormal                Boolean? @map("is_gait_abnormal")
  gaitComment                   String?  @map("gait_comment")
  createdAt                     DateTime @default(now()) @map("created_at")
  updatedAt                     DateTime @updatedAt @map("updated_at")

  reportSection ReportSection @relation(fields: [reportSectionId], references: [reportSectionId])

  @@map("report_section_horse_conditions")
}

model ReportSectionWorkoutCondition {
  reportSectionWorkoutConditionId Bytes   @id @default(dbgenerated("(UUID_TO_BIN(UUID()))")) @map("report_section_workout_condition_id") @db.Binary(16)
  reportSectionId                 Bytes   @map("report_section_id") @db.Binary(16)
  workoutTrainingDate             String? @map("workout_training_date")
  riderName                       String? @map("rider_name")
  runningStyle                    String? @map("running_style")
  facilityId                      String? @map("facility_id")
  courseId                        String? @map("course_id")
  workoutFurlongTime              String? @map("workout_furlong_time")
  workoutFurlongPosition          String? @map("workout_furlong_time_position")
  partnerNumber                   Int?    @map("partner_number")
  partner1Name                    String? @map("partner_1_name")
  partner2Name                    String? @map("partner_2_name")
  courseGoing                     String? @map("course_going")

  createdAt DateTime @default(now()) @map("created_at")
  updatedAt DateTime @updatedAt @map("updated_at")

  reportSection  ReportSection   @relation(fields: [reportSectionId], references: [reportSectionId])
  facility       Facility?       @relation(fields: [facilityId], references: [facilityId])
  trainingCourse TrainingCourse? @relation(fields: [courseId], references: [courseId])

  @@map("report_section_workout_conditions")
}

model Staff {
  staffUuid        Bytes     @id @default(dbgenerated("(uuid_to_bin(uuid()))")) @map("staff_uuid")
  stableUuid       Bytes     @map("stable_uuid")
  name             String
  organizationUuid Bytes?    @map("organization_uuid") @db.Binary(16)
  user_uuid        Bytes?    @map("user_uuid") @db.Binary(16)
  deletedAt        DateTime? @map("deleted_at")
  createdAt        DateTime  @default(now()) @map("created_at")
  updatedAt        DateTime  @updatedAt @map("updated_at")

  stable       Stable?       @relation(fields: [stableUuid], references: [stableUuid])
  organization Organization? @relation(fields: [organizationUuid], references: [organizationUuid])
  user         User?         @relation(fields: [user_uuid], references: [userUuid])

  horseRaceRecapRecords      HorseRaceRecapRecord[]
  horseTrainingRecords       HorseTrainingRecord[]
  StableTmTransportOutStatus StableTmTransportOutStatus[]
  StableTmTransportInStatus  StableTmTransportInStatus[]

  @@map("staffs")
}

model HorseHandoverNote {
  horseHandoverNoteId         Bytes    @id @map("horse_handover_note_id") @db.Binary(16)
  horseHandoverNoteInternalId BigInt   @unique @default(autoincrement()) @map("horse_handover_note_internal_id")
  horseId                     BigInt   @unique @map("horse_id") @db.UnsignedBigInt
  handoverNote                String?  @map("handover_note") @db.Text
  nextRaceEquipmentNote       String?  @map("next_race_equipment_note") @db.Text
  fodderNote                  String?  @map("fodder_note") @db.Text
  createdAt                   DateTime @default(now()) @map("created_at")
  updatedAt                   DateTime @default(now()) @updatedAt @map("updated_at")

  horse Horse @relation(fields: [horseId], references: [horseId])

  @@map("horse_handover_notes")
}

model HorseDailyRecord {
  horseDailyRecordId Bytes    @id @map("horse_daily_record_id") @db.Binary(16)
  organizationUuid   Bytes    @map("organization_uuid") @db.Binary(16)
  horseId            BigInt   @map("horse_id")
  createdUserUuid    Bytes?   @map("created_user_uuid") @db.Binary(16)
  year               Int      @map("year")
  month              Int      @map("month")
  day                Int      @map("day")
  createdAt          DateTime @default(now()) @map("created_at")
  updatedAt          DateTime @updatedAt @map("updated_at")

  organization                 Organization                  @relation(fields: [organizationUuid], references: [organizationUuid])
  horse                        Horse                         @relation(fields: [horseId], references: [horseId])
  createdUser                  User?                         @relation(fields: [createdUserUuid], references: [userUuid])
  horseBodyPhotos              HorseBodyPhoto[]
  horseBodyRecord              HorseBodyRecord?
  horseRaceResultRecord        HorseRaceResultRecord?
  horseRaceRecapRecord         HorseRaceRecapRecord?
  horseShoeingRecords          HorseShoeingRecord[]
  horseMedicalTreatmentRecords HorseMedicalTreatmentRecord[]
  horseTrainingRecords         HorseTrainingRecord[]
  horseBodyAffectedAreaPhotos  HorseBodyAffectedAreaPhoto[]

  @@unique([horseId, year, month, day])
  @@map("horse_daily_records")
}

model HorseBodyPhoto {
  horseBodyPhotoId   Bytes    @id @map("horse_body_photo_id") @db.Binary(16)
  horseDailyRecordId Bytes    @map("horse_daily_record_id") @db.Binary(16)
  photoPath          String   @map("photo_path")
  createdAt          DateTime @default(now()) @map("created_at")
  updatedAt          DateTime @updatedAt @map("updated_at")

  horseDailyRecord HorseDailyRecord @relation(fields: [horseDailyRecordId], references: [horseDailyRecordId])

  @@map("horse_daily_records_horse_body_photos")
}

model HorseBodyAffectedAreaPhoto {
  horseBodyAffectedAreaPhotoId         Bytes    @id @map("horse_body_affected_area_photo_id") @db.Binary(16)
  horseBodyAffectedAreaPhotoInternalId BigInt   @unique @default(autoincrement()) @map("horse_body_affected_area_photo_internal_id")
  horseDailyRecordId                   Bytes    @map("horse_daily_record_id") @db.Binary(16)
  daypart                              String   @map("daypart") @db.VarChar(50)
  photoPath                            String   @map("photo_path")
  createdAt                            DateTime @default(now()) @map("created_at")
  updatedAt                            DateTime @updatedAt @map("updated_at")

  horseDailyRecord HorseDailyRecord @relation(fields: [horseDailyRecordId], references: [horseDailyRecordId])

  @@map("horse_daily_records_horse_body_affected_area_photos")
}

model HorseBodyRecord {
  horseBodyRecordId  Bytes    @id @map("horse_body_record_id") @db.Binary(16)
  horseDailyRecordId Bytes    @unique @map("horse_daily_record_id") @db.Binary(16)
  bodyWeight         Int?     @map("body_weight")
  amBodyTemperature  Float?   @map("am_body_temperature")
  pmBodyTemperature  Float?   @map("pm_body_temperature")
  amHorseBodyComment String?  @map("am_horse_body_comment")
  pmHorseBodyComment String?  @map("pm_horse_body_comment")
  amHorseBodyCare    String?  @map("am_horse_body_care")
  pmHorseBodyCare    String?  @map("pm_horse_body_care")
  freeComment        String?  @map("free_comment")
  createdAt          DateTime @default(now()) @map("created_at")
  updatedAt          DateTime @updatedAt @map("updated_at")

  horseDailyRecord HorseDailyRecord @relation(fields: [horseDailyRecordId], references: [horseDailyRecordId])

  @@map("horse_daily_records_horse_bodies")
}

model HorseTrainingRecord {
  trainingRecordUuid       Bytes    @id @map("training_record_uuid") @db.Binary(16)
  trainingRecordInternalId BigInt   @unique @default(autoincrement()) @map("training_record_internal_id") @db.UnsignedBigInt
  horseDailyRecordId       Bytes    @map("horse_daily_record_id") @db.Binary(16)
  isGaitAbnormal           Boolean? @map("is_gait_abnormal")
  gaitAbnormalDescription  String?  @map("gait_abnormal_description")
  trainingType             String?  @map("training_type")
  riderUuid                Bytes?   @map("rider_uuid") @db.Binary(16)
  trainingMenuUuid         Bytes?   @map("training_menu_uuid") @db.Binary(16)
  gateTrainingType         String?  @map("gate_training_type")
  facilityId               String?  @map("facility_id")
  facilityName             String?  @map("facility_name")
  courseId                 String?  @map("course_id")
  courseGoing              String?  @map("course_going")
  furlongTime              String?  @map("furlong_time")
  furlongTimePosition      String?  @map("furlong_time_position")
  poolTrainingType         String?  @map("pool_training_type")
  lactateLevel             Float?   @map("lactate_level")
  trainingComment          String?  @map("training_comment")
  createdAt                DateTime @default(now()) @map("created_at")
  updatedAt                DateTime @updatedAt @map("updated_at")

  horseDailyRecord HorseDailyRecord  @relation(fields: [horseDailyRecordId], references: [horseDailyRecordId])
  rider            Staff?            @relation(fields: [riderUuid], references: [staffUuid])
  trainingMenu     TrainingMenu?     @relation(fields: [trainingMenuUuid], references: [trainingMenuUuid])
  facility         Facility?         @relation(fields: [facilityId], references: [facilityId])
  trainingCourse   TrainingCourse?   @relation(fields: [courseId], references: [courseId])
  trainingPartners TrainingPartner[]

  @@map("horse_daily_records_trainings")
}

model TrainingPartner {
  trainingPartnerId  Bytes    @id @map("training_partner_id") @db.Binary(16)
  trainingRecordUuid Bytes    @map("training_record_uuid") @db.Binary(16)
  horseId            BigInt?  @map("horse_id")
  rank               Int?     @map("rank")
  horseName          String   @map("horse_name")
  trackPosition      String?  @map("track_position")
  startingOrder      Int?     @map("starting_order")
  detail             String?  @map("detail")
  intensity          String?  @map("intensity")
  margin             String?  @map("margin")
  createdAt          DateTime @default(now()) @map("created_at")
  updatedAt          DateTime @updatedAt @map("updated_at")

  horseTrainingRecord HorseTrainingRecord @relation(fields: [trainingRecordUuid], references: [trainingRecordUuid])
  horse               Horse?              @relation(fields: [horseId], references: [horseId])

  @@map("horse_daily_records_training_partners")
}

model OrganizationVeterinarian {
  organizationVeterinarianId   Bytes                         @id @map("organization_veterinarian_id") @db.Binary(16)
  veterinarianName             String                        @map("veterinarian_name")
  organizationUuid             Bytes                         @map("organization_uuid") @db.Binary(16)
  deletedAt                    DateTime?                     @map("deleted_at")
  createdAt                    DateTime                      @default(now()) @map("created_at")
  updatedAt                    DateTime                      @updatedAt @map("updated_at")
  horseMedicalTreatmentRecords HorseMedicalTreatmentRecord[]

  organization Organization @relation(fields: [organizationUuid], references: [organizationUuid])

  @@map("organization_veterinarians")
}

model OrganizationFarrier {
  organizationFarrierId Bytes                @id @map("organization_farrier_id") @db.Binary(16)
  farrierName           String               @map("farrier_name")
  organizationUuid      Bytes                @map("organization_uuid") @db.Binary(16)
  deletedAt             DateTime?            @map("deleted_at")
  createdAt             DateTime             @default(now()) @map("created_at")
  updatedAt             DateTime             @updatedAt @map("updated_at")
  horseShoeingRecords   HorseShoeingRecord[]

  organization Organization @relation(fields: [organizationUuid], references: [organizationUuid])

  @@map("organization_farriers")
}

model HorseMedicalTreatmentRecord {
  horseMedicalTreatmentRecordId   Bytes    @id @map("horse_medical_treatment_record_id") @db.Binary(16)
  horseDailyRecordId              Bytes    @map("horse_daily_record_id") @db.Binary(16)
  horseMedicalTreatmentReason     String?  @map("medical_treatment_reason")
  horseMedicalTreatmentInspection String?  @map("medical_treatment_inspection")
  horseMedicalTreatmentResult     String?  @map("medical_treatment_result")
  horseMedicalTreatmentDetail     String?  @map("medical_treatment_detail")
  organizationVeterinarianId      Bytes?   @map("organization_veterinarian_id") @db.Binary(16)
  createdAt                       DateTime @default(now()) @map("created_at")
  updatedAt                       DateTime @updatedAt @map("updated_at")

  horseDailyRecord HorseDailyRecord          @relation(fields: [horseDailyRecordId], references: [horseDailyRecordId])
  veterinarian     OrganizationVeterinarian? @relation(fields: [organizationVeterinarianId], references: [organizationVeterinarianId])

  horseMedicalTreatmentInvoicePhotos      HorseMedicalTreatmentInvoicePhoto[]
  horseMedicalTreatmentAffectedAreaPhotos HorseMedicalTreatmentAffectedAreaPhoto[]

  @@map("horse_daily_records_horse_medical_treatments")
}

model HorseMedicalTreatmentInvoicePhoto {
  horseMedicalTreatmentRecordInvoicePhotoId         Bytes    @id @map("horse_medical_treatment_invoice_photo_id") @db.Binary(16)
  horseMedicalTreatmentRecordInvoicePhotoInternalId BigInt   @unique @default(autoincrement()) @map("horse_medical_treatment_invoice_photo_internal_id")
  horseMedicalTreatmentRecordId                     Bytes    @map("horse_medical_treatment_record_id") @db.Binary(16)
  photoPath                                         String   @map("photo_path")
  createdAt                                         DateTime @default(now()) @map("created_at")
  updatedAt                                         DateTime @updatedAt @map("updated_at")

  horseMedicalTreatmentRecord HorseMedicalTreatmentRecord @relation(fields: [horseMedicalTreatmentRecordId], references: [horseMedicalTreatmentRecordId])

  @@map("horse_daily_records_horse_medical_treatment_invoice_photos")
}

model HorseMedicalTreatmentAffectedAreaPhoto {
  horseMedicalTreatmentAffectedAreaPhotoId         Bytes    @id @default(dbgenerated("(UUID_TO_BIN(UUID()))")) @map("horse_medical_treatment_affected_area_photo_id") @db.Binary(16)
  horseMedicalTreatmentAffectedAreaPhotoInternalId BigInt   @unique @default(autoincrement()) @map("horse_medical_treatment_affected_area_photo_internal_id")
  horseMedicalTreatmentRecordId                    Bytes    @map("horse_medical_treatment_record_id") @db.Binary(16)
  photoPath                                        String   @map("photo_path")
  createdAt                                        DateTime @default(now()) @map("created_at")
  updatedAt                                        DateTime @updatedAt @map("updated_at")

  horseMedicalTreatmentRecord HorseMedicalTreatmentRecord @relation(fields: [horseMedicalTreatmentRecordId], references: [horseMedicalTreatmentRecordId])

  @@map("horse_daily_records_horse_medical_treatment_affected_area_photos")
}

model HorseShoeingRecord {
  horseShoeingRecordId      Bytes    @id @map("horse_shoeing_record_id") @db.Binary(16)
  horseDailyRecordId        Bytes    @map("horse_daily_record_id") @db.Binary(16)
  organizationFarrierId     Bytes?   @map("organization_farrier_id") @db.Binary(16)
  horseShoeingTreatmentType String?  @map("horse_shoeing_treatment_type")
  createdAt                 DateTime @default(now()) @map("created_at")
  updatedAt                 DateTime @updatedAt @map("updated_at")
  comment                   String?  @map("comment")

  horseDailyRecord          HorseDailyRecord           @relation(fields: [horseDailyRecordId], references: [horseDailyRecordId])
  farrier                   OrganizationFarrier?       @relation(fields: [organizationFarrierId], references: [organizationFarrierId])
  horseShoeingInvoicePhotos HorseShoeingInvoicePhoto[]

  @@map("horse_daily_records_horse_shoeings")
}

model HorseShoeingInvoicePhoto {
  horseShoeingInvoicePhotoId         Bytes    @id @map("horse_shoeing_invoice_photo_id") @db.Binary(16)
  horseShoeingInvoicePhotoInternalId BigInt   @unique @default(autoincrement()) @map("horse_shoeing_invoice_photo_internal_id")
  horseShoeingRecordId               Bytes    @map("horse_shoeing_record_id") @db.Binary(16)
  photoPath                          String   @map("photo_path")
  createdAt                          DateTime @default(now()) @map("created_at")
  updatedAt                          DateTime @updatedAt @map("updated_at")

  horseShoeingRecord HorseShoeingRecord @relation(fields: [horseShoeingRecordId], references: [horseShoeingRecordId])

  @@map("horse_daily_records_horse_shoeing_invoice_photos")
}

model HorseRaceResultRecord {
  raceResultId         Bytes    @id @map("race_result_id") @db.Binary(16)
  raceResultInternalId BigInt   @unique @default(autoincrement()) @map("race_result_internal_id") @db.UnsignedBigInt
  horseDailyRecordId   Bytes    @unique @map("horse_daily_record_id") @db.Binary(16)
  racePlaceId          Int?     @map("race_place_id")
  raceName             String?  @map("race_name") @db.VarChar(255)
  raceNumber           Int?     @map("race_number")
  distance             Int?
  going                String?  @db.VarChar(50)
  trackType            String?  @map("track_type") @db.VarChar(100)
  jockeyName           String?  @map("jockey_name") @db.VarChar(100)
  weight               Int?
  beforeRaceWeightDiff Int?     @map("before_race_weight_diff")
  rank                 Int?
  createdAt            DateTime @default(now()) @map("created_at")
  updatedAt            DateTime @updatedAt @map("updated_at")

  horseDailyRecord HorseDailyRecord @relation(fields: [horseDailyRecordId], references: [horseDailyRecordId])
  racePlace        RacePlace?       @relation(fields: [racePlaceId], references: [racePlaceId])

  @@map("horse_daily_records_race_results")
}

model HorseRaceRecapRecord {
  raceRecapId         Bytes    @id @map("race_recap_id") @db.Binary(16)
  raceRecapInternalId BigInt   @unique @default(autoincrement()) @map("race_recap_internal_id") @db.UnsignedBigInt
  horseDailyRecordId  Bytes    @unique @map("horse_daily_record_id") @db.Binary(16)
  attendance          String?  @db.VarChar(100)
  staffUuid           Bytes?   @map("staff_uuid") @db.Binary(16)
  equipment           String?  @db.VarChar(255)
  transportComment    String?  @map("transport_comment") @db.Text
  stallComment        String?  @map("stall_comment") @db.Text
  paddockComment      String?  @map("paddock_comment") @db.Text
  warmUpComment       String?  @map("warm_up_comment") @db.Text
  gateComment         String?  @map("gate_comment") @db.Text
  raceStrategyComment String?  @map("race_strategy_comment") @db.Text
  afterRaceComment    String?  @map("after_race_comment") @db.Text
  jockeyComment       String?  @map("jockey_comment") @db.Text
  trainerComment      String?  @map("trainer_comment") @db.Text
  nextRaceComment     String?  @map("next_race_comment") @db.Text
  createdAt           DateTime @default(now()) @map("created_at")
  updatedAt           DateTime @updatedAt @map("updated_at")

  horseDailyRecord HorseDailyRecord @relation(fields: [horseDailyRecordId], references: [horseDailyRecordId])
  staff            Staff?           @relation(fields: [staffUuid], references: [staffUuid])

  @@map("horse_daily_records_race_recaps")
}

model TrainingMenu {
  trainingMenuUuid       Bytes     @id @map("training_menu_uuid") @db.Binary(16)
  trainingMenuInternalId BigInt    @unique @default(autoincrement()) @map("training_menu_internal_id") @db.UnsignedBigInt
  trainingMenuName       String    @map("training_menu_name")
  trainingType           String?   @map("training_type") @db.VarChar(255)
  stableUuid             Bytes     @map("stable_uuid") @db.Binary(16)
  deletedAt              DateTime? @map("deleted_at")
  createdAt              DateTime  @default(now()) @map("created_at")
  updatedAt              DateTime  @updatedAt @map("updated_at")

  stable               Stable                @relation(fields: [stableUuid], references: [stableUuid])
  horseTrainingRecords HorseTrainingRecord[]

  @@map("training_menus")
}

model Training {
  trainingId         BigInt    @id @default(autoincrement()) @map("training_id")
  horseId            BigInt    @map("horse_id")
  trainerUuid        Bytes?    @map("trainer_uuid") @db.Binary(16)
  trainerId          BigInt?   @map("trainer_id")
  stableUuid         Bytes     @map("stable_uuid") @db.Binary(16)
  startAt            DateTime? @map("start_at")
  endAt              DateTime? @map("end_at")
  analysisStartedAt  DateTime? @map("analysis_started_at")
  analysisEndedAt    DateTime? @map("analysis_ended_at")
  locationDataPath   String?   @map("location_data_path") @db.VarChar(200)
  locationDataExt    String?   @map("location_data_ext") @db.VarChar(50)
  gyroAndAccDataPath String?   @map("gyro_and_acc_data_path") @db.VarChar(200)
  gyroAndAccDataExt  String?   @map("gyro_and_acc_data_ext") @db.VarChar(50)
  deletedAt          DateTime? @map("deleted_at")
  createdAt          DateTime  @default(now()) @map("created_at")
  updatedAt          DateTime  @updatedAt @map("updated_at")

  timeSeriesHeartBeatResults TimeSeriesHeartBeatResult[]
  trainingIndicators         TrainingIndicator[]
  trainingPeriods            TrainingPeriod[]
  gaitAnalysisResult         GaitAnalysisResult[]
  horse                      Horse?                      @relation(fields: [horseId], references: [horseId])
  stable                     Stable?                     @relation(fields: [stableUuid], references: [stableUuid])

  // Indexes
  @@unique([trainingId])
  @@map("training")
}

model TrainingIndicator {
  trainingIndicatorId               BigInt   @id @default(autoincrement()) @map("training_indicator_id")
  trainingId                        BigInt   @map("training_id")
  periodGroupId                     Int      @map("period_group_id")
  facilityId                        BigInt?  @map("facility_id")
  courseId                          String?  @map("course_id") @db.VarChar(50)
  maxHeartRate                      Int?     @map("max_heart_rate")
  maxHeartRateInAll                 Int?     @map("max_heart_rate_in_all")
  maxHeartRateInLap                 Int?     @map("max_heart_rate_in_lap")
  thr100                            Int?     @map("thr100")
  v200                              Float?   @map("v200")
  oneMinuteHeartRate                Int?     @map("one_minute_heart_rate")
  threeMinutesMinHeartRate          Int?     @map("three_minutes_min_heart_rate")
  heartRateGap                      Int?     @map("heart_rate_gap")
  thirtySecondsAfterGoalHeartRate   Int?     @map("thirty_seconds_after_goal_heart_rate")
  oneMinuteAfterGoalHeartRate       Int?     @map("one_minute_after_goal_heart_rate")
  twoMinutesAfterGoalHeartRate      Int?     @map("two_minutes_after_goal_heart_rate")
  twoMinutesAfterGoalMinHeartRate   Int?     @map("two_minutes_after_goal_min_heart_rate")
  threeMinutesAfterGoalMinHeartRate Int?     @map("three_minutes_after_goal_min_heart_rate")
  createdAt                         DateTime @default(now()) @map("created_at")
  updatedAt                         DateTime @updatedAt @map("updated_at")

  training               Training                 @relation(fields: [trainingId], references: [trainingId])
  TrainingIndicatorLabel TrainingIndicatorLabel[]

  @@index([trainingId], map: "training_indicators_training_id_fk")
  @@map("training_indicators")
}

model TrainingPeriod {
  trainingPeriodUuid Bytes      @id @default(dbgenerated("(uuid_to_bin(uuid()))")) @map("training_period_uuid")
  trainingId         BigInt     @map("training_id")
  periodGroupId      Int        @map("period_group_id")
  periodType         PeriodType @map("period_type")
  facilityId         BigInt?    @map("facility_id")
  courseId           String?    @map("course_id") @db.VarChar(50)
  direction          String?    @map("direction") @db.VarChar(50)
  startAt            DateTime?  @map("start_at")
  startTime          Float      @map("start_time")
  endTime            Float      @map("end_time")
  startDistance      Float      @map("start_distance")
  endDistance        Float      @map("end_distance")
  lapCount           Int?       @map("lap_count")
  isMain             Boolean    @default(false) @map("is_main")
  leftLegRatio       Float?     @map("left_leg_ratio")
  totalDistance      Float?     @map("total_distance")
  createdAt          DateTime   @default(now()) @map("created_at")
  updatedAt          DateTime   @updatedAt @map("updated_at")

  training       Training        @relation(fields: [trainingId], references: [trainingId])
  trainingCourse TrainingCourse? @relation(fields: [courseId], references: [courseId])

  @@unique([trainingId, periodGroupId, lapCount], map: "training_periods_training_period_group_lap_unique")
  @@map("training_periods")
}

enum PeriodType {
  all
  all_in_facility
  one_lap
}

model TrainingIndicatorLabel {
  trainingIndicatorLabelId BigInt   @id @default(autoincrement()) @map("training_indicator_label_id")
  trainingIndicatorId      BigInt   @map("training_indicator_id")
  label                    String   @map("label") @db.VarChar(50)
  time                     Int      @map("time")
  distance                 Int      @map("distance")
  createdAt                DateTime @default(now()) @map("created_at")
  updatedAt                DateTime @updatedAt @map("updated_at")

  trainingIndicator TrainingIndicator @relation(fields: [trainingIndicatorId], references: [trainingIndicatorId])

  @@index([trainingIndicatorId], map: "training_indicator_labels_training_indicator_id_fk")
  @@map("training_indicator_labels")
}

model TimeSeriesHeartBeatResult {
  timeSeriesHeartBeatResultId BigInt   @id @default(autoincrement()) @map("time_series_heart_beat_result_id") @db.UnsignedBigInt
  trainingId                  BigInt   @map("training_id") @db.UnsignedBigInt
  time                        Int
  heartRate                   Int?     @map("heart_rate")
  sympatheticNerve            Float?   @map("sympathetic_nerve")
  parasympatheticNerve        Float?   @map("parasympathetic_nerve")
  createdAt                   DateTime @default(now()) @map("created_at")

  training Training @relation(fields: [trainingId], references: [trainingId])

  @@unique([trainingId, time])
  @@map("time_series_heart_beat_results")
}

model GaitAnalysisResult {
  gaitAnalysisResultUuid   Bytes    @id @default(dbgenerated("(uuid_to_bin(uuid()))")) @map("gait_analysis_result_uuid") @db.Binary(16)
  trainingId               BigInt   @map("training_id")
  startAt                  DateTime @map("start_at")
  endAt                    DateTime @map("end_at")
  gait                     Gait?    @map("gait")
  impactLeftFront          Float?   @map("impact_left_front")
  impactRightFront         Float?   @map("impact_right_front")
  impactLeftBack           Float?   @map("impact_left_back")
  impactRightBack          Float?   @map("impact_right_back")
  swingTimeRatioLeftFront  Float?   @map("swing_time_ratio_left_front")
  swingTimeRatioRightFront Float?   @map("swing_time_ratio_right_front")
  swingTimeRatioLeftBack   Float?   @map("swing_time_ratio_left_back")
  swingTimeRatioRightBack  Float?   @map("swing_time_ratio_right_back")
  footOnAngleLeftFront     Float?   @map("foot_on_angle_left_front")
  footOnAngleRightFront    Float?   @map("foot_on_angle_right_front")
  footOnAngleLeftBack      Float?   @map("foot_on_angle_left_back")
  footOnAngleRightBack     Float?   @map("foot_on_angle_right_back")
  footOffAngleLeftFront    Float?   @map("foot_off_angle_left_front")
  footOffAngleRightFront   Float?   @map("foot_off_angle_right_front")
  footOffAngleLeftBack     Float?   @map("foot_off_angle_left_back")
  footOffAngleRightBack    Float?   @map("foot_off_angle_right_back")
  createdAt                DateTime @default(now()) @map("created_at")

  training Training @relation(fields: [trainingId], references: [trainingId])

  @@map("gait_analysis_results")
}

enum Gait {
  stable
  walk
  trot
  canter
  gallop
}

model HorseCoursePitchAverage {
  horseCoursePitchAverageUuid Bytes    @id @default(dbgenerated("(uuid_to_bin(uuid()))")) @map("horse_course_pitch_average_uuid")
  masterHorseId               String   @map("master_horse_id") @db.VarChar(50)
  courseId                    String   @map("course_id") @db.VarChar(50)
  speed                       Int      @map("speed")
  relativeAveragePitch        Float?   @map("relative_average_pitch")
  absoluteAveragePitch        Float?   @map("absolute_average_pitch")
  deviationScore              Float?   @map("deviation_score")
  createdAt                   DateTime @default(now()) @map("created_at")
  updatedAt                   DateTime @updatedAt @map("updated_at")

  masterHorse MasterHorse    @relation(fields: [masterHorseId], references: [masterHorseId])
  course      TrainingCourse @relation(fields: [courseId], references: [courseId])

  @@unique([masterHorseId, courseId, speed], map: "unique_horse_course_speed")
  @@index([masterHorseId], map: "horse_course_pitch_averages_master_horse_id_fk")
  @@index([courseId], map: "horse_course_pitch_averages_course_id_idx")
  @@map("horse_course_pitch_averages")
}

model UserTermsAcceptances {
  userTermsAcceptanceId Bytes    @id @default(dbgenerated("(uuid_to_bin(uuid()))")) @map("user_terms_acceptance_id") @db.Binary(16)
  acceptedAt            DateTime @map("accepted_at")
  createdAt             DateTime @default(now()) @map("created_at")
  updatedAt             DateTime @updatedAt @map("updated_at")
  ownerId               String   @map("owner_id")

  owner Owner @relation(fields: [ownerId], references: [ownerId])

  @@map("user_terms_acceptances")
}

model HorseNoteUserDevice {
  deviceId           String    @id @map("device_id")
  osName             String?   @map("os_name")
  osVersion          String?   @map("os_version")
  modelName          String?   @map("model_name")
  appRuntimeVersion  String?   @map("app_runtime_version")
  appUpdateCreatedAt DateTime? @map("app_update_created_at")
  userUuid           Bytes?    @map("user_uuid") @db.Binary(16)
  createdAt          DateTime  @default(now()) @map("created_at")
  updatedAt          DateTime  @updatedAt @map("updated_at")

  user User? @relation(fields: [userUuid], references: [userUuid])

  @@map("horse_note_user_devices")
}

model TrainersUserSettings {
  trainersUserSettingsId            Bytes    @id @default(dbgenerated("(uuid_to_bin(uuid()))")) @map("trainers_user_settings_id") @db.Binary(16)
  userUuid                          Bytes    @map("user_uuid") @db.Binary(16)
  reportUnreadFilterTruncateEnabled Boolean  @default(false) @map("report_unread_filter_truncate_enabled")
  createdAt                         DateTime @default(now()) @map("created_at")
  updatedAt                         DateTime @updatedAt @map("updated_at")

  user User? @relation(fields: [userUuid], references: [userUuid])

  @@map("trainers_user_settings")
}

model Contract {
  contractId       BigInt   @id @default(autoincrement()) @map("contract_id")
  organizationUuid Bytes    @unique @map("organization_uuid") @db.Binary(16)
  hasHnContract    Boolean  @default(false) @map("has_hn_contract")
  hasOrmContract   Boolean  @default(false) @map("has_orm_contract")
  hasOrmAiContract Boolean  @default(false) @map("has_orm_ai_contract")
  hasStmContracts  Boolean  @default(false) @map("has_stm_contracts")
  hasRmContract    Boolean  @default(false) @map("has_rm_contract")
  createdAt        DateTime @default(now()) @map("created_at")
  updatedAt        DateTime @updatedAt @map("updated_at")

  organization Organization @relation(fields: [organizationUuid], references: [organizationUuid])

  @@map("contracts")
}

model ReportSectionMonthlySummary {
  reportSectionMonthlySummaryId         Bytes    @id @default(dbgenerated("(UUID_TO_BIN(UUID()))")) @map("report_section_monthly_summary_id") @db.Binary(16)
  reportSectionMonthlySummaryInternalId BigInt   @unique @default(autoincrement()) @map("report_section_monthly_summary_internal_id")
  reportSectionId                       Bytes    @map("report_section_id") @db.Binary(16)
  startYear                             Int      @map("start_year")
  startMonth                            Int      @map("start_month")
  startDay                              Int      @map("start_day")
  endYear                               Int      @map("end_year")
  endMonth                              Int      @map("end_month")
  endDay                                Int      @map("end_day")
  horseBodyWeightHistory                String?  @map("horse_body_weight_history") @db.Text
  createdAt                             DateTime @default(now()) @map("created_at")
  updatedAt                             DateTime @updatedAt @map("updated_at")

  reportSection                          ReportSection                           @relation(fields: [reportSectionId], references: [reportSectionId])
  reportSectionMonthlySummaryRaceRecords ReportSectionMonthlySummaryRaceRecord[]

  @@map("report_section_monthly_summaries")
}

model ReportSectionMonthlySummaryRaceRecord {
  reportSectionMonthlySummaryRaceRecordId         Bytes    @id @default(dbgenerated("(UUID_TO_BIN(UUID()))")) @map("report_section_monthly_summary_race_record_id") @db.Binary(16)
  reportSectionMonthlySummaryRaceRecordInternalId BigInt   @unique @default(autoincrement()) @map("report_section_monthly_summary_race_record_internal_id")
  reportSectionMonthlySummaryId                   Bytes    @map("report_section_monthly_summary_id") @db.Binary(16)
  year                                            Int      @map("year")
  month                                           Int      @map("month")
  day                                             Int      @map("day")
  body                                            String?  @map("body") @db.Text
  createdAt                                       DateTime @default(now()) @map("created_at")
  updatedAt                                       DateTime @updatedAt @map("updated_at")

  reportSectionMonthlySummary ReportSectionMonthlySummary @relation(fields: [reportSectionMonthlySummaryId], references: [reportSectionMonthlySummaryId])

  @@map("report_section_monthly_summary_race_records")
}

model ReportSectionMonthlyTimeline {
  reportSectionMonthlyTimelineId         Bytes    @id @default(dbgenerated("(UUID_TO_BIN(UUID()))")) @map("report_section_monthly_timeline_id") @db.Binary(16)
  reportSectionMonthlyTimelineInternalId BigInt   @unique @default(autoincrement()) @map("report_section_monthly_timeline_internal_id")
  reportSectionId                        Bytes    @map("report_section_id") @db.Binary(16)
  createdAt                              DateTime @default(now()) @map("created_at")
  updatedAt                              DateTime @updatedAt @map("updated_at")

  reportSection                       ReportSection                        @relation(fields: [reportSectionId], references: [reportSectionId])
  reportSectionMonthlyTimelineRecords ReportSectionMonthlyTimelineRecord[]

  @@map("report_section_monthly_timelines")
}

model ReportSectionMonthlyTimelineRecord {
  reportSectionMonthlyTimelineRecordId         Bytes    @id @default(dbgenerated("(UUID_TO_BIN(UUID()))")) @map("report_section_monthly_timeline_record_id") @db.Binary(16)
  reportSectionMonthlyTimelineRecordInternalId BigInt   @unique @default(autoincrement()) @map("report_section_monthly_timeline_record_internal_id")
  reportSectionMonthlyTimelineId               Bytes    @map("report_section_monthly_timeline_id") @db.Binary(16)
  year                                         Int      @map("year")
  month                                        Int      @map("month")
  day                                          Int      @map("day")
  body                                         String?  @map("body") @db.Text
  trainingMenu                                 String?  @map("training_menu") @db.Text
  assignee                                     String?  @map("assignee") @db.Text
  furlongTime                                  String?  @map("furlong_time") @db.VarChar(255)
  index                                        String   @map("index") @db.VarChar(255)
  createdAt                                    DateTime @default(now()) @map("created_at")
  updatedAt                                    DateTime @updatedAt @map("updated_at")

  reportSectionMonthlyTimeline ReportSectionMonthlyTimeline @relation(fields: [reportSectionMonthlyTimelineId], references: [reportSectionMonthlyTimelineId])

  @@map("report_section_monthly_timeline_records")
}

model ReportSectionMedicalTreatment {
  reportSectionMedicalTreatmentId         Bytes    @id @default(dbgenerated("(UUID_TO_BIN(UUID()))")) @map("report_section_medical_treatment_id") @db.Binary(16)
  reportSectionMedicalTreatmentInternalId BigInt   @unique @default(autoincrement()) @map("report_section_medical_treatment_internal_id")
  reportSectionId                         Bytes    @map("report_section_id") @db.Binary(16)
  createdAt                               DateTime @default(now()) @map("created_at")
  updatedAt                               DateTime @updatedAt @map("updated_at")

  reportSection                        ReportSection                         @relation(fields: [reportSectionId], references: [reportSectionId])
  reportSectionMedicalTreatmentRecords ReportSectionMedicalTreatmentRecord[]

  @@map("report_section_medical_treatments")
}

model ReportSectionMedicalTreatmentRecord {
  reportSectionMedicalTreatmentRecordId         Bytes    @id @default(dbgenerated("(UUID_TO_BIN(UUID()))")) @map("report_section_medical_treatment_record_id") @db.Binary(16)
  reportSectionMedicalTreatmentRecordInternalId BigInt   @unique @default(autoincrement()) @map("report_section_medical_treatment_record_internal_id")
  reportSectionMedicalTreatmentId               Bytes    @map("report_section_medical_treatment_id") @db.Binary(16)
  year                                          Int      @map("year")
  month                                         Int      @map("month")
  day                                           Int      @map("day")
  body                                          String?  @map("body") @db.Text
  veterinarian                                  String?  @map("veterinarian") @db.VarChar(255)
  createdAt                                     DateTime @default(now()) @map("created_at")
  updatedAt                                     DateTime @updatedAt @map("updated_at")

  reportSectionMedicalTreatment                   ReportSectionMedicalTreatment                    @relation(fields: [reportSectionMedicalTreatmentId], references: [reportSectionMedicalTreatmentId])
  reportSectionMedicalTreatmentAffectedAreaPhotos ReportSectionMedicalTreatmentAffectedAreaPhoto[]

  @@map("report_section_medical_treatment_records")
}

model ReportSectionMedicalTreatmentAffectedAreaPhoto {
  reportSectionMedicalTreatmentAffectedAreaPhotoId         Bytes    @id @default(dbgenerated("(UUID_TO_BIN(UUID()))")) @map("report_section_medical_treatment_affected_area_photo_id") @db.Binary(16)
  reportSectionMedicalTreatmentAffectedAreaPhotoInternalId BigInt   @unique @default(autoincrement()) @map("report_section_medical_treatment_affected_area_photo_internal_id")
  reportSectionMedicalTreatmentRecordId                    Bytes    @map("report_section_medical_treatment_record_id") @db.Binary(16)
  photoPath                                                String   @map("photo_path")
  createdAt                                                DateTime @default(now()) @map("created_at")
  updatedAt                                                DateTime @updatedAt @map("updated_at")

  reportSectionMedicalTreatmentRecord ReportSectionMedicalTreatmentRecord @relation(fields: [reportSectionMedicalTreatmentRecordId], references: [reportSectionMedicalTreatmentRecordId])

  @@map("report_section_medical_treatment_affected_area_photos")
}

model FeatureFlag {
  featureFlagId    BigInt   @id @default(autoincrement()) @map("feature_flag_id")
  organizationUuid Bytes    @map("organization_uuid") @db.Binary(16)
  monthlyReport    Boolean  @default(false) @map("monthly_report")
  businessTrip     Boolean  @default(false) @map("business_trip")
  languageSetting  Boolean  @default(false) @map("language_setting")
  createdAt        DateTime @default(now()) @map("created_at")
  updatedAt        DateTime @updatedAt @map("updated_at")

  organization Organization @relation(fields: [organizationUuid], references: [organizationUuid])

  @@unique([organizationUuid])
  @@map("feature_flags")
}

model BusinessTripHistory {
  businessTripHistoryId Bytes    @id @default(dbgenerated("(uuid_to_bin(uuid()))")) @map("business_trip_history_id") @db.Binary(16)
  organizationUuid      Bytes    @map("organization_uuid") @db.Binary(16)
  staffName             String?  @map("staff_name")
  destinationName       String?  @map("destination_name")
  startYear             Int?     @map("start_year")
  startMonth            Int?     @map("start_month")
  startDay              Int?     @map("start_day")
  endYear               Int?     @map("end_year")
  endMonth              Int?     @map("end_month")
  endDay                Int?     @map("end_day")
  createdAt             DateTime @default(now()) @map("created_at")
  updatedAt             DateTime @default(now()) @updatedAt @map("updated_at")

  organization Organization               @relation(fields: [organizationUuid], references: [organizationUuid])
  horses       BusinessTripHistoryHorse[]

  @@map("business_trip_histories")
}

model BusinessTripHistoryHorse {
  businessTripHistoryHorseId Bytes    @id @default(dbgenerated("(uuid_to_bin(uuid()))")) @map("business_trip_history_horse_id") @db.Binary(16)
  businessTripHistoryId      Bytes    @map("business_trip_history_id") @db.Binary(16)
  horseId                    BigInt   @map("horse_id")
  createdAt                  DateTime @default(now()) @map("created_at")
  updatedAt                  DateTime @default(now()) @updatedAt @map("updated_at")

  businessTripHistory BusinessTripHistory @relation(fields: [businessTripHistoryId], references: [businessTripHistoryId])
  horse               Horse               @relation(fields: [horseId], references: [horseId])

  @@unique([businessTripHistoryId, horseId], map: "business_trip_history_horses_unique")
  @@map("business_trip_history_horses")
}

model UserLangSetting {
  userLangSettingId BigInt   @id @default(autoincrement()) @map("user_lang_setting_id")
  userUuid          Bytes    @map("user_uuid") @db.Binary(16)
  lang              String   @map("lang")
  createdAt         DateTime @default(now()) @map("created_at")
  updatedAt         DateTime @updatedAt @map("updated_at")

  user User? @relation(fields: [userUuid], references: [userUuid])

  @@unique([userUuid], map: "user_lang_settings_unique")
  @@map("user_lang_settings")
}

model FarmArea {
  farmAreaId         Bytes    @id @map("farm_area_id") @db.Binary(16)
  farmAreaInternalId BigInt   @unique @default(autoincrement()) @map("farm_area_internal_id") @db.UnsignedBigInt
  name               String   @map("name")
  order              Int      @map("order")
  createdAt          DateTime @default(now()) @map("created_at")
  updatedAt          DateTime @updatedAt @map("updated_at")

  masterFarms         MasterFarm[]
  StableTmOutsideFarm StableTmOutsideFarm[]

  @@map("farm_areas")
}

model MasterFarm {
  masterFarmId         Bytes    @id @map("master_farm_id") @db.Binary(16)
  masterFarmInternalId BigInt   @unique @default(autoincrement()) @map("master_farm_internal_id") @db.UnsignedBigInt
  farmAreaId           Bytes    @map("farm_area_id") @db.Binary(16)
  name                 String   @map("name")
  createdAt            DateTime @default(now()) @map("created_at")
  updatedAt            DateTime @updatedAt @map("updated_at")

  farmArea             FarmArea              @relation(fields: [farmAreaId], references: [farmAreaId])
  stableTmOutsideFarms StableTmOutsideFarm[]

  @@map("master_farms")
}

model StableTmOutsideFarm {
  outsideFarmId         Bytes     @id @map("outside_farm_id") @db.Binary(16)
  outsideFarmInternalId BigInt    @unique @default(autoincrement()) @map("outside_farm_internal_id") @db.UnsignedBigInt
  organizationUuid      Bytes     @map("organization_uuid") @db.Binary(16)
  farmAreaId            Bytes     @map("farm_area_id") @db.Binary(16)
  name                  String    @map("name")
  masterFarmId          Bytes?    @map("master_farm_id") @db.Binary(16)
  deletedAt             DateTime? @map("deleted_at")
  createdAt             DateTime  @default(now()) @map("created_at")
  updatedAt             DateTime  @updatedAt @map("updated_at")

  organization               Organization                 @relation(fields: [organizationUuid], references: [organizationUuid])
  farmArea                   FarmArea                     @relation(fields: [farmAreaId], references: [farmAreaId])
  masterFarm                 MasterFarm?                  @relation(fields: [masterFarmId], references: [masterFarmId])
  StableTmTransportOutStatus StableTmTransportOutStatus[]
  HorseStatus                HorseStatus[]

  @@map("stable_tm_outside_farm")
}

model StableTmSection {
  sectionId         Bytes    @id @map("section_id") @db.Binary(16)
  sectionInternalId BigInt   @unique @default(autoincrement()) @map("section_internal_id") @db.UnsignedBigInt
  stableUuid        Bytes    @map("stable_uuid") @db.Binary(16)
  startDate         DateTime @map("start_date")
  endDate           DateTime @map("end_date")
  status            String?  @map("status")
  createdAt         DateTime @default(now()) @map("created_at")
  updatedAt         DateTime @updatedAt @map("updated_at")

  stable                Stable                         @relation(fields: [stableUuid], references: [stableUuid])
  transportQueueTickets StableTmTransportQueueTicket[]
  fixedSlots            StableTmFixedSlot[]
  transportDailyRecords StableTmTransportDailyRecord[]

  @@map("stable_tm_sections")
}

model StableTmFixedSlot {
  fixedSlotId         Bytes    @id @map("fixed_slot_id") @db.Binary(16)
  fixedSlotInternalId BigInt   @unique @default(autoincrement()) @map("fixed_slot_internal_id") @db.UnsignedBigInt
  sectionId           Bytes    @map("section_id") @db.Binary(16)
  numberOfSection     Int      @map("number_of_section")
  slotNum             Int      @map("slot_num")
  createdAt           DateTime @default(now()) @map("created_at")
  updatedAt           DateTime @updatedAt @map("updated_at")

  section StableTmSection @relation(fields: [sectionId], references: [sectionId])

  @@map("stable_tm_fixed_slots")
}

model StableTmTransportQueueTicket {
  transportQueueTicketId         Bytes    @id @map("transport_queue_ticket_id") @db.Binary(16)
  transportQueueTicketInternalId BigInt   @unique @default(autoincrement()) @map("transport_queue_ticket_internal_id") @db.UnsignedBigInt
  sectionId                      Bytes    @map("section_id") @db.Binary(16)
  ticketKey                      String   @map("ticket_key")
  createdAt                      DateTime @default(now()) @map("created_at")
  updatedAt                      DateTime @updatedAt @map("updated_at")

  section StableTmSection @relation(fields: [sectionId], references: [sectionId])

  @@map("stable_tm_transport_queue_tickets")
}

model StableTmTransportDailyRecord {
  transportDailyRecordId         Bytes    @id @map("transport_daily_record_id") @db.Binary(16)
  transportDailyRecordInternalId BigInt   @unique @default(autoincrement()) @map("transport_daily_record_internal_id") @db.UnsignedBigInt
  sectionId                      Bytes    @map("section_id") @db.Binary(16)
  stableUuid                     Bytes    @map("stable_uuid") @db.Binary(16)
  year                           Int      @map("year")
  month                          Int      @map("month")
  day                            Int      @map("day")
  isConfirmed                    Boolean  @default(false) @map("is_confirmed")
  createdAt                      DateTime @default(now()) @map("created_at")
  updatedAt                      DateTime @updatedAt @map("updated_at")

  section          StableTmSection           @relation(fields: [sectionId], references: [sectionId])
  stable           Stable                    @relation(fields: [stableUuid], references: [stableUuid])
  transportRecords StableTmTransportRecord[]

  @@map("stable_tm_transport_daily_records")
}

model StableTmTransportRecord {
  transportRecordId         Bytes    @id @map("transport_record_id") @db.Binary(16)
  transportRecordInternalId BigInt   @unique @default(autoincrement()) @map("transport_record_internal_id") @db.UnsignedBigInt
  transportDailyRecordId    Bytes?   @map("transport_daily_record_id") @db.Binary(16)
  type                      String   @map("type")
  horseId                   BigInt   @map("horse_id") @db.UnsignedBigInt
  index                     String   @map("index") @db.VarChar(255)
  createdAt                 DateTime @default(now()) @map("created_at")
  updatedAt                 DateTime @updatedAt @map("updated_at")

  transportDailyRecord StableTmTransportDailyRecord? @relation(fields: [transportDailyRecordId], references: [transportDailyRecordId])
  horse                Horse                         @relation(fields: [horseId], references: [horseId])
  transportInStatus    StableTmTransportInStatus?
  transportOutStatus   StableTmTransportOutStatus?

  @@map("stable_tm_transport_records")
}

model StableTmTransportInStatus {
  transportInStatusId         Bytes    @id @map("transport_in_status_id") @db.Binary(16)
  transportInStatusInternalId BigInt   @unique @default(autoincrement()) @map("transport_in_status_internal_id") @db.UnsignedBigInt
  transportRecordId           Bytes    @unique @map("transport_record_id") @db.Binary(16)
  staffUuid                   Bytes?   @map("staff_uuid") @db.Binary(16)
  isHorseVanArranged          Boolean  @default(false) @map("is_horse_van_arranged")
  isQuarantineApplied         Boolean  @default(false) @map("is_quarantine_applied")
  isOwnerContacted            Boolean  @default(false) @map("is_owner_contacted")
  isFarmContacted             Boolean  @default(false) @map("is_farm_contacted")
  nextRace                    String?  @map("next_race")
  comment                     String?  @map("comment")
  createdAt                   DateTime @default(now()) @map("created_at")
  updatedAt                   DateTime @updatedAt @map("updated_at")

  transportRecord StableTmTransportRecord @relation(fields: [transportRecordId], references: [transportRecordId])
  staff           Staff?                  @relation(fields: [staffUuid], references: [staffUuid])

  @@map("stable_tm_transport_in_statuses")
}

model StableTmTransportOutStatus {
  transportOutStatusId          Bytes    @id @map("transport_out_status_id") @db.Binary(16)
  transportOutStatusInternalId  BigInt   @unique @default(autoincrement()) @map("transport_out_status_internal_id") @db.UnsignedBigInt
  transportRecordId             Bytes    @unique @map("transport_record_id") @db.Binary(16)
  staffUuid                     Bytes?   @map("staff_uuid") @db.Binary(16)
  isStableOutProcedureCompleted Boolean  @default(false) @map("is_stable_out_procedure_completed")
  isHorseVanArranged            Boolean  @default(false) @map("is_horse_van_arranged")
  isOwnerContacted              Boolean  @default(false) @map("is_owner_contacted")
  isFarmContacted               Boolean  @default(false) @map("is_farm_contacted")
  farmId                        Bytes?   @map("farm_id") @db.Binary(16)
  comment                       String?  @map("comment")
  createdAt                     DateTime @default(now()) @map("created_at")
  updatedAt                     DateTime @updatedAt @map("updated_at")

  transportRecord           StableTmTransportRecord            @relation(fields: [transportRecordId], references: [transportRecordId])
  staff                     Staff?                             @relation(fields: [staffUuid], references: [staffUuid])
  farm                      StableTmOutsideFarm?               @relation(fields: [farmId], references: [outsideFarmId])
  transportOutHandoverNotes StableTmTransportOutHandoverNote[]

  @@map("stable_tm_transport_out_statuses")
}

model StableTmTransportOutHandoverNote {
  transportOutHandoverNoteId         Bytes     @id @map("transport_out_handover_note_id") @db.Binary(16)
  transportOutHandoverNoteInternalId BigInt    @unique @default(autoincrement()) @map("transport_out_handover_note_internal_id") @db.UnsignedBigInt
  transportOutStatusId               Bytes     @map("transport_out_status_id") @db.Binary(16)
  body                               String?   @map("body")
  latestHorseShoeingDate             DateTime? @map("latest_horse_shoeing_date")
  latestHorseShoeingFarrier          String?   @map("latest_horse_shoeing_farrier")
  latestHorseShoeingBody             String?   @map("latest_horse_shoeing_body")
  latestHorseBodyWeightDate          DateTime? @map("latest_horse_body_weight_date")
  latestHorseBodyWeight              String?   @map("latest_horse_body_weight")
  createdAt                          DateTime  @default(now()) @map("created_at")
  updatedAt                          DateTime  @updatedAt @map("updated_at")

  transportOutStatus StableTmTransportOutStatus @relation(fields: [transportOutStatusId], references: [transportOutStatusId])

  @@map("stable_tm_transport_out_handover_notes")
}
