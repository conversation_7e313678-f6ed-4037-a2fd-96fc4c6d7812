{
  "name": "equtum-management-api",
  "dockerComposeFile": "./../docker/docker-compose.yml",
  "service": "management-api",
  "workspaceFolder": "/var/equtum-management-api",
  "forwardPorts": [3000],
  // "onCreateCommand": "npm install && npx prisma generate --sql",
  "features": {
    "ghcr.io/devcontainers/features/github-cli:1": {}
  },
  "customizations": {
    "vscode": {
      "extensions": [
        "esbenp.prettier-vscode",
        "Prisma.prisma",
        "zxh404.vscode-proto3",
        "vitest.explorer",
        "streetsidesoftware.code-spell-checker",
        "biomejs.biome"
      ]
    },
    "runArgs": ["--name", "equtum-management-api"]
  }
}
