import { Code, ConnectError } from "@connectrpc/connect";
import type {
  StableTmTransportRecord as PrismaTransportRecord,
  StableTmTransportInStatus,
  StableTmTransportOutHandoverNote,
  StableTmTransportOutStatus,
  Staff,
  StableTmTransportDailyRecord as TransportDailyRecord,
} from "@prisma/client";
import { generateKeyBetween } from "fractional-indexing";
import { parse, v7 as uuidv7 } from "uuid";
import { client as prisma } from "./client";
import type { HorsesWithStableHistories } from "./horse_repository";
import type { RepositoryFunction as RF } from "./repository";
import type { OutsideFarm } from "./stable_tm_outside_farm_repository";

export type HorseTransportRecord = PrismaTransportRecord & {
  horse: HorsesWithStableHistories;
  transportDailyRecord: TransportDailyRecord | null;
  transportInStatus:
    | (StableTmTransportInStatus & {
        staff: Staff | null;
      })
    | null;
  transportOutStatus:
    | (StableTmTransportOutStatus & {
        transportOutHandoverNotes: StableTmTransportOutHandoverNote[];
        farm: OutsideFarm | null;
        staff: Staff | null;
      })
    | null;
};

export type HorseTransportOutStatus = StableTmTransportOutStatus & {
  transportOutHandoverNotes: StableTmTransportOutHandoverNote[];
};

type FetchHorseTransportRecordInput =
  | {
      transportRecordId: Uint8Array;
    }
  | {
      horseId: bigint;
      year: number;
      month: number;
      day: number;
    };

export const fetchHorseTransportRecord: RF<
  FetchHorseTransportRecordInput,
  HorseTransportRecord
> = async (input, tx) => {
  const client = tx ?? prisma;
  if ("transportRecordId" in input) {
    const { transportRecordId } = input;

    const record = await client.stableTmTransportRecord.findUnique({
      where: {
        transportRecordId,
      },
      include: {
        horse: {
          include: {
            horseStableHistories: {
              orderBy: {
                createdAt: "desc",
              },
            },
            horseStatus: {
              include: {
                outsideFarm: {
                  include: {
                    farmArea: true,
                  },
                },
              },
            },
          },
        },
        transportDailyRecord: true,
        transportInStatus: {
          include: {
            staff: true,
          },
        },
        transportOutStatus: {
          include: {
            transportOutHandoverNotes: true,
            farm: {
              include: {
                farmArea: true,
              },
            },
            staff: true,
          },
        },
      },
    });

    if (!record) {
      throw new ConnectError("horseTransportRecord not found", Code.NotFound);
    }

    return record;
  }
  if ("horseId" in input) {
    const { horseId, year, month, day } = input;

    const record = await client.stableTmTransportRecord.findFirst({
      where: {
        transportDailyRecord: {
          year,
          month,
          day,
        },
        horseId,
      },
      include: {
        horse: {
          include: {
            horseStableHistories: {
              orderBy: {
                createdAt: "desc",
              },
            },
            horseStatus: {
              include: {
                outsideFarm: {
                  include: {
                    farmArea: true,
                  },
                },
              },
            },
          },
        },
        transportDailyRecord: true,
        transportInStatus: {
          include: {
            staff: true,
          },
        },
        transportOutStatus: {
          include: {
            transportOutHandoverNotes: true,
            farm: {
              include: {
                farmArea: true,
              },
            },
            staff: true,
          },
        },
      },
    });

    if (!record) {
      throw new ConnectError("horseTransportRecord not found", Code.NotFound);
    }

    return record;
  }
  throw new ConnectError("Invalid input", Code.InvalidArgument);
};

type FetchTransportRecordsWithoutDailyRecordInput = {
  stableUuid: Uint8Array;
};

export const fetchTransportRecordsWithoutDailyRecord: RF<
  FetchTransportRecordsWithoutDailyRecordInput,
  HorseTransportRecord[]
> = async (input, tx) => {
  const client = tx ?? prisma;
  const { stableUuid } = input;
  const records = await client.stableTmTransportRecord.findMany({
    where: {
      transportDailyRecord: null,
      horse: {
        stableUuid,
      },
    },
    include: {
      horse: {
        include: {
          horseStableHistories: {
            orderBy: {
              createdAt: "desc",
            },
          },
          horseStatus: {
            include: {
              outsideFarm: {
                include: {
                  farmArea: true,
                },
              },
            },
          },
        },
      },
      transportDailyRecord: true,
      transportInStatus: {
        include: {
          staff: true,
        },
      },
      transportOutStatus: {
        include: {
          transportOutHandoverNotes: true,
          farm: {
            include: {
              farmArea: true,
            },
          },
          staff: true,
        },
      },
    },
  });
  return records;
};

type FetchHorseTransportRecordsInput =
  | {
      transportDailyRecordId: Uint8Array<ArrayBufferLike>;
    }
  | {
      transportDailyRecordIds: Uint8Array<ArrayBufferLike>[];
    };

export const fetchHorseTransportRecords: RF<
  FetchHorseTransportRecordsInput,
  HorseTransportRecord[]
> = async (input, tx) => {
  const client = tx ?? prisma;
  if ("transportDailyRecordId" in input) {
    const records = await client.stableTmTransportRecord.findMany({
      where: {
        transportDailyRecordId: input.transportDailyRecordId,
      },
      include: {
        horse: {
          include: {
            horseStableHistories: {
              orderBy: {
                createdAt: "desc",
              },
            },
            horseStatus: {
              include: {
                outsideFarm: {
                  include: {
                    farmArea: true,
                  },
                },
              },
            },
          },
        },
        transportDailyRecord: true,
        transportInStatus: {
          include: {
            staff: true,
          },
        },
        transportOutStatus: {
          include: {
            transportOutHandoverNotes: true,
            farm: {
              include: {
                farmArea: true,
              },
            },
            staff: true,
          },
        },
      },
      orderBy: {
        index: "asc",
      },
    });
    return records;
  }
  if ("transportDailyRecordIds" in input) {
    const { transportDailyRecordIds } = input;
    const records = await client.stableTmTransportRecord.findMany({
      where: {
        transportDailyRecordId: { in: transportDailyRecordIds },
      },
      include: {
        horse: {
          include: {
            horseStableHistories: {
              orderBy: {
                createdAt: "desc",
              },
            },
            horseStatus: {
              include: {
                outsideFarm: {
                  include: {
                    farmArea: true,
                  },
                },
              },
            },
          },
        },
        transportDailyRecord: true,
        transportInStatus: {
          include: {
            staff: true,
          },
        },
        transportOutStatus: {
          include: {
            transportOutHandoverNotes: true,
            farm: {
              include: {
                farmArea: true,
              },
            },
            staff: true,
          },
        },
      },
      orderBy: [
        {
          transportDailyRecordId: "asc",
        },
        {
          index: "asc",
        },
      ],
    });
    return records;
  }
  throw new ConnectError("Invalid input", Code.InvalidArgument);
};

type FetchLatestHorseTransportRecordsInput = {
  horseIds: bigint[];
};

export const fetchLatestHorseTransportRecords: RF<
  FetchLatestHorseTransportRecordsInput,
  HorseTransportRecord[]
> = async (input, tx) => {
  const client = tx ?? prisma;
  const records = await client.stableTmTransportRecord.findMany({
    where: { horseId: { in: input.horseIds } },
    orderBy: { createdAt: "desc" },
    include: {
      horse: {
        include: {
          horseStableHistories: {
            orderBy: {
              createdAt: "desc",
            },
          },
          horseStatus: {
            include: {
              outsideFarm: {
                include: {
                  farmArea: true,
                },
              },
            },
          },
        },
      },
      transportDailyRecord: true,
      transportInStatus: {
        include: {
          staff: true,
        },
      },
      transportOutStatus: {
        include: {
          transportOutHandoverNotes: true,
          farm: {
            include: {
              farmArea: true,
            },
          },
          staff: true,
        },
      },
    },
    distinct: ["horseId"],
  });
  return records;
};

type FetchLatestHorseTransportRecordsWithinPeriodInput = {
  horseIds: bigint[];
  endDate: Date;
};

export const fetchLatestHorseTransportRecordsWithinPeriod: RF<
  FetchLatestHorseTransportRecordsWithinPeriodInput,
  HorseTransportRecord[]
> = async (input, tx) => {
  const client = tx ?? prisma;
  const records = await client.stableTmTransportRecord.findMany({
    where: {
      horseId: { in: input.horseIds },
      transportDailyRecord: {
        OR: [
          // transportDailyRecordが存在し、endDate以前のもの
          {
            year: { lt: input.endDate.getFullYear() },
          },
          {
            year: input.endDate.getFullYear(),
            month: { lt: input.endDate.getMonth() + 1 },
          },
          {
            year: input.endDate.getFullYear(),
            month: input.endDate.getMonth() + 1,
            day: { lte: input.endDate.getDate() },
          },
        ],
      },
    },
    orderBy: { createdAt: "desc" },
    include: {
      horse: {
        include: {
          horseStableHistories: {
            orderBy: {
              createdAt: "desc",
            },
          },
          horseStatus: {
            include: {
              outsideFarm: {
                include: {
                  farmArea: true,
                },
              },
            },
          },
        },
      },
      transportDailyRecord: true,
      transportInStatus: {
        include: {
          staff: true,
        },
      },
      transportOutStatus: {
        include: {
          transportOutHandoverNotes: true,
          farm: {
            include: {
              farmArea: true,
            },
          },
          staff: true,
        },
      },
    },
    distinct: ["horseId"],
  });
  return records;
};

type CreateHorseTransportRecordInput = {
  transportDailyRecordId?: Uint8Array<ArrayBufferLike>;
  horseId: bigint;
  type: string;
  previousRecordId?: Buffer | null;
  nextRecordId?: Buffer | null;
};

export const createHorseTransportRecord: RF<
  CreateHorseTransportRecordInput,
  HorseTransportRecord
> = async (input, tx) => {
  const client = tx ?? prisma;

  // 前後のレコードのindexを取得
  let prevIndex: string | null = null;
  let nextIndex: string | null = null;

  if (input.previousRecordId) {
    const prevRecord = await client.stableTmTransportRecord.findUnique({
      where: {
        transportRecordId: input.previousRecordId,
      },
    });
    if (!prevRecord) {
      throw new ConnectError("Previous record not found", Code.NotFound);
    }
    if (prevRecord.transportDailyRecordId === input.transportDailyRecordId) {
      prevIndex = prevRecord.index;
    }
  }

  if (input.nextRecordId) {
    const nextRecord = await client.stableTmTransportRecord.findUnique({
      where: {
        transportRecordId: input.nextRecordId,
      },
    });
    if (!nextRecord) {
      throw new ConnectError("Next record not found", Code.NotFound);
    }
    if (nextRecord.transportDailyRecordId === input.transportDailyRecordId) {
      nextIndex = nextRecord.index;
    }
  }

  const index = generateKeyBetween(prevIndex, nextIndex);

  const record = await client.stableTmTransportRecord.create({
    data: {
      transportRecordId: parse(uuidv7()),
      transportDailyRecordId: input.transportDailyRecordId,
      type: input.type,
      horseId: input.horseId,
      index,
    },
    include: {
      horse: {
        include: {
          horseStableHistories: {
            orderBy: {
              createdAt: "desc",
            },
          },
          horseStatus: {
            include: {
              outsideFarm: {
                include: {
                  farmArea: true,
                },
              },
            },
          },
        },
      },
      transportDailyRecord: true,
      transportInStatus: {
        include: {
          staff: true,
        },
      },
      transportOutStatus: {
        include: {
          transportOutHandoverNotes: true,
          farm: {
            include: {
              farmArea: true,
            },
          },
          staff: true,
        },
      },
    },
  });
  return record;
};

type UpdateHorseTransportRecordInput = {
  transportRecordId: Uint8Array;
  transportDailyRecordId?: Uint8Array;
  horseId?: bigint;
  type?: string;
};

export const updateHorseTransportRecord: RF<
  UpdateHorseTransportRecordInput,
  HorseTransportRecord
> = async (input, tx) => {
  const client = tx ?? prisma;
  const { transportRecordId, ...updateFields } = input;
  const record = await client.stableTmTransportRecord.update({
    where: { transportRecordId },
    data: updateFields,
    include: {
      horse: {
        include: {
          horseStableHistories: {
            orderBy: {
              createdAt: "desc",
            },
          },
          horseStatus: {
            include: {
              outsideFarm: {
                include: {
                  farmArea: true,
                },
              },
            },
          },
        },
      },
      transportDailyRecord: true,
      transportInStatus: {
        include: {
          staff: true,
        },
      },
      transportOutStatus: {
        include: {
          transportOutHandoverNotes: true,
          farm: {
            include: {
              farmArea: true,
            },
          },
          staff: true,
        },
      },
    },
  });
  return record;
};

type DeleteHorseTransportRecordInput = {
  transportRecordId: Uint8Array;
};

type ReorderHorseTransportRecordInput = {
  transportRecordId: Uint8Array;
  transportDailyRecordId: Uint8Array | null;
  previousRecordId?: Uint8Array | null;
  nextRecordId?: Uint8Array | null;
};

export const reorderHorseTransportRecord: RF<
  ReorderHorseTransportRecordInput,
  HorseTransportRecord
> = async (input, tx) => {
  const client = tx ?? prisma;

  const targetRecord = await client.stableTmTransportRecord.findUnique({
    where: {
      transportRecordId: input.transportRecordId,
    },
  });

  if (!targetRecord) {
    throw new ConnectError("Record not found", Code.NotFound);
  }

  // 前後のレコードのindexを取得
  let prevIndex: string | null = null;
  let nextIndex: string | null = null;

  if (input.previousRecordId) {
    const prevRecord = await client.stableTmTransportRecord.findUnique({
      where: {
        transportRecordId: input.previousRecordId,
        transportDailyRecordId: input.transportDailyRecordId,
      },
      include: {
        transportDailyRecord: true,
      },
    });
    if (!prevRecord) {
      throw new ConnectError("Previous record not found", Code.NotFound);
    }
    prevIndex = prevRecord.index;
  }

  if (input.nextRecordId) {
    const nextRecord = await client.stableTmTransportRecord.findUnique({
      where: {
        transportRecordId: input.nextRecordId,
        transportDailyRecordId: input.transportDailyRecordId,
      },
      include: {
        transportDailyRecord: true,
      },
    });
    if (!nextRecord) {
      throw new ConnectError("Next record not found", Code.NotFound);
    }
    nextIndex = nextRecord.index;
  }

  const newIndex = generateKeyBetween(prevIndex, nextIndex);

  const updatedRecord = await client.stableTmTransportRecord.update({
    where: {
      transportRecordId: input.transportRecordId,
    },
    data: {
      transportDailyRecordId: input.transportDailyRecordId,
      index: newIndex,
    },
    include: {
      horse: {
        include: {
          horseStableHistories: {
            orderBy: {
              createdAt: "desc",
            },
          },
          horseStatus: {
            include: {
              outsideFarm: {
                include: {
                  farmArea: true,
                },
              },
            },
          },
        },
      },
      transportDailyRecord: true,
      transportInStatus: {
        include: {
          staff: true,
        },
      },
      transportOutStatus: {
        include: {
          transportOutHandoverNotes: true,
          farm: {
            include: {
              farmArea: true,
            },
          },
          staff: true,
        },
      },
    },
  });

  return updatedRecord;
};

export const deleteHorseTransportRecord: RF<
  DeleteHorseTransportRecordInput,
  void
> = async (input, tx) => {
  const client = tx ?? prisma;
  const record = await client.stableTmTransportRecord.findUnique({
    where: { transportRecordId: input.transportRecordId },
  });
  if (!record) {
    throw new ConnectError("horseTransportRecord not found", Code.NotFound);
  }
  await client.stableTmTransportRecord.delete({
    where: { transportRecordId: input.transportRecordId },
  });
  return;
};
