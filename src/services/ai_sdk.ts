import { anthropic } from "@ai-sdk/anthropic";
import { openai } from "@ai-sdk/openai";
import { Code, ConnectError } from "@connectrpc/connect";
import { generateText } from "ai";
import { nanoid } from "nanoid";
import { createAiGenerateLog } from "@/repositories/ai_generate_log_repository";

const anthropicModel = anthropic("claude-3-7-sonnet-20250219");
const openaiModel = openai("gpt-4o-mini");

export interface CreateMessageWithAiSdkResponse {
  text: string;
  response: {
    id: string;
  };
}

export const createMessageWithAiSdk = async (
  content: string,
): Promise<CreateMessageWithAiSdkResponse> => {
  const result = await generateTextWithLog(content, anthropicModel);

  if (result?.finishReason === "error" || !result) {
    return await fallbackToOpenAI(content);
  }

  return { text: result.text, response: result.response };
};

// AnthropicとOpenAIを並列で呼び出し、先に返ってきた結果を使用する
const fallbackToOpenAI = async (
  content: string,
): Promise<CreateMessageWithAiSdkResponse> => {
  const [anthropicPromise, openaiPromise] = [
    generateTextWithLog(content, anthropicModel),
    generateTextWithLog(content, openaiModel),
  ];

  const results = await Promise.allSettled([anthropicPromise, openaiPromise]);

  // Anthropicの結果が成功した場合はそれを返す
  if (results[0].status === "fulfilled" && results[0].value) {
    const { text, response } = results[0].value;
    return {
      text,
      response,
    };
  }

  // OpenAIの結果が成功した場合はそれを返す
  if (results[1].status === "fulfilled" && results[1].value) {
    const { text, response } = results[1].value;
    return {
      text,
      response,
    };
  }

  throw new ConnectError("AI APIの呼び出しに失敗しました", Code.Internal);
};

const generateTextWithLog = async (
  content: string,
  model: ReturnType<typeof anthropic> | ReturnType<typeof openai>,
) => {
  try {
    const result = await generateText({
      model,
      prompt: content,
    });

    await createAiGenerateLog({
      aiGenerateLogId: result.response.id,
      model: model.modelId,
      requestPrompt: content,
      response: result.text,
      promptTokens: result.usage.inputTokens,
      completionTokens: result.usage.outputTokens,
      totalTokens: result.usage.totalTokens,
      finishReason: result.finishReason,
    });

    return result;
  } catch (_error) {
    await createAiGenerateLog({
      aiGenerateLogId: nanoid(12),
      model: model.modelId,
      requestPrompt: content,
      finishReason: "error",
    });
    return null;
  }
};
