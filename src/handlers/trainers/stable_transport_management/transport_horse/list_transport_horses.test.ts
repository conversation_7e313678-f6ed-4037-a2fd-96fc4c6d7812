import { create } from "@bufbuild/protobuf";
import {
  ListTransportHorsesRequestSchema,
  TransportHorseService,
} from "@schema/gen/trainers/stable_transport_management/transport_horse_service_pb";
import { HorseFactory } from "@test/factories/horse_factory";
import { HorseStatusFactory } from "@test/factories/horse_status_factory";
import { OrganizationFactory } from "@test/factories/organization_factory";
import { StableFactory } from "@test/factories/stable_factory";
import { TransportDailyRecordFactory } from "@test/factories/stable_tm_horse_transport_daily_record_factory";
import { StableTmTransportRecordFactory } from "@test/factories/stable_tm_horse_transport_record_factory";
import { SectionFactory } from "@test/factories/stable_tm_section_factory";
import { UserFactory } from "@test/factories/user_factory";
import { UserRoleFactory } from "@test/factories/user_role_factory";
import { getFirebaseIDToken } from "@test/util";
import { stringify } from "uuid";
import { describe, expect, it, vi } from "vitest";
import { createHorseTransportRecord } from "@/repositories/stable_tm_transport_record_repository";

describe("listTransportHorses", () => {
  it("in_stable=trueを指定した場合、在厩中の馬のみが返されること", async ({
    getClient,
  }) => {
    const fixedTime = new Date("2025-01-03T00:00:00Z");
    vi.setSystemTime(fixedTime);

    const user = await UserFactory.create();
    const organization = await OrganizationFactory.create();
    const stable = await StableFactory.create({
      organization: {
        connect: { organizationUuid: organization.organizationUuid },
      },
    });
    await UserRoleFactory.create({
      user: { connect: { userUuid: user.userUuid } },
      organization: {
        connect: { organizationUuid: organization.organizationUuid },
      },
    });

    const inStableHorse = await HorseFactory.create({
      stable: { connect: { stableUuid: stable.stableUuid } },
    });

    await HorseStatusFactory.create({
      horse: { connect: { horseId: inStableHorse.horseId } },
      inStable: true,
      latestStableInDate: new Date("2025-01-01T00:00:00Z"),
    });

    const outStableHorse = await HorseFactory.create({
      stable: { connect: { stableUuid: stable.stableUuid } },
    });

    await HorseStatusFactory.create({
      horse: { connect: { horseId: outStableHorse.horseId } },
      inStable: false,
      latestStableOutDate: new Date("2025-01-01T00:00:00Z"),
    });

    const idToken = await getFirebaseIDToken(user.firebaseUid);

    const client = getClient(TransportHorseService);
    const headers = new Headers({
      authorization: `Bearer ${idToken}`,
    });

    const response = await client.listTransportHorses(
      create(ListTransportHorsesRequestSchema, {
        stableUuid: stringify(stable.stableUuid),
        inStable: true,
      }),
      {
        headers,
      },
    );

    expect(response.transportHorses).toHaveLength(1);
    // 入厩中の馬のみが返されることを確認
    expect(response.transportHorses).toEqual(
      expect.arrayContaining([
        expect.objectContaining({
          inStable: true,
          inStatus: expect.objectContaining({
            stableInDate: "2025/01/01",
            inStableDuration: 2,
          }),
        }),
      ]),
    );
  });

  it("in_stable=falseを指定した場合、退厩中の馬のみが返されること", async ({
    getClient,
  }) => {
    const fixedTime = new Date("2025-01-03T00:00:00Z");
    vi.setSystemTime(fixedTime);

    const user = await UserFactory.create();
    const organization = await OrganizationFactory.create();
    const stable = await StableFactory.create({
      organization: {
        connect: { organizationUuid: organization.organizationUuid },
      },
    });
    await UserRoleFactory.create({
      user: { connect: { userUuid: user.userUuid } },
      organization: {
        connect: { organizationUuid: organization.organizationUuid },
      },
    });

    const inStableHorse = await HorseFactory.create({
      stable: { connect: { stableUuid: stable.stableUuid } },
    });

    await HorseStatusFactory.create({
      horse: { connect: { horseId: inStableHorse.horseId } },
      inStable: true,
      latestStableInDate: new Date("2025-01-01T00:00:00Z"),
    });

    const outStableHorse = await HorseFactory.create({
      stable: { connect: { stableUuid: stable.stableUuid } },
    });

    await HorseStatusFactory.create({
      horse: { connect: { horseId: outStableHorse.horseId } },
      inStable: false,
      latestStableOutDate: new Date("2025-01-01T00:00:00Z"),
    });

    const idToken = await getFirebaseIDToken(user.firebaseUid);
    const client = getClient(TransportHorseService);
    const headers = new Headers({
      authorization: `Bearer ${idToken}`,
    });

    const response = await client.listTransportHorses(
      create(ListTransportHorsesRequestSchema, {
        stableUuid: stringify(stable.stableUuid),
        inStable: false,
      }),
      {
        headers,
      },
    );

    expect(response.transportHorses).toHaveLength(1);
    // 退厩中の馬のみが返されることを確認
    expect(response.transportHorses).toEqual(
      expect.arrayContaining([
        expect.objectContaining({
          inStable: false,
          outStatus: expect.objectContaining({
            stableOutDate: "2025/01/01",
            outStableDuration: 2,
          }),
        }),
      ]),
    );
  });

  it("条件に一致する馬が存在しない場合、空配列が返されること", async ({
    getClient,
  }) => {
    const fixedTime = new Date("2025-01-01T00:00:00Z");
    vi.setSystemTime(fixedTime);

    const user = await UserFactory.create();
    const organization = await OrganizationFactory.create();
    const stable = await StableFactory.create({
      organization: {
        connect: { organizationUuid: organization.organizationUuid },
      },
    });
    await UserRoleFactory.create({
      user: { connect: { userUuid: user.userUuid } },
    });

    const inStableHorse = await HorseFactory.create({
      stable: { connect: { stableUuid: stable.stableUuid } },
    });

    await HorseStatusFactory.create({
      horse: { connect: { horseId: inStableHorse.horseId } },
      inStable: true,
      latestStableInDate: new Date("2025-01-01T00:00:00Z"),
    });

    const outStableHorse = await HorseFactory.create({
      stable: { connect: { stableUuid: stable.stableUuid } },
    });

    await HorseStatusFactory.create({
      horse: { connect: { horseId: outStableHorse.horseId } },
      inStable: false,
      latestStableOutDate: new Date("2025-01-01T00:00:00Z"),
    });

    const idToken = await getFirebaseIDToken(user.firebaseUid);

    const client = getClient(TransportHorseService);
    const headers = new Headers({
      authorization: `Bearer ${idToken}`,
    });

    const response = await client.listTransportHorses(
      create(ListTransportHorsesRequestSchema, {
        stableUuid: stringify(stable.stableUuid),
        inStable: false,
      }),
      {
        headers,
      },
    );

    expect(response.transportHorses).toBeDefined();
    expect(Array.isArray(response.transportHorses)).toBe(true);
  });

  it("manageStatus=managedの馬のみが返されること", async ({ getClient }) => {
    const fixedTime = new Date("2025-01-03T00:00:00Z");
    vi.setSystemTime(fixedTime);

    const user = await UserFactory.create();
    const organization = await OrganizationFactory.create();
    const stable = await StableFactory.create({
      organization: {
        connect: { organizationUuid: organization.organizationUuid },
      },
    });
    await UserRoleFactory.create({
      user: { connect: { userUuid: user.userUuid } },
      organization: {
        connect: { organizationUuid: organization.organizationUuid },
      },
    });

    // 管理対象の馬
    const managedHorse = await HorseFactory.create({
      stable: { connect: { stableUuid: stable.stableUuid } },
      manageStatus: "managed",
    });

    await HorseStatusFactory.create({
      horse: { connect: { horseId: managedHorse.horseId } },
      inStable: true,
    });

    // 管理対象外の馬
    const unmanagedHorse = await HorseFactory.create({
      stable: { connect: { stableUuid: stable.stableUuid } },
      manageStatus: "unmanaged",
    });

    await HorseStatusFactory.create({
      horse: { connect: { horseId: unmanagedHorse.horseId } },
      inStable: true,
    });

    const idToken = await getFirebaseIDToken(user.firebaseUid);
    const client = getClient(TransportHorseService);
    const headers = new Headers({
      authorization: `Bearer ${idToken}`,
    });

    const response = await client.listTransportHorses(
      create(ListTransportHorsesRequestSchema, {
        stableUuid: stringify(stable.stableUuid),
        inStable: true,
      }),
      {
        headers,
      },
    );

    // 管理対象の馬のみが返されることを確認
    expect(response.transportHorses).toHaveLength(1);
    expect(response.transportHorses[0].horseId).toBe(managedHorse.horseId);
  });

  it("horseStatusが未設定の馬も含まれること", async ({ getClient }) => {
    const fixedTime = new Date("2025-01-03T00:00:00Z");
    vi.setSystemTime(fixedTime);

    const user = await UserFactory.create();
    const organization = await OrganizationFactory.create();
    const stable = await StableFactory.create({
      organization: {
        connect: { organizationUuid: organization.organizationUuid },
      },
    });
    await UserRoleFactory.create({
      user: { connect: { userUuid: user.userUuid } },
      organization: {
        connect: { organizationUuid: organization.organizationUuid },
      },
    });

    // horseStatusが未設定の馬
    const horseWithoutStatus = await HorseFactory.create({
      stable: { connect: { stableUuid: stable.stableUuid } },
      manageStatus: "managed",
    });

    const idToken = await getFirebaseIDToken(user.firebaseUid);
    const client = getClient(TransportHorseService);
    const headers = new Headers({
      authorization: `Bearer ${idToken}`,
    });

    const response = await client.listTransportHorses(
      create(ListTransportHorsesRequestSchema, {
        stableUuid: stringify(stable.stableUuid),
        inStable: false,
      }),
      {
        headers,
      },
    );

    // horseStatus未設定の馬も含まれることを確認
    expect(response.transportHorses).toHaveLength(1);
    expect(response.transportHorses[0].horseId).toBe(
      horseWithoutStatus.horseId,
    );
  });

  it("最新のtransportRecordを考慮した馬の選択が行われること", async ({
    getClient,
  }) => {
    const fixedTime = new Date("2025-01-03T00:00:00Z");
    vi.setSystemTime(fixedTime);

    const user = await UserFactory.create();
    const organization = await OrganizationFactory.create();
    const stable = await StableFactory.create({
      organization: {
        connect: { organizationUuid: organization.organizationUuid },
      },
    });
    await UserRoleFactory.create({
      user: { connect: { userUuid: user.userUuid } },
      organization: {
        connect: { organizationUuid: organization.organizationUuid },
      },
    });

    // 在厩中だが最新のtransportRecordがinの馬
    const horse = await HorseFactory.create({
      stable: { connect: { stableUuid: stable.stableUuid } },
      manageStatus: "managed",
    });

    await HorseStatusFactory.create({
      horse: { connect: { horseId: horse.horseId } },
      inStable: true,
    });

    const section = await SectionFactory.create({
      stable: { connect: { stableUuid: stable.stableUuid } },
      startDate: new Date("2025-01-01"),
      endDate: new Date("2025-01-07"),
    });

    // 最新のtransportRecordを作成（type: "in"）
    const transportDailyRecord = await TransportDailyRecordFactory.create({
      section: { connect: { sectionId: section.sectionId } },
      year: 2025,
      month: 1,
      day: 2,
    });

    await StableTmTransportRecordFactory.create({
      horse: { connect: { horseId: horse.horseId } },
      transportDailyRecord: {
        connect: {
          transportDailyRecordId: transportDailyRecord.transportDailyRecordId,
        },
      },
      type: "in",
    });

    const idToken = await getFirebaseIDToken(user.firebaseUid);
    const client = getClient(TransportHorseService);
    const headers = new Headers({
      authorization: `Bearer ${idToken}`,
    });

    const response = await client.listTransportHorses(
      create(ListTransportHorsesRequestSchema, {
        stableUuid: stringify(stable.stableUuid),
        inStable: true, // 入厩馬として選択
      }),
      {
        headers,
      },
    );

    // 在厩中だが最新recordがoutの馬が入厩馬として選択可能であることを確認
    expect(response.transportHorses).toHaveLength(1);
    expect(response.transportHorses[0].horseId).toBe(horse.horseId);
  });

  it("sectionIdを指定してsection期間内のレコードで制限されること", async ({
    getClient,
  }) => {
    const fixedTime = new Date("2025-01-10T00:00:00Z");
    vi.setSystemTime(fixedTime);

    const user = await UserFactory.create();
    const organization = await OrganizationFactory.create();
    const stable = await StableFactory.create({
      organization: {
        connect: { organizationUuid: organization.organizationUuid },
      },
    });
    await UserRoleFactory.create({
      user: { connect: { userUuid: user.userUuid } },
      organization: {
        connect: { organizationUuid: organization.organizationUuid },
      },
    });

    const horse = await HorseFactory.create({
      stable: { connect: { stableUuid: stable.stableUuid } },
      manageStatus: "managed",
    });

    await HorseStatusFactory.create({
      horse: { connect: { horseId: horse.horseId } },
      inStable: false,
    });

    // section作成（1月1日〜1月7日）
    const section = await SectionFactory.create({
      stable: { connect: { stableUuid: stable.stableUuid } },
      startDate: new Date("2025-01-01"),
      endDate: new Date("2025-01-07"),
    });

    // section期間内のtransportRecord
    const transportDailyRecordInSection =
      await TransportDailyRecordFactory.create({
        section: { connect: { sectionId: section.sectionId } },
        year: 2025,
        month: 1,
        day: 3,
      });

    await StableTmTransportRecordFactory.create({
      horse: { connect: { horseId: horse.horseId } },
      transportDailyRecord: {
        connect: {
          transportDailyRecordId:
            transportDailyRecordInSection.transportDailyRecordId,
        },
      },
      type: "in",
    });

    // section期間外用の別のsectionを作成
    const sectionOut = await SectionFactory.create({
      stable: { connect: { stableUuid: stable.stableUuid } },
      startDate: new Date("2025-01-08"),
      endDate: new Date("2025-01-14"),
    });

    // section期間外のtransportRecord（1月8日、section終了後）
    const transportDailyRecordOutSection =
      await TransportDailyRecordFactory.create({
        section: { connect: { sectionId: sectionOut.sectionId } },
        year: 2025,
        month: 1,
        day: 8,
      });

    await StableTmTransportRecordFactory.create({
      horse: { connect: { horseId: horse.horseId } },
      transportDailyRecord: {
        connect: {
          transportDailyRecordId:
            transportDailyRecordOutSection.transportDailyRecordId,
        },
      },
      type: "out",
    });

    const idToken = await getFirebaseIDToken(user.firebaseUid);
    const client = getClient(TransportHorseService);
    const headers = new Headers({
      authorization: `Bearer ${idToken}`,
    });

    const response = await client.listTransportHorses(
      create(ListTransportHorsesRequestSchema, {
        stableUuid: stringify(stable.stableUuid),
        inStable: true,
        sectionId: stringify(section.sectionId), // sectionIdを指定
      }),
      {
        headers,
      },
    );

    // section期間内の最新record（type: "out"）が考慮され、入厩馬として選択可能であることを確認
    expect(response.transportHorses).toHaveLength(1);
    expect(response.transportHorses[0].horseId).toBe(horse.horseId);
  });

  it("すでに退厩候補馬として選択されている馬は入厩候補馬から除外されること", async ({
    getClient,
  }) => {
    const fixedTime = new Date("2025-01-03T00:00:00Z");
    vi.setSystemTime(fixedTime);

    const user = await UserFactory.create();
    const organization = await OrganizationFactory.create();
    const stable = await StableFactory.create({
      organization: {
        connect: { organizationUuid: organization.organizationUuid },
      },
    });
    await UserRoleFactory.create({
      user: { connect: { userUuid: user.userUuid } },
      organization: {
        connect: { organizationUuid: organization.organizationUuid },
      },
    });

    // 在厩中の馬を作成
    const horse1 = await HorseFactory.create({
      stable: { connect: { stableUuid: stable.stableUuid } },
      manageStatus: "managed",
    });

    await HorseStatusFactory.create({
      horse: { connect: { horseId: horse1.horseId } },
      inStable: true,
    });

    // 通常の在厩中の馬（除外されない）
    const horse2 = await HorseFactory.create({
      stable: { connect: { stableUuid: stable.stableUuid } },
      manageStatus: "managed",
    });

    await HorseStatusFactory.create({
      horse: { connect: { horseId: horse2.horseId } },
      inStable: true,
    });

    // horse1をすでに退厩候補馬として選択（transportDailyRecordがnullのoutレコード）
    await createHorseTransportRecord({
      horseId: horse1.horseId,
      type: "out",
    });

    const idToken = await getFirebaseIDToken(user.firebaseUid);
    const client = getClient(TransportHorseService);
    const headers = new Headers({
      authorization: `Bearer ${idToken}`,
    });

    const response = await client.listTransportHorses(
      create(ListTransportHorsesRequestSchema, {
        stableUuid: stringify(stable.stableUuid),
        inStable: true, // 入厩候補馬を取得
      }),
      {
        headers,
      },
    );

    // すでに退厩候補馬として選択されている馬は除外され、通常の馬のみが返される
    expect(response.transportHorses).toHaveLength(1);
    expect(response.transportHorses[0].horseId).toBe(horse2.horseId);
  });

  it("すでに入厩候補馬として選択されている馬は退厩候補馬から除外されること", async ({
    getClient,
  }) => {
    const fixedTime = new Date("2025-01-03T00:00:00Z");
    vi.setSystemTime(fixedTime);

    const user = await UserFactory.create();
    const organization = await OrganizationFactory.create();
    const stable = await StableFactory.create({
      organization: {
        connect: { organizationUuid: organization.organizationUuid },
      },
    });
    await UserRoleFactory.create({
      user: { connect: { userUuid: user.userUuid } },
      organization: {
        connect: { organizationUuid: organization.organizationUuid },
      },
    });

    // 退厩中の馬を作成
    const horse1 = await HorseFactory.create({
      stable: { connect: { stableUuid: stable.stableUuid } },
      manageStatus: "managed",
    });

    await HorseStatusFactory.create({
      horse: { connect: { horseId: horse1.horseId } },
      inStable: false,
    });

    // 通常の退厩中の馬（除外されない）
    const horse2 = await HorseFactory.create({
      stable: { connect: { stableUuid: stable.stableUuid } },
      manageStatus: "managed",
    });

    await HorseStatusFactory.create({
      horse: { connect: { horseId: horse2.horseId } },
      inStable: false,
    });

    // horse1をすでに入厩候補馬として選択（transportDailyRecordがnullのinレコード）
    await createHorseTransportRecord({
      horseId: horse1.horseId,
      type: "in",
    });

    const idToken = await getFirebaseIDToken(user.firebaseUid);
    const client = getClient(TransportHorseService);
    const headers = new Headers({
      authorization: `Bearer ${idToken}`,
    });

    const response = await client.listTransportHorses(
      create(ListTransportHorsesRequestSchema, {
        stableUuid: stringify(stable.stableUuid),
        inStable: false, // 退厩候補馬を取得
      }),
      {
        headers,
      },
    );

    // すでに入厩候補馬として選択されている馬は除外され、通常の馬のみが返される
    expect(response.transportHorses).toHaveLength(1);
    expect(response.transportHorses[0].horseId).toBe(horse2.horseId);
  });
});
