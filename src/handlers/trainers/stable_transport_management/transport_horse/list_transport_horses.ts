import { create } from "@bufbuild/protobuf";
import { requestContext } from "@fastify/request-context";
import {
  type ListTransportHorsesRequest,
  type ListTransportHorsesResponse,
  ListTransportHorsesResponseSchema,
} from "@schema/gen/trainers/stable_transport_management/transport_horse_service_pb";
import { parse } from "uuid";
import { toResponses } from "@/handlers/mapper/trainers/stable_transport_management/transport_horse";
import { fetchHorses, isInStable } from "@/repositories/horse_repository";
import { fetchSection } from "@/repositories/stable_tm_section_repository";
import {
  fetchLatestHorseTransportRecords,
  fetchLatestHorseTransportRecordsWithinPeriod,
  fetchTransportRecordsWithoutDailyRecord,
  type HorseTransportRecord,
} from "@/repositories/stable_tm_transport_record_repository";

export const listTransportHorses = async (
  req: ListTransportHorsesRequest,
): Promise<ListTransportHorsesResponse> => {
  const requestedAt = requestContext.get("requestedAt")!;

  const stableUuid = parse(req.stableUuid);
  const allHorses = await fetchHorses({
    stableUuids: [stableUuid],
    includeUnlinkedMasterHorse: true,
  });
  const managedHorses = allHorses.filter(
    (horse) => horse.manageStatus === "managed",
  );

  let latestRecords: HorseTransportRecord[];
  if (req.sectionId) {
    const section = await fetchSection({ sectionId: parse(req.sectionId) });

    latestRecords = await fetchLatestHorseTransportRecordsWithinPeriod({
      horseIds: managedHorses.map((horse) => horse.horseId),
      endDate: section.endDate,
    });
  } else {
    latestRecords = await fetchLatestHorseTransportRecords({
      horseIds: managedHorses.map((horse) => horse.horseId),
    });
  }

  const latestRecordMap = new Map(
    latestRecords.map((record) => [record.horseId, record]),
  );

  const withoutDailyRecordHorseTransportRecords =
    await fetchTransportRecordsWithoutDailyRecord({
      stableUuid,
    });

  const withoutDailyRecordHorseTransportRecordMap = new Map(
    withoutDailyRecordHorseTransportRecords.map((record) => [
      record.horseId,
      record,
    ]),
  );

  const filteredHorses = managedHorses.filter((horse) => {
    const horseInStable = isInStable(horse);
    const latestRecord = latestRecordMap.get(horse.horseId);
    const withoutDailyRecordHorseTransportRecord =
      withoutDailyRecordHorseTransportRecordMap.get(horse.horseId);

    if (req.inStable) {
      // すでに退厩候補馬として選択されている馬は除外
      if (withoutDailyRecordHorseTransportRecord?.type === "out") {
        return false;
      }
      // 最新のhorseTransportRecordがoutの退厩予定がある馬は除外
      if (latestRecord?.type === "out") {
        return false;
      }
      // 現在在厩中の馬か最新のhorseTransportRecordがinの馬は候補に含める（isInStable = true）
      return horseInStable || latestRecord?.type === "in";
    } else {
      // すでに入厩候補馬として選択されている馬は除外
      if (withoutDailyRecordHorseTransportRecord?.type === "in") {
        return false;
      }
      // 最新のhorseTransportRecordがinの入厩予定がある馬は除外
      if (latestRecord?.type === "in") {
        return false;
      }
      // 現在退厩中の馬か最新のhorseTransportRecordがoutの馬は候補に含める（isInStable = false）
      return !horseInStable || latestRecord?.type === "out";
    }
  });

  const transportHorses = toResponses({
    horses: filteredHorses,
    now: requestedAt,
  });

  return create(ListTransportHorsesResponseSchema, {
    transportHorses,
  });
};
