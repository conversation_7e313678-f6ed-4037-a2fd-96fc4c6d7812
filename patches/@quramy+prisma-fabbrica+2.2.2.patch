diff --git a/node_modules/@quramy/prisma-fabbrica/lib/templates/index.js b/node_modules/@quramy/prisma-fabbrica/lib/templates/index.js
index f7bbcff..52ebed6 100644
--- a/node_modules/@quramy/prisma-fabbrica/lib/templates/index.js
+++ b/node_modules/@quramy/prisma-fabbrica/lib/templates/index.js
@@ -1,75 +1,119 @@
-"use strict";
-var __importDefault = (this && this.__importDefault) || function (mod) {
-    return (mod && mod.__esModule) ? mod : { "default": mod };
-};
-Object.defineProperty(exports, "__esModule", { value: true });
-exports.assignWithTransientFields = exports.defineModelFactory = exports.modelFactoryBuilder = exports.defineModelFactoryInternal = exports.autoGenerateModelScalarsOrEnums = exports.autoGenerateModelScalarsOrEnumsFieldArgs = exports.isModelAssociationFactory = exports.modelFactoryInterface = exports.modelFactoryInterfaceWithoutTraits = exports.modelTraitKeys = exports.modelFactoryDefineOptions = exports.modelFactoryTrait = exports.modelTransientFields = exports.modelFactoryDefineInput = exports.modelBelongsToRelationFactory = exports.modelScalarOrEnumFields = exports.argInputType = exports.scalarFieldType = exports.modelFieldDefinitions = exports.genericDeclarations = exports.importStatement = exports.header = void 0;
-exports.findPrismaCreateInputTypeFromModelName = findPrismaCreateInputTypeFromModelName;
+'use strict';
+var __importDefault =
+  (this && this.__importDefault) ||
+  function (mod) {
+    return mod && mod.__esModule ? mod : { default: mod };
+  };
+Object.defineProperty(exports, '__esModule', { value: true });
+exports.assignWithTransientFields =
+  exports.defineModelFactory =
+  exports.modelFactoryBuilder =
+  exports.defineModelFactoryInternal =
+  exports.autoGenerateModelScalarsOrEnums =
+  exports.autoGenerateModelScalarsOrEnumsFieldArgs =
+  exports.isModelAssociationFactory =
+  exports.modelFactoryInterface =
+  exports.modelFactoryInterfaceWithoutTraits =
+  exports.modelTraitKeys =
+  exports.modelFactoryDefineOptions =
+  exports.modelFactoryTrait =
+  exports.modelTransientFields =
+  exports.modelFactoryDefineInput =
+  exports.modelBelongsToRelationFactory =
+  exports.modelScalarOrEnumFields =
+  exports.argInputType =
+  exports.scalarFieldType =
+  exports.modelFieldDefinitions =
+  exports.genericDeclarations =
+  exports.importStatement =
+  exports.header =
+    void 0;
+exports.findPrismaCreateInputTypeFromModelName =
+  findPrismaCreateInputTypeFromModelName;
 exports.getIdFieldNames = getIdFieldNames;
 exports.getSourceFile = getSourceFile;
-const typescript_1 = __importDefault(require("typescript"));
-const talt_1 = require("talt");
-const helpers_1 = require("../helpers");
-const relations_1 = require("../relations");
-const astShorthand_1 = require("./ast-tools/astShorthand");
-const createJSONLiteral_1 = require("./ast-tools/createJSONLiteral");
-const comment_1 = require("./ast-tools/comment");
+const typescript_1 = __importDefault(require('typescript'));
+const talt_1 = require('talt');
+const helpers_1 = require('../helpers');
+const relations_1 = require('../relations');
+const astShorthand_1 = require('./ast-tools/astShorthand');
+const createJSONLiteral_1 = require('./ast-tools/createJSONLiteral');
+const comment_1 = require('./ast-tools/comment');
 function findPrismaCreateInputTypeFromModelName(document, modelName) {
-    const search = `${modelName}CreateInput`;
-    const inputType = document.schema.inputObjectTypes.prisma.find(x => x.name === search);
-    // When model has field annotated with Unsupported type, Prisma omits to output ModelCreateInput / ModelUpdateInput to DMMF.
-    if (!inputType)
-        return null;
-    return inputType;
+  const search = `${modelName}CreateInput`;
+  const inputType = document.schema.inputObjectTypes.prisma.find(
+    (x) => x.name === search
+  );
+  // When model has field annotated with Unsupported type, Prisma omits to output ModelCreateInput / ModelUpdateInput to DMMF.
+  if (!inputType) return null;
+  return inputType;
 }
 function getIdFieldNames(model) {
-    if (model.primaryKey) {
-        return model.primaryKey.fields;
-    }
-    const idLike = model.fields.find(f => f.isId || f.isUnique);
-    if (idLike) {
-        return [idLike.name];
-    }
-    if (model.uniqueFields.length) {
-        return model.uniqueFields[0];
-    }
-    throw new Error(`Model ${model.name} does not have @id nor @@id nor @@unique.`);
+  if (model.primaryKey) {
+    return model.primaryKey.fields;
+  }
+  const idLike = model.fields.find((f) => f.isId || f.isUnique);
+  if (idLike) {
+    return [idLike.name];
+  }
+  if (model.uniqueFields.length) {
+    return model.uniqueFields[0];
+  }
+  throw new Error(
+    `Model ${model.name} does not have @id nor @@id nor @@unique.`
+  );
 }
 function filterRequiredFields(inputType) {
-    return inputType.fields.filter(field => field.isRequired);
+  return inputType.fields.filter((field) => field.isRequired);
 }
 function isScalarOrEnumField(field) {
-    return field.inputTypes.every(cit => cit.location === "enumTypes" || cit.location === "scalar");
+  return field.inputTypes.every(
+    (cit) => cit.location === 'enumTypes' || cit.location === 'scalar'
+  );
 }
 function isInputObjectTypeField(field) {
-    return field.inputTypes.length === 1 && field.inputTypes.every(cit => cit.location === "inputObjectTypes");
+  return (
+    field.inputTypes.length === 1 &&
+    field.inputTypes.every((cit) => cit.location === 'inputObjectTypes')
+  );
 }
 function filterRequiredScalarOrEnumFields(inputType) {
-    return filterRequiredFields(inputType)
-        .filter(inputType => !inputType.isNullable)
-        .filter(isScalarOrEnumField);
+  return filterRequiredFields(inputType)
+    .filter((inputType) => !inputType.isNullable)
+    .filter(isScalarOrEnumField);
 }
 function filterRequiredInputObjectTypeField(inputType) {
-    return filterRequiredFields(inputType).filter(isInputObjectTypeField);
+  return filterRequiredFields(inputType).filter(isInputObjectTypeField);
 }
 function filterBelongsToField(model, inputType) {
-    return inputType.fields
-        .filter(isInputObjectTypeField)
-        .filter(field => model.fields.find((0, helpers_1.byName)(field))?.isList === false);
+  return inputType.fields
+    .filter(isInputObjectTypeField)
+    .filter(
+      (field) =>
+        model.fields.find((0, helpers_1.byName)(field))?.isList === false
+    );
 }
 function filterEnumFields(inputType) {
-    return inputType.fields.filter(field => field.inputTypes.length > 0 && field.inputTypes.some(childInputType => childInputType.location === "enumTypes"));
+  return inputType.fields.filter(
+    (field) =>
+      field.inputTypes.length > 0 &&
+      field.inputTypes.some(
+        (childInputType) => childInputType.location === 'enumTypes'
+      )
+  );
 }
 function extractFirstEnumValue(enums, field) {
-    const typeName = field.inputTypes[0].type;
-    const found = enums.find((0, helpers_1.byName)(typeName));
-    if (!found) {
-        throw new Error(`Not found enum ${typeName}`);
-    }
-    return found.values[0];
+  const typeName = field.inputTypes[0].type;
+  const found = enums.find((0, helpers_1.byName)(typeName));
+  if (!found) {
+    throw new Error(`Not found enum ${typeName}`);
+  }
+  return found.values[0];
 }
-const header = (prismaClientModuleSpecifier) => talt_1.template.sourceFile `
-    import type { Prisma, PrismaClient } from ${() => astShorthand_1.ast.stringLiteral(prismaClientModuleSpecifier)};
+const header = (prismaClientModuleSpecifier) =>
+  talt_1.template.sourceFile`
+    import type { Prisma, PrismaClient } from ${() =>
+      astShorthand_1.ast.stringLiteral(prismaClientModuleSpecifier)};
     import {
       createInitializer,
       createScreener,
@@ -87,11 +131,15 @@ const header = (prismaClientModuleSpecifier) => talt_1.template.sourceFile `
     export { resetSequence, registerScalarFieldValueGenerator, resetScalarFieldValueGenerator } from "@quramy/prisma-fabbrica/lib/internal";
   `();
 exports.header = header;
-const importStatement = (specifier, prismaClientModuleSpecifier) => talt_1.template.statement `
-    import type { ${() => astShorthand_1.ast.identifier(specifier)} } from ${() => astShorthand_1.ast.stringLiteral(prismaClientModuleSpecifier)};
+const importStatement = (specifier, prismaClientModuleSpecifier) =>
+  talt_1.template.statement`
+    import type { ${() =>
+      astShorthand_1.ast.identifier(specifier)} } from ${() =>
+    astShorthand_1.ast.stringLiteral(prismaClientModuleSpecifier)};
   `();
 exports.importStatement = importStatement;
-const genericDeclarations = () => talt_1.template.sourceFile `
+const genericDeclarations = () =>
+  talt_1.template.sourceFile`
     type BuildDataOptions<TTransients extends Record<string, unknown>> = {
       readonly seq: number;
     } & TTransients;
@@ -109,111 +157,199 @@ const genericDeclarations = () => talt_1.template.sourceFile `
     export const { initialize } = initializer;
   `();
 exports.genericDeclarations = genericDeclarations;
-const modelFieldDefinitions = (models) => talt_1.template.statement `
-    const modelFieldDefinitions: ModelWithFields[] = ${() => (0, createJSONLiteral_1.createJSONLiteral)((0, relations_1.createFieldDefinitions)(models))};
+const modelFieldDefinitions = (models) =>
+  talt_1.template.statement`
+    const modelFieldDefinitions: ModelWithFields[] = ${() =>
+      (0, createJSONLiteral_1.createJSONLiteral)(
+        (0, relations_1.createFieldDefinitions)(models)
+      )};
   `();
 exports.modelFieldDefinitions = modelFieldDefinitions;
 const scalarFieldType = (model, fieldName, inputType) => {
-    if (inputType.location !== "scalar") {
-        throw new Error("Invalid call. This function is allowed for only scalar field.");
-    }
-    switch (inputType.type) {
-        case "Null":
-            return astShorthand_1.ast.literalTypeNode(astShorthand_1.ast.null());
-        case "Boolean":
-            return astShorthand_1.ast.keywordTypeNode(typescript_1.default.SyntaxKind.BooleanKeyword);
-        case "String":
-            return astShorthand_1.ast.keywordTypeNode(typescript_1.default.SyntaxKind.StringKeyword);
-        case "Int":
-        case "Float":
-            return astShorthand_1.ast.keywordTypeNode(typescript_1.default.SyntaxKind.NumberKeyword);
-        case "BigInt":
-            return talt_1.template.typeNode `bigint | number`();
-        case "Decimal":
-            return talt_1.template.typeNode `Prisma.Decimal | Prisma.DecimalJsLike | string`();
-        case "DateTime":
-            return talt_1.template.typeNode `Date`();
-        case "Bytes":
-            return talt_1.template.typeNode `Buffer`();
-        case "Json":
-            return talt_1.template.typeNode `Prisma.InputJsonValue`();
-        default:
-            throw new Error(`Unknown scalar type "${inputType.type}" for ${model.name}.${fieldName} .`);
-    }
+  if (inputType.location !== 'scalar') {
+    throw new Error(
+      'Invalid call. This function is allowed for only scalar field.'
+    );
+  }
+  switch (inputType.type) {
+    case 'Null':
+      return astShorthand_1.ast.literalTypeNode(astShorthand_1.ast.null());
+    case 'Boolean':
+      return astShorthand_1.ast.keywordTypeNode(
+        typescript_1.default.SyntaxKind.BooleanKeyword
+      );
+    case 'String':
+      return astShorthand_1.ast.keywordTypeNode(
+        typescript_1.default.SyntaxKind.StringKeyword
+      );
+    case 'Int':
+    case 'Float':
+      return astShorthand_1.ast.keywordTypeNode(
+        typescript_1.default.SyntaxKind.NumberKeyword
+      );
+    case 'BigInt':
+      return talt_1.template.typeNode`bigint | number`();
+    case 'Decimal':
+      return talt_1.template
+        .typeNode`Prisma.Decimal | Prisma.DecimalJsLike | string`();
+    case 'DateTime':
+      return talt_1.template.typeNode`Date`();
+    case 'Bytes':
+      return talt_1.template.typeNode`Buffer`();
+    case 'Json':
+      return talt_1.template.typeNode`Prisma.InputJsonValue`();
+    default:
+      throw new Error(
+        `Unknown scalar type "${inputType.type}" for ${model.name}.${fieldName} .`
+      );
+  }
 };
 exports.scalarFieldType = scalarFieldType;
 const argInputType = (model, fieldName, inputType) => {
-    const fieldType = () => {
-        if (inputType.location === "scalar") {
-            return (0, exports.scalarFieldType)(model, fieldName, inputType);
-        }
-        else if (inputType.location === "enumTypes") {
-            return inputType.namespace === "model"
-                ? astShorthand_1.ast.typeReferenceNode(astShorthand_1.ast.identifier(inputType.type))
-                : talt_1.template.typeNode `Prisma.${() => astShorthand_1.ast.identifier(inputType.type)}`();
-        }
-        else if (inputType.location === "outputObjectTypes" || inputType.location === "inputObjectTypes") {
-            return astShorthand_1.ast.typeReferenceNode(talt_1.template.expression `Prisma.${() => astShorthand_1.ast.identifier(inputType.type)}`());
-        }
-        else {
-            // FIXME inputType.location === "fieldRefTypes"
-            return astShorthand_1.ast.keywordTypeNode(typescript_1.default.SyntaxKind.UnknownKeyword);
-        }
-    };
-    return inputType.isList
-        ? astShorthand_1.ast.typeReferenceNode(talt_1.template.expression `Array<${fieldType}>`())
-        : fieldType();
+  const fieldType = () => {
+    if (inputType.location === 'scalar') {
+      return (0, exports.scalarFieldType)(model, fieldName, inputType);
+    } else if (inputType.location === 'enumTypes') {
+      return inputType.namespace === 'model'
+        ? astShorthand_1.ast.typeReferenceNode(
+            astShorthand_1.ast.identifier(inputType.type)
+          )
+        : talt_1.template.typeNode`Prisma.${() =>
+            astShorthand_1.ast.identifier(inputType.type)}`();
+    } else if (
+      inputType.location === 'outputObjectTypes' ||
+      inputType.location === 'inputObjectTypes'
+    ) {
+      return astShorthand_1.ast.typeReferenceNode(
+        talt_1.template.expression`Prisma.${() =>
+          astShorthand_1.ast.identifier(inputType.type)}`()
+      );
+    } else {
+      // FIXME inputType.location === "fieldRefTypes"
+      return astShorthand_1.ast.keywordTypeNode(
+        typescript_1.default.SyntaxKind.UnknownKeyword
+      );
+    }
+  };
+  return inputType.isList
+    ? astShorthand_1.ast.typeReferenceNode(
+        talt_1.template.expression`Array<${fieldType}>`()
+      )
+    : fieldType();
 };
 exports.argInputType = argInputType;
-const modelScalarOrEnumFields = (model, inputType) => talt_1.template.statement `
-    type MODEL_SCALAR_OR_ENUM_FIELDS = ${() => astShorthand_1.ast.typeLiteralNode(filterRequiredScalarOrEnumFields(inputType).map(field => astShorthand_1.ast.propertySignature(undefined, field.name, undefined, astShorthand_1.ast.unionTypeNode(field.inputTypes.map(childInputType => (0, exports.argInputType)(model, field.name, childInputType))))))}
+const modelScalarOrEnumFields = (model, inputType) =>
+  talt_1.template.statement`
+    type MODEL_SCALAR_OR_ENUM_FIELDS = ${() =>
+      astShorthand_1.ast.typeLiteralNode(
+        filterRequiredScalarOrEnumFields(inputType).map((field) =>
+          astShorthand_1.ast.propertySignature(
+            undefined,
+            field.name,
+            undefined,
+            astShorthand_1.ast.unionTypeNode(
+              field.inputTypes.map((childInputType) =>
+                (0, exports.argInputType)(model, field.name, childInputType)
+              )
+            )
+          )
+        )
+      )}
   `({
-    MODEL_SCALAR_OR_ENUM_FIELDS: astShorthand_1.ast.identifier(`${model.name}ScalarOrEnumFields`),
-});
+    MODEL_SCALAR_OR_ENUM_FIELDS: astShorthand_1.ast.identifier(
+      `${model.name}ScalarOrEnumFields`
+    ),
+  });
 exports.modelScalarOrEnumFields = modelScalarOrEnumFields;
 const modelBelongsToRelationFactory = (fieldType, model) => {
-    const targetModel = model.fields.find((0, helpers_1.byName)(fieldType));
-    return talt_1.template.statement `
-    type ${() => astShorthand_1.ast.identifier(`${model.name}${fieldType.name}Factory`)} = {
-      _factoryFor: ${() => astShorthand_1.ast.literalTypeNode(astShorthand_1.ast.stringLiteral(targetModel.type))};
-      build: () => PromiseLike<Prisma.${() => astShorthand_1.ast.identifier(fieldType.inputTypes[0].type)}["create"]>;
+  const targetModel = model.fields.find((0, helpers_1.byName)(fieldType));
+  return talt_1.template.statement`
+    type ${() =>
+      astShorthand_1.ast.identifier(
+        `${model.name}${fieldType.name}Factory`
+      )} = {
+      _factoryFor: ${() =>
+        astShorthand_1.ast.literalTypeNode(
+          astShorthand_1.ast.stringLiteral(targetModel.type)
+        )};
+      build: () => PromiseLike<Prisma.${() =>
+        astShorthand_1.ast.identifier(fieldType.inputTypes[0].type)}["create"]>;
     };
   `();
 };
 exports.modelBelongsToRelationFactory = modelBelongsToRelationFactory;
-const modelFactoryDefineInput = (model, inputType) => talt_1.template.statement `
-    type MODEL_FACTORY_DEFINE_INPUT = ${() => astShorthand_1.ast.typeLiteralNode(inputType.fields.map(field => astShorthand_1.ast.propertySignature(undefined, field.name, !field.isRequired || isScalarOrEnumField(field) ? astShorthand_1.ast.token(typescript_1.default.SyntaxKind.QuestionToken) : undefined, astShorthand_1.ast.unionTypeNode([
-    ...((field.isRequired || model.fields.find((0, helpers_1.byName)(field)).isList === false) &&
-        isInputObjectTypeField(field)
-        ? [astShorthand_1.ast.typeReferenceNode(astShorthand_1.ast.identifier(`${model.name}${field.name}Factory`))]
-        : []),
-    ...field.inputTypes.map(childInputType => (0, exports.argInputType)(model, field.name, childInputType)),
-]))))};
+const modelFactoryDefineInput = (model, inputType) =>
+  talt_1.template.statement`
+    type MODEL_FACTORY_DEFINE_INPUT = ${() =>
+      astShorthand_1.ast.typeLiteralNode(
+        inputType.fields.map((field) =>
+          astShorthand_1.ast.propertySignature(
+            undefined,
+            field.name,
+            !field.isRequired || isScalarOrEnumField(field)
+              ? astShorthand_1.ast.token(
+                  typescript_1.default.SyntaxKind.QuestionToken
+                )
+              : undefined,
+            astShorthand_1.ast.unionTypeNode([
+              ...((field.isRequired ||
+                model.fields.find((0, helpers_1.byName)(field)).isList ===
+                  false) &&
+              isInputObjectTypeField(field)
+                ? [
+                    astShorthand_1.ast.typeReferenceNode(
+                      astShorthand_1.ast.identifier(
+                        `${model.name}${field.name}Factory`
+                      )
+                    ),
+                  ]
+                : []),
+              ...field.inputTypes.map((childInputType) =>
+                (0, exports.argInputType)(model, field.name, childInputType)
+              ),
+            ])
+          )
+        )
+      )};
   `({
-    MODEL_FACTORY_DEFINE_INPUT: astShorthand_1.ast.identifier(`${model.name}FactoryDefineInput`),
-});
+    MODEL_FACTORY_DEFINE_INPUT: astShorthand_1.ast.identifier(
+      `${model.name}FactoryDefineInput`
+    ),
+  });
 exports.modelFactoryDefineInput = modelFactoryDefineInput;
-const modelTransientFields = (model) => talt_1.template.statement `
+const modelTransientFields = (model) =>
+  talt_1.template.statement`
     type MODEL_TRANSIENT_FIELDS = Record<string, unknown> & Partial<Record<keyof MODEL_FACTORY_DEFINE_INPUT, never>>;
   `({
-    MODEL_TRANSIENT_FIELDS: astShorthand_1.ast.identifier(`${model.name}TransientFields`),
-    MODEL_FACTORY_DEFINE_INPUT: astShorthand_1.ast.identifier(`${model.name}FactoryDefineInput`),
-});
+    MODEL_TRANSIENT_FIELDS: astShorthand_1.ast.identifier(
+      `${model.name}TransientFields`
+    ),
+    MODEL_FACTORY_DEFINE_INPUT: astShorthand_1.ast.identifier(
+      `${model.name}FactoryDefineInput`
+    ),
+  });
 exports.modelTransientFields = modelTransientFields;
-const modelFactoryTrait = (model) => talt_1.template.statement `
+const modelFactoryTrait = (model) =>
+  talt_1.template.statement`
     type MODEL_FACTORY_TRAIT<TTransients extends Record<string, unknown>> = {
       data?: Resolver<Partial<MODEL_FACTORY_DEFINE_INPUT>, BuildDataOptions<TTransients>>;
     } & CallbackDefineOptions<MODEL_TYPE, Prisma.MODEL_CREATE_INPUT, TTransients>;
   `({
     MODEL_TYPE: astShorthand_1.ast.identifier(model.name),
-    MODEL_CREATE_INPUT: astShorthand_1.ast.identifier(`${model.name}CreateInput`),
-    MODEL_FACTORY_DEFINE_INPUT: astShorthand_1.ast.identifier(`${model.name}FactoryDefineInput`),
-    MODEL_FACTORY_TRAIT: astShorthand_1.ast.identifier(`${model.name}FactoryTrait`),
-});
+    MODEL_CREATE_INPUT: astShorthand_1.ast.identifier(
+      `${model.name}CreateInput`
+    ),
+    MODEL_FACTORY_DEFINE_INPUT: astShorthand_1.ast.identifier(
+      `${model.name}FactoryDefineInput`
+    ),
+    MODEL_FACTORY_TRAIT: astShorthand_1.ast.identifier(
+      `${model.name}FactoryTrait`
+    ),
+  });
 exports.modelFactoryTrait = modelFactoryTrait;
 const modelFactoryDefineOptions = (model, isOpionalDefaultData) => {
-    const compiled = isOpionalDefaultData
-        ? talt_1.template.statement `
+  const compiled = isOpionalDefaultData
+    ? talt_1.template.statement`
         type MODEL_FACTORY_DEFINE_OPTIONS<TTransients extends Record<string, unknown> = Record<string, unknown>> = {
           defaultData?: Resolver<MODEL_FACTORY_DEFINE_INPUT, BuildDataOptions<TTransients>>;
           traits?: {
@@ -221,7 +357,7 @@ const modelFactoryDefineOptions = (model, isOpionalDefaultData) => {
           };
         } & CallbackDefineOptions<MODEL_TYPE, Prisma.MODEL_CREATE_INPUT, TTransients>;
       `
-        : talt_1.template.statement `
+    : talt_1.template.statement`
         type MODEL_FACTORY_DEFINE_OPTIONS<TTransients extends Record<string, unknown> = Record<string, unknown>> = {
           defaultData: Resolver<MODEL_FACTORY_DEFINE_INPUT, BuildDataOptions<TTransients>>;
           traits?: {
@@ -229,46 +365,75 @@ const modelFactoryDefineOptions = (model, isOpionalDefaultData) => {
           };
         } & CallbackDefineOptions<MODEL_TYPE, Prisma.MODEL_CREATE_INPUT, TTransients>;
       `;
-    return compiled({
-        MODEL_TYPE: astShorthand_1.ast.identifier(model.name),
-        MODEL_CREATE_INPUT: astShorthand_1.ast.identifier(`${model.name}CreateInput`),
-        MODEL_FACTORY_DEFINE_INPUT: astShorthand_1.ast.identifier(`${model.name}FactoryDefineInput`),
-        MODEL_FACTORY_TRAIT: astShorthand_1.ast.identifier(`${model.name}FactoryTrait`),
-        MODEL_FACTORY_DEFINE_OPTIONS: astShorthand_1.ast.identifier(`${model.name}FactoryDefineOptions`),
-    });
+  return compiled({
+    MODEL_TYPE: astShorthand_1.ast.identifier(model.name),
+    MODEL_CREATE_INPUT: astShorthand_1.ast.identifier(
+      `${model.name}CreateInput`
+    ),
+    MODEL_FACTORY_DEFINE_INPUT: astShorthand_1.ast.identifier(
+      `${model.name}FactoryDefineInput`
+    ),
+    MODEL_FACTORY_TRAIT: astShorthand_1.ast.identifier(
+      `${model.name}FactoryTrait`
+    ),
+    MODEL_FACTORY_DEFINE_OPTIONS: astShorthand_1.ast.identifier(
+      `${model.name}FactoryDefineOptions`
+    ),
+  });
 };
 exports.modelFactoryDefineOptions = modelFactoryDefineOptions;
-const modelTraitKeys = (model) => talt_1.template.statement `
+const modelTraitKeys = (model) =>
+  talt_1.template.statement`
     type MODEL_TRAIT_KEYS<TOptions extends MODEL_FACTORY_DEFINE_OPTIONS<any>> = Exclude<keyof TOptions["traits"], number>;
   `({
     MODEL_TRAIT_KEYS: astShorthand_1.ast.identifier(`${model.name}TraitKeys`),
-    MODEL_FACTORY_DEFINE_OPTIONS: astShorthand_1.ast.identifier(`${model.name}FactoryDefineOptions`),
-});
+    MODEL_FACTORY_DEFINE_OPTIONS: astShorthand_1.ast.identifier(
+      `${model.name}FactoryDefineOptions`
+    ),
+  });
 exports.modelTraitKeys = modelTraitKeys;
-const modelFactoryInterfaceWithoutTraits = (model) => talt_1.template.statement `
+const modelFactoryInterfaceWithoutTraits = (model) =>
+  talt_1.template.statement`
     export interface MODEL_FACTORY_INTERFACE_WITHOUT_TRAITS<TTransients extends Record<string, unknown> {
-      readonly _factoryFor: ${() => astShorthand_1.ast.literalTypeNode(astShorthand_1.ast.stringLiteral(model.name))}
+      readonly _factoryFor: ${() =>
+        astShorthand_1.ast.literalTypeNode(
+          astShorthand_1.ast.stringLiteral(model.name)
+        )}
       build(inputData?: CREATE_INPUT_TYPE): PromiseLike<Prisma.MODEL_CREATE_INPUT>
       buildCreateInput(inputData?: CREATE_INPUT_TYPE): PromiseLike<Prisma.MODEL_CREATE_INPUT>
       buildList(list: readonly CREATE_INPUT_TYPE[]): PromiseLike<Prisma.MODEL_CREATE_INPUT[]>
       buildList(count: number, item?: CREATE_INPUT_TYPE): PromiseLike<Prisma.MODEL_CREATE_INPUT[]>
-      pickForConnect(inputData: MODEL_TYPE): Pick<MODEL_TYPE, MODEL_ID_COLS>
+      pickForConnect(inputData: MODEL_TYPE): { connect: Pick<MODEL_TYPE, MODEL_ID_COLS> }
       create(inputData?: CREATE_INPUT_TYPE): PromiseLike<MODEL_TYPE>
       createList(list: readonly CREATE_INPUT_TYPE[]): PromiseLike<MODEL_TYPE[]>
       createList(count: number, item?: CREATE_INPUT_TYPE): PromiseLike<MODEL_TYPE[]>
-      createForConnect(inputData?: CREATE_INPUT_TYPE): PromiseLike<Pick<MODEL_TYPE, MODEL_ID_COLS>>
+      createForConnect(inputData?: CREATE_INPUT_TYPE): PromiseLike<{ connect: Pick<MODEL_TYPE, MODEL_ID_COLS> }>
     }
   `({
     MODEL_TYPE: astShorthand_1.ast.identifier(model.name),
-    MODEL_FACTORY_INTERFACE_WITHOUT_TRAITS: astShorthand_1.ast.identifier(`${model.name}FactoryInterfaceWithoutTraits`),
-    MODEL_CREATE_INPUT: astShorthand_1.ast.identifier(`${model.name}CreateInput`),
-    CREATE_INPUT_TYPE: talt_1.template.typeNode `Partial<Prisma.MODEL_CREATE_INPUT & TTransients>`({
-        MODEL_CREATE_INPUT: astShorthand_1.ast.identifier(`${model.name}CreateInput`),
+    MODEL_FACTORY_INTERFACE_WITHOUT_TRAITS: astShorthand_1.ast.identifier(
+      `${model.name}FactoryInterfaceWithoutTraits`
+    ),
+    MODEL_CREATE_INPUT: astShorthand_1.ast.identifier(
+      `${model.name}CreateInput`
+    ),
+    CREATE_INPUT_TYPE: talt_1.template
+      .typeNode`Partial<Prisma.MODEL_CREATE_INPUT & TTransients>`({
+      MODEL_CREATE_INPUT: astShorthand_1.ast.identifier(
+        `${model.name}CreateInput`
+      ),
     }),
-    MODEL_ID_COLS: astShorthand_1.ast.unionTypeNode(getIdFieldNames(model).map(fieldName => astShorthand_1.ast.literalTypeNode(astShorthand_1.ast.stringLiteral(fieldName)))),
-});
+    MODEL_ID_COLS: astShorthand_1.ast.unionTypeNode(
+      getIdFieldNames(model).map((fieldName) =>
+        astShorthand_1.ast.literalTypeNode(
+          astShorthand_1.ast.stringLiteral(fieldName)
+        )
+      )
+    ),
+  });
 exports.modelFactoryInterfaceWithoutTraits = modelFactoryInterfaceWithoutTraits;
-const modelFactoryInterface = (model) => talt_1.template.statement `
+const modelFactoryInterface = (model) =>
+  talt_1.template.statement`
     export interface MODEL_FACTORY_INTERFACE<
       TTransients extends Record<string, unknown> = Record<string, unknown>,
       TTraitName extends TraitName = TraitName
@@ -276,51 +441,90 @@ const modelFactoryInterface = (model) => talt_1.template.statement `
       use(name: TTraitName, ...names: readonly TTraitName[]): MODEL_FACTORY_INTERFACE_WITHOUT_TRAITS<TTransients>;
     }
   `({
-    MODEL_FACTORY_INTERFACE: astShorthand_1.ast.identifier(`${model.name}FactoryInterface`),
-    MODEL_FACTORY_INTERFACE_WITHOUT_TRAITS: astShorthand_1.ast.identifier(`${model.name}FactoryInterfaceWithoutTraits`),
-});
+    MODEL_FACTORY_INTERFACE: astShorthand_1.ast.identifier(
+      `${model.name}FactoryInterface`
+    ),
+    MODEL_FACTORY_INTERFACE_WITHOUT_TRAITS: astShorthand_1.ast.identifier(
+      `${model.name}FactoryInterfaceWithoutTraits`
+    ),
+  });
 exports.modelFactoryInterface = modelFactoryInterface;
 const isModelAssociationFactory = (fieldType, model) => {
-    const targetModel = model.fields.find((0, helpers_1.byName)(fieldType));
-    return talt_1.template.statement `
-    function ${() => astShorthand_1.ast.identifier(`is${model.name}${fieldType.name}Factory`)}(
-      x: MODEL_BELONGS_TO_RELATION_FACTORY | ${() => (0, exports.argInputType)(model, fieldType.name, fieldType.inputTypes[0])} | undefined
+  const targetModel = model.fields.find((0, helpers_1.byName)(fieldType));
+  return talt_1.template.statement`
+    function ${() =>
+      astShorthand_1.ast.identifier(`is${model.name}${fieldType.name}Factory`)}(
+      x: MODEL_BELONGS_TO_RELATION_FACTORY | ${() =>
+        (0, exports.argInputType)(
+          model,
+          fieldType.name,
+          fieldType.inputTypes[0]
+        )} | undefined
     ): x is MODEL_BELONGS_TO_RELATION_FACTORY {
-      return (x as any)?._factoryFor === ${() => astShorthand_1.ast.stringLiteral(targetModel.type)};
+      return (x as any)?._factoryFor === ${() =>
+        astShorthand_1.ast.stringLiteral(targetModel.type)};
     }
   `({
-        MODEL_BELONGS_TO_RELATION_FACTORY: astShorthand_1.ast.typeReferenceNode(`${model.name}${fieldType.name}Factory`),
-    });
+    MODEL_BELONGS_TO_RELATION_FACTORY: astShorthand_1.ast.typeReferenceNode(
+      `${model.name}${fieldType.name}Factory`
+    ),
+  });
 };
 exports.isModelAssociationFactory = isModelAssociationFactory;
 const autoGenerateModelScalarsOrEnumsFieldArgs = (model, field, enums) =>
-// Note: In Json sclar filed, inputTypes[0].location is not scalar but enumType
-field.inputTypes[field.inputTypes.length - 1].location === "scalar"
-    ? talt_1.template.expression `
+  // Note: In Json sclar filed, inputTypes[0].location is not scalar but enumType
+  field.inputTypes[field.inputTypes.length - 1].location === 'scalar'
+    ? talt_1.template.expression`
         getScalarFieldValueGenerator().SCALAR_TYPE({ modelName: MODEL_NAME, fieldName: FIELD_NAME, isId: IS_ID, isUnique: IS_UNIQUE, seq })
       `({
-        SCALAR_TYPE: astShorthand_1.ast.identifier(field.inputTypes[field.inputTypes.length - 1].type),
+        SCALAR_TYPE: astShorthand_1.ast.identifier(
+          field.inputTypes[field.inputTypes.length - 1].type
+        ),
         MODEL_NAME: astShorthand_1.ast.stringLiteral(model.name),
         FIELD_NAME: astShorthand_1.ast.stringLiteral(field.name),
-        IS_ID: model.fields.find((0, helpers_1.byName)(field)).isId || model.primaryKey?.fields.includes(field.name)
+        IS_ID:
+          model.fields.find((0, helpers_1.byName)(field)).isId ||
+          model.primaryKey?.fields.includes(field.name)
             ? astShorthand_1.ast.true()
             : astShorthand_1.ast.false(),
-        IS_UNIQUE: model.fields.find((0, helpers_1.byName)(field)).isUnique || model.uniqueFields.flat().includes(field.name)
+        IS_UNIQUE:
+          model.fields.find((0, helpers_1.byName)(field)).isUnique ||
+          model.uniqueFields.flat().includes(field.name)
             ? astShorthand_1.ast.true()
             : astShorthand_1.ast.false(),
-    })
+      })
     : astShorthand_1.ast.stringLiteral(extractFirstEnumValue(enums, field));
-exports.autoGenerateModelScalarsOrEnumsFieldArgs = autoGenerateModelScalarsOrEnumsFieldArgs;
-const autoGenerateModelScalarsOrEnums = (model, inputType, enums) => talt_1.template.statement `
+exports.autoGenerateModelScalarsOrEnumsFieldArgs =
+  autoGenerateModelScalarsOrEnumsFieldArgs;
+const autoGenerateModelScalarsOrEnums = (model, inputType, enums) =>
+  talt_1.template.statement`
     function AUTO_GENERATE_MODEL_SCALARS_OR_ENUMS({ seq }: { readonly seq: number }): MODEL_SCALAR_OR_ENUM_FIELDS {
-      return ${() => astShorthand_1.ast.objectLiteralExpression(filterRequiredScalarOrEnumFields(inputType).map(field => astShorthand_1.ast.propertyAssignment(field.name, (0, exports.autoGenerateModelScalarsOrEnumsFieldArgs)(model, field, enums))), true)};
+      return ${() =>
+        astShorthand_1.ast.objectLiteralExpression(
+          filterRequiredScalarOrEnumFields(inputType).map((field) =>
+            astShorthand_1.ast.propertyAssignment(
+              field.name,
+              (0, exports.autoGenerateModelScalarsOrEnumsFieldArgs)(
+                model,
+                field,
+                enums
+              )
+            )
+          ),
+          true
+        )};
     }
   `({
-    AUTO_GENERATE_MODEL_SCALARS_OR_ENUMS: astShorthand_1.ast.identifier(`autoGenerate${model.name}ScalarsOrEnums`),
-    MODEL_SCALAR_OR_ENUM_FIELDS: astShorthand_1.ast.identifier(`${model.name}ScalarOrEnumFields`),
-});
+    AUTO_GENERATE_MODEL_SCALARS_OR_ENUMS: astShorthand_1.ast.identifier(
+      `autoGenerate${model.name}ScalarsOrEnums`
+    ),
+    MODEL_SCALAR_OR_ENUM_FIELDS: astShorthand_1.ast.identifier(
+      `${model.name}ScalarOrEnumFields`
+    ),
+  });
 exports.autoGenerateModelScalarsOrEnums = autoGenerateModelScalarsOrEnums;
-const defineModelFactoryInternal = (model, inputType) => talt_1.template.statement `
+const defineModelFactoryInternal = (model, inputType) =>
+  talt_1.template.statement`
     function DEFINE_MODEL_FACTORY_INTERNAL<TTransients extends Record<string, unknown>, TOptions extends MODEL_FACTORY_DEFINE_OPTIONS<TTransients>>({
       defaultData: defaultDataResolver,
       onAfterBuild,
@@ -331,7 +535,8 @@ const defineModelFactoryInternal = (model, inputType) => talt_1.template.stateme
       const getFactoryWithTraits = (traitKeys: readonly MODEL_TRAIT_KEYS<TOptions>[] = []) => {
         const seqKey = {};
         const getSeq = () => getSequenceCounter(seqKey);
-        const screen = createScreener(${() => astShorthand_1.ast.stringLiteral(model.name)}, modelFieldDefinitions);
+        const screen = createScreener(${() =>
+          astShorthand_1.ast.stringLiteral(model.name)}, modelFieldDefinitions);
 
         const handleAfterBuild = createCallbackChain([
           onAfterBuild,
@@ -366,14 +571,26 @@ const defineModelFactoryInternal = (model, inputType) => talt_1.template.stateme
               ...traitData,
             };
           }, resolveValue(resolverInput);
-          const defaultAssociations = ${() => astShorthand_1.ast.objectLiteralExpression(filterBelongsToField(model, inputType).map(field => astShorthand_1.ast.propertyAssignment(field.name, talt_1.template.expression `
+          const defaultAssociations = ${() =>
+            astShorthand_1.ast.objectLiteralExpression(
+              filterBelongsToField(model, inputType).map((field) =>
+                astShorthand_1.ast.propertyAssignment(
+                  field.name,
+                  talt_1.template.expression`
                     IS_MODEL_BELONGS_TO_RELATION_FACTORY(defaultData.FIELD_NAME) ? {
                       create: await defaultData.FIELD_NAME.build()
                     } : defaultData.FIELD_NAME
                   `({
-    IS_MODEL_BELONGS_TO_RELATION_FACTORY: astShorthand_1.ast.identifier(`is${model.name}${field.name}Factory`),
-    FIELD_NAME: astShorthand_1.ast.identifier(field.name),
-}))), true)} as Prisma.MODEL_CREATE_INPUT; // FIXME no type assertion
+                    IS_MODEL_BELONGS_TO_RELATION_FACTORY:
+                      astShorthand_1.ast.identifier(
+                        `is${model.name}${field.name}Factory`
+                      ),
+                    FIELD_NAME: astShorthand_1.ast.identifier(field.name),
+                  })
+                )
+              ),
+              true
+            )} as Prisma.MODEL_CREATE_INPUT; // FIXME no type assertion
           const data: Prisma.MODEL_CREATE_INPUT = { ...requiredScalarData, ...defaultData, ...defaultAssociations, ...filteredInputData };
           await handleAfterBuild(data, transientFields);
           return data;
@@ -381,9 +598,20 @@ const defineModelFactoryInternal = (model, inputType) => talt_1.template.stateme
 
         const buildList = (...args: unknown[]) => Promise.all(normalizeList<CREATE_INPUT_TYPE>(...args).map(data => build(data)));
 
-        const pickForConnect = (inputData: ${() => astShorthand_1.ast.typeReferenceNode(model.name)}) => (
-          ${() => astShorthand_1.ast.objectLiteralExpression(getIdFieldNames(model).map(fieldName => astShorthand_1.ast.propertyAssignment(fieldName, talt_1.template.expression `inputData.${() => astShorthand_1.ast.identifier(fieldName)}`())), true)}
-        );
+        const pickForConnect = (inputData: ${() =>
+          astShorthand_1.ast.typeReferenceNode(model.name)}) => ({
+          connect: ${() =>
+            astShorthand_1.ast.objectLiteralExpression(
+              getIdFieldNames(model).map((fieldName) =>
+                astShorthand_1.ast.propertyAssignment(
+                  fieldName,
+                  talt_1.template.expression`inputData.${() =>
+                    astShorthand_1.ast.identifier(fieldName)}`()
+                )
+              ),
+              true
+            )}
+        });
 
         const create = async (
           inputData: CREATE_INPUT_TYPE = {}
@@ -401,7 +629,8 @@ const defineModelFactoryInternal = (model, inputType) => talt_1.template.stateme
         const createForConnect = (inputData: CREATE_INPUT_TYPE = {}) => create(inputData).then(pickForConnect);
 
         return {
-          _factoryFor: ${() => astShorthand_1.ast.stringLiteral(model.name)} as const,
+          _factoryFor: ${() =>
+            astShorthand_1.ast.stringLiteral(model.name)} as const,
           build,
           buildList,
           buildCreateInput: build,
@@ -421,127 +650,202 @@ const defineModelFactoryInternal = (model, inputType) => talt_1.template.stateme
       };
     }
   `({
-    MODEL_KEY: astShorthand_1.ast.identifier((0, helpers_1.camelize)(model.name)),
+    MODEL_KEY: astShorthand_1.ast.identifier(
+      (0, helpers_1.camelize)(model.name)
+    ),
     MODEL_TRAIT_KEYS: astShorthand_1.ast.identifier(`${model.name}TraitKeys`),
-    DEFINE_MODEL_FACTORY_INTERNAL: astShorthand_1.ast.identifier(`define${model.name}FactoryInternal`),
-    MODEL_FACTORY_INTERFACE: astShorthand_1.ast.identifier(`${model.name}FactoryInterface`),
-    MODEL_FACTORY_DEFINE_INPUT: astShorthand_1.ast.identifier(`${model.name}FactoryDefineInput`),
-    MODEL_FACTORY_DEFINE_OPTIONS: astShorthand_1.ast.identifier(`${model.name}FactoryDefineOptions`),
-    MODEL_CREATE_INPUT: astShorthand_1.ast.identifier(`${model.name}CreateInput`),
-    AUTO_GENERATE_MODEL_SCALARS_OR_ENUMS: astShorthand_1.ast.identifier(`autoGenerate${model.name}ScalarsOrEnums`),
-    CREATE_INPUT_TYPE: talt_1.template.typeNode `Partial<Prisma.MODEL_CREATE_INPUT & TTransients>`({
-        MODEL_CREATE_INPUT: astShorthand_1.ast.identifier(`${model.name}CreateInput`),
+    DEFINE_MODEL_FACTORY_INTERNAL: astShorthand_1.ast.identifier(
+      `define${model.name}FactoryInternal`
+    ),
+    MODEL_FACTORY_INTERFACE: astShorthand_1.ast.identifier(
+      `${model.name}FactoryInterface`
+    ),
+    MODEL_FACTORY_DEFINE_INPUT: astShorthand_1.ast.identifier(
+      `${model.name}FactoryDefineInput`
+    ),
+    MODEL_FACTORY_DEFINE_OPTIONS: astShorthand_1.ast.identifier(
+      `${model.name}FactoryDefineOptions`
+    ),
+    MODEL_CREATE_INPUT: astShorthand_1.ast.identifier(
+      `${model.name}CreateInput`
+    ),
+    AUTO_GENERATE_MODEL_SCALARS_OR_ENUMS: astShorthand_1.ast.identifier(
+      `autoGenerate${model.name}ScalarsOrEnums`
+    ),
+    CREATE_INPUT_TYPE: talt_1.template
+      .typeNode`Partial<Prisma.MODEL_CREATE_INPUT & TTransients>`({
+      MODEL_CREATE_INPUT: astShorthand_1.ast.identifier(
+        `${model.name}CreateInput`
+      ),
     }),
     DEFALUT_DATA_RESOLVER: filterRequiredInputObjectTypeField(inputType).length
-        ? talt_1.template.expression(`defaultDataResolver`)({})
-        : talt_1.template.expression(`defaultDataResolver ?? {}`)({}),
-});
+      ? talt_1.template.expression(`defaultDataResolver`)({})
+      : talt_1.template.expression(`defaultDataResolver ?? {}`)({}),
+  });
 exports.defineModelFactoryInternal = defineModelFactoryInternal;
 const modelFactoryBuilder = (model, inputType) => {
-    const compiled = filterRequiredInputObjectTypeField(inputType).length
-        ? talt_1.template.statement `
+  const compiled = filterRequiredInputObjectTypeField(inputType).length
+    ? talt_1.template.statement`
         interface MODEL_FACTORY_BUILDER {
           <TOptions extends MODEL_FACTORY_DEFINE_OPTIONS>(options: TOptions): MODEL_FACTORY_INTERFACE<{}, MODEL_TRAIT_KEYS<TOptions>>;
           withTransientFields: <TTransients extends MODEL_TRANSIENT_FIELDS>(defaultTransientFieldValues: TTransients) => <TOptions extends MODEL_FACTORY_DEFINE_OPTIONS<TTransients>>(options: TOptions) => MODEL_FACTORY_INTERFACE<TTransients, MODEL_TRAIT_KEYS<TOptions>>
         }`
-        : talt_1.template.statement `
+    : talt_1.template.statement`
         interface MODEL_FACTORY_BUILDER {
           <TOptions extends MODEL_FACTORY_DEFINE_OPTIONS>(options?: TOptions): MODEL_FACTORY_INTERFACE<{}, MODEL_TRAIT_KEYS<TOptions>>;
           withTransientFields: <TTransients extends MODEL_TRANSIENT_FIELDS>(defaultTransientFieldValues: TTransients) => <TOptions extends MODEL_FACTORY_DEFINE_OPTIONS<TTransients>>(options?: TOptions) => MODEL_FACTORY_INTERFACE<TTransients, MODEL_TRAIT_KEYS<TOptions>>
         }`;
-    return compiled({
-        MODEL_FACTORY_DEFINE_OPTIONS: astShorthand_1.ast.identifier(`${model.name}FactoryDefineOptions`),
-        MODEL_FACTORY_INTERFACE: astShorthand_1.ast.identifier(`${model.name}FactoryInterface`),
-        MODEL_FACTORY_BUILDER: astShorthand_1.ast.identifier(`${model.name}FactoryBuilder`),
-        MODEL_TRAIT_KEYS: astShorthand_1.ast.identifier(`${model.name}TraitKeys`),
-        MODEL_TRANSIENT_FIELDS: astShorthand_1.ast.identifier(`${model.name}TransientFields`),
-    });
+  return compiled({
+    MODEL_FACTORY_DEFINE_OPTIONS: astShorthand_1.ast.identifier(
+      `${model.name}FactoryDefineOptions`
+    ),
+    MODEL_FACTORY_INTERFACE: astShorthand_1.ast.identifier(
+      `${model.name}FactoryInterface`
+    ),
+    MODEL_FACTORY_BUILDER: astShorthand_1.ast.identifier(
+      `${model.name}FactoryBuilder`
+    ),
+    MODEL_TRAIT_KEYS: astShorthand_1.ast.identifier(`${model.name}TraitKeys`),
+    MODEL_TRANSIENT_FIELDS: astShorthand_1.ast.identifier(
+      `${model.name}TransientFields`
+    ),
+  });
 };
 exports.modelFactoryBuilder = modelFactoryBuilder;
 const defineModelFactory = (model, inputType) => {
-    const compiled = filterRequiredInputObjectTypeField(inputType).length
-        ? talt_1.template.statement `
+  const compiled = filterRequiredInputObjectTypeField(inputType).length
+    ? talt_1.template.statement`
         export const DEFINE_MODEL_FACTORY = (<TOptions extends MODEL_FACTORY_DEFINE_OPTIONS>(options: TOptions): MODEL_FACTORY_INTERFACE<TOptions> => {
           return DEFINE_MODEL_FACTORY_INTERNAL(options, {});
         }) as MODEL_FACTORY_BUILDER;
       `
-        : talt_1.template.statement `
+    : talt_1.template.statement`
         export const DEFINE_MODEL_FACTORY = (<TOptions extends MODEL_FACTORY_DEFINE_OPTIONS>(options?: TOptions): MODEL_FACTORY_INTERFACE<TOptions> => {
           return DEFINE_MODEL_FACTORY_INTERNAL(options ?? {}, {});
         }) as MODEL_FACTORY_BUILDER;
       `;
-    const tsDoc = `
+  const tsDoc = `
 Define factory for {@link ${model.name}} model.
 
 @param options
 @returns factory {@link ${model.name}FactoryInterface}
   `;
-    return (0, comment_1.wrapWithTSDoc)(tsDoc, compiled({
-        DEFINE_MODEL_FACTORY: astShorthand_1.ast.identifier(`define${model.name}Factory`),
-        DEFINE_MODEL_FACTORY_INTERNAL: astShorthand_1.ast.identifier(`define${model.name}FactoryInternal`),
-        MODEL_FACTORY_DEFINE_OPTIONS: astShorthand_1.ast.identifier(`${model.name}FactoryDefineOptions`),
-        MODEL_FACTORY_INTERFACE: astShorthand_1.ast.identifier(`${model.name}FactoryInterface`),
-        MODEL_FACTORY_BUILDER: astShorthand_1.ast.identifier(`${model.name}FactoryBuilder`),
-    }));
+  return (0, comment_1.wrapWithTSDoc)(
+    tsDoc,
+    compiled({
+      DEFINE_MODEL_FACTORY: astShorthand_1.ast.identifier(
+        `define${model.name}Factory`
+      ),
+      DEFINE_MODEL_FACTORY_INTERNAL: astShorthand_1.ast.identifier(
+        `define${model.name}FactoryInternal`
+      ),
+      MODEL_FACTORY_DEFINE_OPTIONS: astShorthand_1.ast.identifier(
+        `${model.name}FactoryDefineOptions`
+      ),
+      MODEL_FACTORY_INTERFACE: astShorthand_1.ast.identifier(
+        `${model.name}FactoryInterface`
+      ),
+      MODEL_FACTORY_BUILDER: astShorthand_1.ast.identifier(
+        `${model.name}FactoryBuilder`
+      ),
+    })
+  );
 };
 exports.defineModelFactory = defineModelFactory;
 const assignWithTransientFields = (model, inputType) => {
-    const compiled = filterRequiredInputObjectTypeField(inputType).length
-        ? talt_1.template.statement `
+  const compiled = filterRequiredInputObjectTypeField(inputType).length
+    ? talt_1.template.statement`
         DEFINE_MODEL_FACTORY.withTransientFields = defaultTransientFieldValues => options => DEFINE_MODEL_FACTORY_INTERNAL(options, defaultTransientFieldValues);
       `
-        : talt_1.template.statement `
+    : talt_1.template.statement`
         DEFINE_MODEL_FACTORY.withTransientFields = defaultTransientFieldValues => options => DEFINE_MODEL_FACTORY_INTERNAL(options ?? {}, defaultTransientFieldValues);
       `;
-    return compiled({
-        DEFINE_MODEL_FACTORY_INTERNAL: astShorthand_1.ast.identifier(`define${model.name}FactoryInternal`),
-        DEFINE_MODEL_FACTORY: astShorthand_1.ast.identifier(`define${model.name}Factory`),
-    });
+  return compiled({
+    DEFINE_MODEL_FACTORY_INTERNAL: astShorthand_1.ast.identifier(
+      `define${model.name}FactoryInternal`
+    ),
+    DEFINE_MODEL_FACTORY: astShorthand_1.ast.identifier(
+      `define${model.name}Factory`
+    ),
+  });
 };
 exports.assignWithTransientFields = assignWithTransientFields;
-function getSourceFile({ document, prismaClientModuleSpecifier = "@prisma/client", }) {
-    const modelEnums = [
-        ...new Set(document.schema.inputObjectTypes.prisma
-            .filter(iOT => iOT.name.endsWith("CreateInput"))
-            .flatMap(filterEnumFields)
-            .flatMap(field => field.inputTypes
-            .filter(inputType => inputType.namespace === "model")
-            .map(inputType => inputType.type))),
-    ];
-    const modelNames = document.datamodel.models
-        .map(m => m.name)
-        .filter(modelName => findPrismaCreateInputTypeFromModelName(document, modelName));
-    const statements = [
-        ...modelNames.map(modelName => (0, exports.importStatement)(modelName, prismaClientModuleSpecifier)),
-        ...modelEnums.map(enumName => (0, exports.importStatement)(enumName, prismaClientModuleSpecifier)),
-        ...(0, exports.header)(prismaClientModuleSpecifier).statements,
-        ...(0, comment_1.insertLeadingBreakMarker)((0, exports.genericDeclarations)().statements),
-        (0, comment_1.insertLeadingBreakMarker)((0, exports.modelFieldDefinitions)(document.datamodel.models)),
-        ...document.datamodel.models
-            .reduce((acc, model) => {
-            const createInputType = findPrismaCreateInputTypeFromModelName(document, model.name);
-            if (!createInputType)
-                return acc;
-            return [...acc, { model, createInputType }];
-        }, [])
-            .flatMap(({ model, createInputType }) => [
-            (0, exports.modelScalarOrEnumFields)(model, createInputType),
-            ...filterBelongsToField(model, createInputType).map(fieldType => (0, exports.modelBelongsToRelationFactory)(fieldType, model)),
-            (0, exports.modelFactoryDefineInput)(model, createInputType),
-            (0, exports.modelTransientFields)(model),
-            (0, exports.modelFactoryTrait)(model),
-            (0, exports.modelFactoryDefineOptions)(model, filterRequiredInputObjectTypeField(createInputType).length === 0),
-            ...filterBelongsToField(model, createInputType).map(fieldType => (0, exports.isModelAssociationFactory)(fieldType, model)),
-            (0, exports.modelTraitKeys)(model),
-            (0, exports.modelFactoryInterfaceWithoutTraits)(model),
-            (0, exports.modelFactoryInterface)(model),
-            (0, exports.autoGenerateModelScalarsOrEnums)(model, createInputType, document.schema.enumTypes.model ?? []),
-            (0, exports.defineModelFactoryInternal)(model, createInputType),
-            (0, exports.modelFactoryBuilder)(model, createInputType),
-            (0, exports.defineModelFactory)(model, createInputType),
-            (0, exports.assignWithTransientFields)(model, createInputType),
-        ])
-            .map(comment_1.insertLeadingBreakMarker),
-    ];
-    return astShorthand_1.ast.updateSourceFile(talt_1.template.sourceFile("")(), statements);
+function getSourceFile({
+  document,
+  prismaClientModuleSpecifier = '@prisma/client',
+}) {
+  const modelEnums = [
+    ...new Set(
+      document.schema.inputObjectTypes.prisma
+        .filter((iOT) => iOT.name.endsWith('CreateInput'))
+        .flatMap(filterEnumFields)
+        .flatMap((field) =>
+          field.inputTypes
+            .filter((inputType) => inputType.namespace === 'model')
+            .map((inputType) => inputType.type)
+        )
+    ),
+  ];
+  const modelNames = document.datamodel.models
+    .map((m) => m.name)
+    .filter((modelName) =>
+      findPrismaCreateInputTypeFromModelName(document, modelName)
+    );
+  const statements = [
+    ...modelNames.map((modelName) =>
+      (0, exports.importStatement)(modelName, prismaClientModuleSpecifier)
+    ),
+    ...modelEnums.map((enumName) =>
+      (0, exports.importStatement)(enumName, prismaClientModuleSpecifier)
+    ),
+    ...(0, exports.header)(prismaClientModuleSpecifier).statements,
+    ...(0, comment_1.insertLeadingBreakMarker)(
+      (0, exports.genericDeclarations)().statements
+    ),
+    (0, comment_1.insertLeadingBreakMarker)(
+      (0, exports.modelFieldDefinitions)(document.datamodel.models)
+    ),
+    ...document.datamodel.models
+      .reduce((acc, model) => {
+        const createInputType = findPrismaCreateInputTypeFromModelName(
+          document,
+          model.name
+        );
+        if (!createInputType) return acc;
+        return [...acc, { model, createInputType }];
+      }, [])
+      .flatMap(({ model, createInputType }) => [
+        (0, exports.modelScalarOrEnumFields)(model, createInputType),
+        ...filterBelongsToField(model, createInputType).map((fieldType) =>
+          (0, exports.modelBelongsToRelationFactory)(fieldType, model)
+        ),
+        (0, exports.modelFactoryDefineInput)(model, createInputType),
+        (0, exports.modelTransientFields)(model),
+        (0, exports.modelFactoryTrait)(model),
+        (0, exports.modelFactoryDefineOptions)(
+          model,
+          filterRequiredInputObjectTypeField(createInputType).length === 0
+        ),
+        ...filterBelongsToField(model, createInputType).map((fieldType) =>
+          (0, exports.isModelAssociationFactory)(fieldType, model)
+        ),
+        (0, exports.modelTraitKeys)(model),
+        (0, exports.modelFactoryInterfaceWithoutTraits)(model),
+        (0, exports.modelFactoryInterface)(model),
+        (0, exports.autoGenerateModelScalarsOrEnums)(
+          model,
+          createInputType,
+          document.schema.enumTypes.model ?? []
+        ),
+        (0, exports.defineModelFactoryInternal)(model, createInputType),
+        (0, exports.modelFactoryBuilder)(model, createInputType),
+        (0, exports.defineModelFactory)(model, createInputType),
+        (0, exports.assignWithTransientFields)(model, createInputType),
+      ])
+      .map(comment_1.insertLeadingBreakMarker),
+  ];
+  return astShorthand_1.ast.updateSourceFile(
+    talt_1.template.sourceFile('')(),
+    statements
+  );
 }
