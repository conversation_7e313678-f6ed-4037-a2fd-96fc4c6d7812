import type { reportTemplate } from "@report_templates/index";

export interface WorkoutReportTemplate extends reportTemplate {
  templateId: "workout_report";
  title: string;
  reportSections: {
    horse_conditions: "horse_conditions";
    horse_body_photo: "image";
    workout_conditions: "workout_conditions";
    workout_detail: "plain_text";
    rider_comment: "plain_text";
    next_race_schedule: "plain_text";
    next_race_jockey_name: "plain_text";
    report_body: "plain_text";
  };
}
