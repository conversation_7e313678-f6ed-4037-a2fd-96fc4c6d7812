{"version": "2.0.0", "tasks": [{"type": "npm", "script": "start:dev", "group": "build", "problemMatcher": [], "label": "npm: start:dev", "detail": "開発サーバーの起動（ウォッチモード）"}, {"type": "npm", "script": "test", "group": {"kind": "test", "isDefault": true}, "problemMatcher": [], "label": "npm: test", "detail": "Vitestでテストを実行"}, {"type": "npm", "script": "test:watch", "group": "test", "problemMatcher": [], "label": "npm: test:watch", "detail": "テストをウォッチモードで実行"}, {"type": "npm", "script": "prisma:generate", "problemMatcher": [], "label": "npm: prisma:generate", "detail": "Prismaの型定義を生成"}, {"type": "npm", "script": "schema:generate", "problemMatcher": [], "label": "npm: schema:generate", "detail": "スキーマの生成"}, {"type": "npm", "script": "biome", "group": {"kind": "build", "isDefault": true}, "problemMatcher": [], "label": "npm: biome", "detail": "フォーマット、リント、チェックを実行"}, {"type": "npm", "script": "typecheck", "problemMatcher": ["$tsc"], "label": "npm: typecheck", "detail": "型チェックを実行"}]}