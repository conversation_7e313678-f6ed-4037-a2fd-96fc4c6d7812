{
  // Use IntelliSense to learn about possible attributes.
  // Hover to view descriptions of existing attributes.
  // For more information, visit: https://go.microsoft.com/fwlink/?linkid=830387
  "version": "0.2.0",
  "configurations": [
    {
      "type": "node",
      "request": "launch",
      "name": "Launch Program",
      "runtimeExecutable": "tsx",
      "skipFiles": [
        // Node.js internal core modules
        "<node_internals>/**",
        "${workspaceFolder}/node_modules/**",
      ],
      "program": "${workspaceFolder}/src/main.ts",
      "cwd": "${workspaceFolder}",
      "console": "integratedTerminal",
      "internalConsoleOptions": "openOnSessionStart",
    }
  ]
}
