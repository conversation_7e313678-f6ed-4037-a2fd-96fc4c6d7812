{"words": ["abelorg", "apify", "autoincrement", "biomejs", "bufbuild", "Ｃａｂｃ", "commitish", "connectrpc", "datasource", "datasources", "daypart", "dbgenerated", "devcontainer", "devcontainers", "dpkg", "equtum", "esbenp", "fabbrica", "fastify", "Filenaming", "fromdate", "getsentry", "healthcheck", "initdb", "Inn<PERSON>", "<PERSON><PERSON>i", "LINESTRING", "luxon", "mapi", "ma<PERSON><PERSON>no", "middie", "<PERSON><PERSON>", "mongolyy", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "nocheck", "notionhq", "Orbstack", "ppasswd", "presigner", "proto", "protobuf", "protoc", "QNHDBZ", "quramy", "reviewdog", "rfid", "rsms", "rsmt", "r<PERSON>sebach", "SEGH", "sendgrid", "shoeings", "temurin", "thollander", "typecheck", "usecase", "usecases", "uuidv", "veterinarian", "vitest", "vprisma", "whatwg"], "ignorePaths": ["./docker/firebase-emulator/export/auth_export/accounts.json", "./schema/gen/", "./database/", ".giti<PERSON>re", "patches/*"]}