{"editor.formatOnSave": true, "files.trimTrailingWhitespace": true, "files.insertFinalNewline": true, "editor.defaultFormatter": "esbenp.prettier-vscode", "[javascript]": {"editor.defaultFormatter": "biomejs.biome"}, "[javascriptreact]": {"editor.defaultFormatter": "biomejs.biome"}, "[typescript]": {"editor.defaultFormatter": "biomejs.biome"}, "[typescriptreact]": {"editor.defaultFormatter": "biomejs.biome"}, "editor.codeActionsOnSave": {"quickfix.biome": "explicit", "source.organizeImports.biome": "always"}, "[prisma]": {"editor.defaultFormatter": "Prisma.prisma"}, "explorer.fileNesting.patterns": {"*.ts": "${capture}.test.ts"}}