import type { MailTemplate } from "@mail_templates/index";
import { environment } from "@/utils/environment";
import { getOwnerReportLink } from "@/utils/generate_frontend_url";

export const sendReportForVerifiedOwnerMailTemplate: ({
  ownerName,
  organizationName,
  horseName,
  sentReportId,
  horseId,
  templateId,
  reportTitle,
  reportThumbnailImageUrl,
}: {
  ownerName: string;
  organizationName: string;
  horseName: string;
  sentReportId: string;
  horseId: string;
  templateId: string;
  reportTitle: string;
  reportThumbnailImageUrl: string;
}) => MailTemplate = ({
  ownerName,
  organizationName,
  horseName,
  sentReportId,
  horseId,
  templateId,
  reportTitle,
  reportThumbnailImageUrl,
}) => ({
  templateKey: "sendReportForVerifiedOwner",
  subject: `${organizationName}から${horseName}号のレポート共有のお知らせ`,
  text: `
拝啓　${ownerName}様

時下ますますご清祥のこととお慶び申し上げます。
平素は格別のご高配を賜り、厚く御礼申し上げます。
${organizationName}から${horseName}号のレポートが届きました。

下記のリンクから、${horseName}号のレポートをご確認ください。

${getOwnerReportLink(sentReportId, horseId, templateId)}

敬具
${organizationName}

※なお、本メールは${organizationName}様からの依頼に基づき、EQUTUMからお送りしております。
※本メールは送信専用のアドレスから送信しているため、ご返信いただいてもお答えできません。予めご了承ください。
`,
  html: `
<p>拝啓　${ownerName}様</p>

<p>時下ますますご清祥のこととお慶び申し上げます。<br />
平素は格別のご高配を賜り、厚く御礼申し上げます。</p>

<p>${organizationName}から${horseName}号のレポートが届きました。</p>

<a href="${getOwnerReportLink(sentReportId, horseId, templateId)}">${horseName}号のレポートをご確認ください</a>

<p>敬具<br />
${organizationName}</p>

<p>※なお、本メールは${organizationName}様からの依頼に基づき、EQUTUMからお送りしております。<br />
※本メールは送信専用のアドレスから送信しているため、ご返信いただいてもお答えできません。予めご了承ください。</p>
`,
  dynamicTemplateId: "d-28df2269916e4b34955672ea14067546",
  dynamicTemplateData: {
    ownerName,
    organizationName,
    horseName,
    reportTitle,
    reportThumbnailImageUrl,
    reportLink: getOwnerReportLink(sentReportId, horseId, templateId),
    logoImageUrl:
      environment === "production"
        ? "https://equtum-assets-prd.s3.ap-northeast-1.amazonaws.com/logo.png"
        : "https://equtum-assets-dev.s3.ap-northeast-1.amazonaws.com/logo.png",
    termsUrl:
      environment === "production"
        ? "https://owners.equtum.com/terms"
        : "https://owners.equtum.dev/terms",
    privacyPolicyUrl:
      environment === "production"
        ? "https://abel-inc.com/privacy"
        : "https://abel-inc.com/privacy",
    contactUrl:
      environment === "production"
        ? "https://abel-inc.com/equtum#contact"
        : "https://abel-inc.com/equtum#contact",
    readButtonImageUrl:
      environment === "production"
        ? "https://equtum-assets-prd.s3.ap-northeast-1.amazonaws.com/read_button.png"
        : "https://equtum-assets-dev.s3.ap-northeast-1.amazonaws.com/read_button.png",
  },
});
