import type { MailTemplate } from "@mail_templates/index";

export const sendUnreadReportReminderMailTemplate: ({
  ownerName,
  unreadReportCount,
}: {
  ownerName: string;
  unreadReportCount: number;
}) => MailTemplate = ({ ownerName, unreadReportCount }) => ({
  templateKey: "sendUnreadReportReminder",
  subject: "未読レポートがあります",
  text: `
${ownerName}様

いつもEQUTUMをご利用いただき、ありがとうございます。

${unreadReportCount}件の未読レポートがあります。
下記のリンクから、未読レポートをご確認ください。

${process.env.FRONTEND_OWNERS_URL}/reports?filter=unread

※本メールは送信専用のアドレスから送信しているため、ご返信いただいてもお答えできません。予めご了承ください。
`,
  html: `
<p>${ownerName}様</p>

<p>いつもEQUTUMをご利用いただき、ありがとうございます。</p>

<p>${unreadReportCount}件の未読レポートがあります。<br />
下記のリンクから、レポートをご確認ください。</p>

<a href="${process.env.FRONTEND_OWNERS_URL}/reports?filter=unread">「未読レポートを確認する」</a>

<p>※本メールは送信専用のアドレスから送信しているため、ご返信いただいてもお答えできません。予めご了承ください。</p>
`,
});
