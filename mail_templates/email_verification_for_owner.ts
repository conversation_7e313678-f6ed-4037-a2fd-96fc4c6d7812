import type { MailTemplate } from "@mail_templates/index";

export const emailVerificationForOwner: ({
  ownerName,
  verificationLink,
}: {
  ownerName: string;
  verificationLink: string;
}) => MailTemplate = ({ ownerName, verificationLink }) => ({
  templateKey: "emailVerificationForOwner",
  subject: "メールアドレス認証のお願い",
  text: `
拝啓　${ownerName}様

時下ますますご清祥のこととお慶び申し上げます。
平素は格別のご高配を賜り、厚く御礼申し上げます。

下記のリンクよりアクセスいただき、メールアドレスを認証していただけますと幸いです。

${verificationLink}

ご不明な点やご質問がございましたら、いつでもお気軽にお問い合わせください。
${ownerName}様のご活躍を心よりお祈り申し上げます。

敬具
`,
  html: `
<p>拝啓　${ownerName}様</p>

<p>時下ますますご清祥のこととお慶び申し上げます。<br />平素は格別のご高配を賜り、厚く御礼申し上げます。</p>

<p>下記のリンクよりアクセスいただき、メールアドレスを認証していただけますと幸いです。</p>

<a href="${verificationLink}">認証はこちら</a>

<p>ご不明な点やご質問がございましたら、いつでもお気軽にお問い合わせください。<br/>${ownerName}様のご活躍を心よりお祈り申し上げます。</p>

<p>敬具</p>
`,
});
