import type { MailTemplate } from "@mail_templates/index";
import { environment } from "@/utils/environment";

export const emailVerificationForOwnerWithCode: ({
  verificationCode,
}: {
  verificationCode: string;
}) => MailTemplate = ({ verificationCode }) => ({
  templateKey: "emailVerificationForOwnerWithCode",
  subject: "メールアドレス認証のお願い",
  text: `
次の認証コードをブラウザ上でご入力ください。

${verificationCode}

敬具
`,
  dynamicTemplateId: "d-f739260c66dc4e788f061244f8c82f2f",
  dynamicTemplateData: {
    verificationCode,
    logoImageUrl:
      environment === "production"
        ? "https://equtum-assets-prd.s3.ap-northeast-1.amazonaws.com/logo.png"
        : "https://equtum-assets-dev.s3.ap-northeast-1.amazonaws.com/logo.png",
    termsUrl:
      environment === "production"
        ? "https://owners.equtum.com/terms"
        : "https://owners.equtum.dev/terms",
    privacyPolicyUrl:
      environment === "production"
        ? "https://abel-inc.com/privacy"
        : "https://abel-inc.com/privacy",
    contactUrl:
      environment === "production"
        ? "https://abel-inc.com/equtum#contact"
        : "https://abel-inc.com/equtum#contact",
  },
});
