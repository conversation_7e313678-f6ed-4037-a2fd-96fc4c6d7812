import type { MailTemplate } from "@mail_templates/index";
import { environment } from "@/utils/environment";

export const registerNewOwnerMailTemplate: ({
  organizationName,
}: {
  organizationName: string;
  inviteLink: string;
}) => MailTemplate = ({ organizationName, inviteLink }) => ({
  templateKey: "registerNewOwner",
  subject: "【ご招待】EQUTUMへのアクセスをご案内いたします",
  text: `
時下ますますご清祥のこととお慶び申し上げます。
平素は格別のご高配を賜り、厚く御礼申し上げます。

この度、EQUTUMへのアクセス権をご用意いたしました。
本システムは、馬主様の大切な競走馬の情報を一元管理し、円滑な馬主活動をサポートする目的で開発されました。

下記の招待リンクよりアクセスいただき、アカウントを設定していただけますと幸いです。

${inviteLink}

本システムでは以下のような機能をご利用いただけます：
・所有馬のレポートの確認
・過去のレポートの閲覧

ご不明な点やご質問がございましたら、いつでもお気軽にお問い合わせください。馬主様のご活躍を心よりお祈り申し上げます。

敬具

${organizationName}

※なお、本メールは${organizationName}様からの依頼に基づき、EQUTUMからお送りしております。
※本メールは送信専用のアドレスから送信しているため、ご返信いただいてもお答えできません。予めご了承ください。
`,
  html: `
<p>時下ますますご清祥のこととお慶び申し上げます。<br />
平素は格別のご高配を賜り、厚く御礼申し上げます。</p>

<p>この度、EQUTUMへのアクセス権をご用意いたしました。<br />
本システムは、馬主様の大切な競走馬の情報を一元管理し、円滑な馬主活動をサポートする目的で開発されました。</p>

<a href="${inviteLink}">アクセスはこちらから行えます</a>

<p>本システムでは以下のような機能をご利用いただけます：<br />
・所有馬のレポートの確認<br />
・過去のレポートの閲覧</p>

<p>ご不明な点やご質問がございましたら、いつでもお気軽にお問い合わせください。馬主様のご活躍を心よりお祈り申し上げます。</p>

<p>敬具<br />
${organizationName}</p>

<p>※なお、本メールは${organizationName}様からの依頼に基づき、EQUTUMからお送りしております。<br />
※本メールは送信専用のアドレスから送信しているため、ご返信いただいてもお答えできません。予めご了承ください。</p>
`,
  dynamicTemplateId: "d-7153ced9ac3844128be7abc5dfeee280",
  dynamicTemplateData: {
    organizationName,
    inviteLink,
    reportImageUrl:
      environment === "production"
        ? "https://equtum-assets-prd.s3.ap-northeast-1.amazonaws.com/report.png"
        : "https://equtum-assets-dev.s3.ap-northeast-1.amazonaws.com/report.png",
    pastReportImageUrl:
      environment === "production"
        ? "https://equtum-assets-prd.s3.ap-northeast-1.amazonaws.com/list_reports.png"
        : "https://equtum-assets-dev.s3.ap-northeast-1.amazonaws.com/list_reports.png",
    horseBodyImageUrl:
      environment === "production"
        ? "https://equtum-assets-prd.s3.ap-northeast-1.amazonaws.com/horse_body_photos.png"
        : "https://equtum-assets-dev.s3.ap-northeast-1.amazonaws.com/horse_body_photos.png",
    reportDetailImageUrl:
      environment === "production"
        ? "https://equtum-assets-prd.s3.ap-northeast-1.amazonaws.com/report_detail.png"
        : "https://equtum-assets-dev.s3.ap-northeast-1.amazonaws.com/report_detail.png",
    logoImageUrl:
      environment === "production"
        ? "https://equtum-assets-prd.s3.ap-northeast-1.amazonaws.com/logo.png"
        : "https://equtum-assets-dev.s3.ap-northeast-1.amazonaws.com/logo.png",
    termsUrl:
      environment === "production"
        ? "https://owners.equtum.com/terms"
        : "https://owners.equtum.dev/terms",
    privacyPolicyUrl:
      environment === "production"
        ? "https://abel-inc.com/privacy"
        : "https://abel-inc.com/privacy",
    contactUrl:
      environment === "production"
        ? "https://abel-inc.com/equtum#contact"
        : "https://abel-inc.com/equtum#contact",
  },
});
