import type { MailTemplate } from "@mail_templates/index";

export const passwordResetForTrainer: ({
  trainerName,
  resetLink,
}: {
  trainerName: string;
  resetLink: string;
}) => MailTemplate = ({ trainerName, resetLink }) => ({
  templateKey: "passwordResetForTrainer",
  subject: "パスワードを再設定してください",
  text: `
拝啓　${trainerName}様

アカウントのパスワードを再設定するには、次のリンクをクリックしてください。

${resetLink}

ご不明な点やご質問がございましたら、いつでもお気軽にお問い合わせください。

敬具
`,
  html: `
<p>拝啓　${trainerName}様</p>

<p>アカウントのパスワードを再設定するには、次のリンクをクリックしてください。</p>

<a href="${resetLink}">パスワード再設定はこちら</a>

<p>ご不明な点やご質問がございましたら、いつでもお気軽にお問い合わせください。</p>

<p>敬具</p>
`,
});
