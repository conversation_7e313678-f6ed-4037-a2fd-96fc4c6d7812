---
name: 機能実装
about: API・機能実装のためのissueテンプレート
title: "[api] "
labels: "api,enhancement"
assignees: ""
---

<!--
タイトル命名規則:
- [app/api] TrainingsApp向けAPI実装
- [horse_management/api] HorseManagementApp向けAPI実装
- [trainers/api] TrainersWeb向けAPI実装
- [owners/api] OwnersWeb向けAPI実装
- [database] データベース関連実装
-->

# 概要

<!-- 実装する機能の概要を簡潔に記述 -->

## 背景

<!-- なぜこの機能が必要なのか -->

## 実装内容

### API Schema

<!-- 必要な場合のみ記述 -->

```protobuf
// 新しいRPCやメッセージの定義
```

### 実装するファイル

- [ ] `schema/proto` - スキーマ定義
- [ ] `src/handlers` - API ハンドラー
- [ ] `src/repositories` - Repository 関数
- [ ] その他:

### 主な機能

- [ ]
- [ ]
- [ ]

## 技術的考慮事項

<!-- 重要なポイントのみ -->

- **セキュリティ**:
- **認証**: 必要/不要
- **その他**:

## 完了条件

- [ ] スキーマ実装
- [ ] `npm run schema:generate` 実行
- [ ] API 実装
- [ ] テスト実装
- [ ] 動作確認 `npm run test`

## 関連 Issue

<!-- あれば記述 -->

## 優先度

<!-- High/Medium/Low -->

## 参考実装

## <!-- 参考にできるファイルがあれば -->
