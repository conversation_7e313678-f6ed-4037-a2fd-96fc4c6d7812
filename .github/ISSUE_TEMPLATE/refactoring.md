---
name: リファクタリング
about: コードのリファクタリングのためのissueテンプレート
title: '[REFACTOR] '
labels: 'enhancement,refactoring'
assignees: ''
---

<!--
タイトル命名規則:
- [REFACTOR] リファクタリング内容の概要
例: [REFACTOR] ユーザー認証ミドルウェアの共通化
-->

# リファクタリング

## 概要

<!-- リファクタリングの概要を簡潔に記述 -->

## 背景・動機

<!-- なぜリファクタリングが必要なのか -->

- [ ] コードの可読性向上
- [ ] パフォーマンス改善
- [ ] 保守性向上
- [ ] 技術的負債の解消
- [ ] その他:

## 現在の問題

## 対象ファイル

- `[file_path]`
- `[file_path]`

## 改善案

## 実装計画

- [ ]
- [ ]
- [ ]

## 影響範囲・リスク

- [ ] **Low** - 影響範囲が限定的
- [ ] **Medium** - 一部機能に影響
- [ ] **High** - 広範囲に影響

## 完了条件

- [ ] コード実装完了
- [ ] テスト実行完了
- [ ] 動作確認完了

## 関連Issue

## 優先度

<!-- High/Medium/Low -->
