name: Push Cloud SQL Proxy

on:
  workflow_dispatch:
  push:
    branches:
      - main
    paths:
      - .github/workflows/push_cloud_sql_proxy.yaml
      - 'docker/cloud_sql_proxy/**'

jobs:
  push_cloud_sql_proxy:
    runs-on: ubuntu-latest
    permissions:
      id-token: write
      contents: read
    strategy:
      matrix:
        include:
          - environment: dev
            role_arn: arn:aws:iam::471112647741:role/deploy_github_actions
            repository: equtum-cloud-sql-proxy-dev
          - environment: prd
            role_arn: arn:aws:iam::211125479381:role/deploy_github_actions
            repository: equtum-cloud-sql-proxy-prd
    steps:
      - uses: actions/checkout@v5
      - name: auth
        uses: aws-actions/configure-aws-credentials@v4
        with:
          aws-region: ap-northeast-1
          role-to-assume: ${{ matrix.role_arn }}
      - name: Login to Amazon ECR
        id: login-ecr
        uses: aws-actions/amazon-ecr-login@v2
      - name: build_and_push_docker_image
        uses: docker/build-push-action@v6
        with:
          context: ./docker/cloud_sql_proxy
          file: ./docker/cloud_sql_proxy/Dockerfile
          push: true
          tags: ${{ steps.login-ecr.outputs.registry }}/${{ matrix.repository }}:latest
