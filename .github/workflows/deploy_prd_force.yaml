name: Deploy Production (Force)

on:
  workflow_dispatch:
    inputs:
      skip_db_check:
        description: 'データベースのリリースチェックをスキップする'
        type: boolean
        required: true
        default: false

jobs:
  deploy:
    uses: ./.github/workflows/deploy.yaml
    with:
      environment: 'prd'
      role_to_assume: ${{ vars.PRD_ROLE_TO_ASSUME }}
      repository: ${{ vars.PRD_REPOSITORY }}
      cluster: ${{ vars.PRD_CLUSTER }}
      service: ${{ vars.PRD_SERVICE }}
    secrets: inherit
