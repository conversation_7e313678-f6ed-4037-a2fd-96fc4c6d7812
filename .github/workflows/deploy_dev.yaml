name: deploy-dev

on:
  workflow_dispatch:
  push:
    branches:
      - main

concurrency:
  group: ${{ github.workflow }}
  cancel-in-progress: true

jobs:
  deploy-dev:
    uses: ./.github/workflows/deploy.yaml
    with:
      environment: dev
      role_to_assume: arn:aws:iam::471112647741:role/deploy_github_actions
      repository: equtum-management-api-dev
      cluster: dev-equtum-management-api
      service: api
    secrets: inherit
