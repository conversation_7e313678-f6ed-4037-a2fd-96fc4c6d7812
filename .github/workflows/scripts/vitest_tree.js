#!/usr/bin/env node
/**
 * テスト結果JSON (Jest/Vitestの --json 出力など) を標準入力で読み込み、
 * 下記の形で階層ツリーをコンソール出力します。
 *
 *  - テストファイルごとに "File: xxx" を出力
 *  - そのファイル内のすべて "assertionResults" を
 *     ancestorTitles[] + [title] の階層構造でまとめ、ASCIIツリー（├ / └）を使って表示
 *  - 失敗ケースは考慮しない場合、 .status !== "passed" はスキップするなど自由に変更
 *
 * 使い方例:
 *    cat results.json | node print_test_tree.js
 *  または
 *    pbpaste | node print_test_tree.js
 *
 * GitHub Actions など CI 環境でこのスクリプトを実行できます。
 */

/**
 * 標準入力をすべて読み込んで返すヘルパー関数
 */
function readStdin() {
  return new Promise((resolve, reject) => {
    let data = "";
    process.stdin.setEncoding("utf8");
    process.stdin.on("data", (chunk) => {
      data += chunk;
    });
    process.stdin.on("error", reject);
    process.stdin.on("end", () => resolve(data));
  });
}

/**
 * ツリー構造を表すためのノード型
 *  - name: このノードのラベル文字列
 *  - children: 下位ノードを Map で保持 (キー=子ノード名, 値=ノード)
 */
class TreeNode {
  constructor(name) {
    this.name = name;
    this.children = new Map();
  }
}

/**
 * あるノードに、 ["階層A","階層B","テスト名"] のような道筋を辿って子ノードを作り、末端を追加する
 */
function addPathToTree(root, pathArray) {
  let current = root;
  for (const part of pathArray) {
    if (!current.children.has(part)) {
      current.children.set(part, new TreeNode(part));
    }
    current = current.children.get(part);
  }
}

/**
 * TreeNodeを再帰的にたどって ASCIIツリー (├ / └) 文字で表示する
 * @param {TreeNode} node - 表示したいノード
 * @param {string} prefix - 上位ノードからのインデントや縦線など (例 "│  ")
 * @param {boolean} isLast - 同じ階層で最後のノードかどうか
 * @param {boolean} isRoot - ファイル名を表示するルートノードかどうか
 * @returns {string[]} 出力行の配列
 */
function printTree(node, prefix = "", isLast = true, isRoot = false) {
  const lines = [];

  if (isRoot) {
    // ルートノードは特別扱い: "File: xxx"
    lines.push(`${node.name}`);
  } else {
    // ルート以外は、 prefix + (├ or └) + name
    lines.push(prefix + (isLast ? "└ " : "├ ") + node.name);
  }

  const childrenArray = Array.from(node.children.values());
  const lastIndex = childrenArray.length - 1;

  // 次の階層の prefix 設定
  //   isLast なら "   " (3スペース) を追加
  //   そうでなければ "│  " などを追加
  const childPrefix = prefix + (isRoot ? "" : isLast ? "   " : "│  ");

  childrenArray.forEach((child, idx) => {
    const isChildLast = idx === lastIndex;
    lines.push(...printTree(child, childPrefix, isChildLast, false));
  });

  return lines;
}

(async () => {
  // 標準入力から JSON を読み込んでパース
  const data = await readStdin();
  const results = JSON.parse(data);

  // Jest/Vitestの結果にある testResults[] をループ
  //  各ファイルに対して "File: 名前" をルートノードとする TreeNode を作り、
  //  assertionResults[].ancestorTitles + [title] を順にツリー構造に突っ込む
  const outputLines = [];

  for (const testFile of results.testResults) {
    const prefix =
      "/home/<USER>/work/equtum-management-api/equtum-management-api/";
    const displayName = testFile.name.replace(prefix, "");
    const rootNode = new TreeNode(displayName);

    for (const assertion of testFile.assertionResults) {
      const path = [...assertion.ancestorTitles, assertion.title];
      addPathToTree(rootNode, path);
    }

    const lines = printTree(rootNode, "", true, true);
    outputLines.push(...lines, "");
  }

  // biome-ignore lint/suspicious/noConsole: デバッグ用
  console.log(outputLines.join("\n"));
})();
