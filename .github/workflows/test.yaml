name: test

on:
  push:
    branches:
      - main
  pull_request:

concurrency:
  group: ${{ github.workflow }}-${{ github.ref }}
  cancel-in-progress: true

env:
  TOTAL_SHARDS: 4

jobs:
  test:
    runs-on: ubuntu-24.04-arm-4core
    strategy:
      matrix:
        shard: [1, 2, 3, 4]
      fail-fast: false
    services:
      db:
        image: mysql:9.4.0
        ports:
          - 3306:3306
        env:
          MYSQL_DATABASE: equtum
          MYSQL_ALLOW_EMPTY_PASSWORD: 1
        options: >-
          --health-cmd "mysqladmin ping"
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
          --volume /tmp/mysql-socket:/var/run/mysql
    env:
      DATABASE_URL: mysql://root@localhost:3306/equtum
      FIREBASE_AUTH_EMULATOR_HOST: 127.0.0.1:9099
    steps:
      - name: github-app-access-token
        id: get-github-app
        uses: getsentry/action-github-app-token@v3
        with:
          app_id: ${{ secrets.GH_APP_ID }}
          private_key: ${{ secrets.GH_APP_PRIVATE_KEY }}
      - uses: actions/checkout@v5
        with:
          submodules: recursive
          token: ${{ steps.get-github-app.outputs.token }}
          fetch-depth: 0
      - name: db setup
        run: |
          curl -L https://github.com/golang-migrate/migrate/releases/download/v4.17.1/migrate.linux-arm64.tar.gz | tar xvz
          ./migrate -database "mysql://root@tcp(127.0.0.1:3306)/equtum" -path database/db/migrations up
      - name: Set up JDK 17
        uses: actions/setup-java@v4
        with:
          distribution: 'temurin'
          java-version: '17'
      - name: Install
        run: |
          npm install
      - name: generate
        run: npm run prisma:generate
      - name: Test
        id: test
        run: |
          ./docker/firebase-emulator/setup.sh
          ./docker/firebase-emulator/wait_setup.sh
          npm run test -- --shard=${{ matrix.shard }}/${{ env.TOTAL_SHARDS }}
      - name: typecheck
        run: npm run typecheck

  changed_tests:
    if: github.event_name == 'pull_request'
    runs-on: ubuntu-24.04-arm-4core
    services:
      db:
        image: mysql:9.4.0
        ports:
          - 3306:3306
        env:
          MYSQL_DATABASE: equtum
          MYSQL_ALLOW_EMPTY_PASSWORD: 1
        options: >-
          --health-cmd "mysqladmin ping"
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
          --volume /tmp/mysql-socket:/var/run/mysql
    env:
      DATABASE_URL: mysql://root@localhost:3306/equtum
      FIREBASE_AUTH_EMULATOR_HOST: 127.0.0.1:9099
    steps:
      - name: github-app-access-token
        id: get-github-app
        uses: getsentry/action-github-app-token@v3
        with:
          app_id: ${{ secrets.GH_APP_ID }}
          private_key: ${{ secrets.GH_APP_PRIVATE_KEY }}
      - uses: actions/checkout@v5
        with:
          submodules: recursive
          token: ${{ steps.get-github-app.outputs.token }}
          fetch-depth: 0
      - name: db setup
        run: |
          curl -L https://github.com/golang-migrate/migrate/releases/download/v4.17.1/migrate.linux-arm64.tar.gz | tar xvz
          ./migrate -database "mysql://root@tcp(127.0.0.1:3306)/equtum" -path database/db/migrations up
      - name: Set up JDK 17
        uses: actions/setup-java@v4
        with:
          distribution: 'temurin'
          java-version: '17'
      - name: Install
        run: |
          npm install
      - name: generate
        run: npm run prisma:generate
      - name: Get changed test files
        id: changed-files
        run: |
          CHANGED_FILES=$(git diff --name-only origin/${{ github.base_ref }} ${{ github.sha }} | grep '\.test\.ts$' | tr '\n' ' ')
          echo "files=$CHANGED_FILES" >> $GITHUB_OUTPUT
      - name: Run Changed Tests
        if: steps.changed-files.outputs.files != ''
        id: changed-tests
        run: |
          ./docker/firebase-emulator/setup.sh
          ./docker/firebase-emulator/wait_setup.sh
          echo "Running tests for changed files: ${{ steps.changed-files.outputs.files }}"
          npx vitest ${{ steps.changed-files.outputs.files }} --reporter=json --outputFile=./test-output.json
      - name: Print tree
        if: steps.changed-files.outputs.files != ''
        id: print-tree
        run: |
          TEST_TREE=$(node .github/workflows/scripts/vitest_tree.js < ./test-output.json)
          echo "test_tree<<EOF" >> $GITHUB_OUTPUT
          echo "$TEST_TREE" >> $GITHUB_OUTPUT
          echo "EOF" >> $GITHUB_OUTPUT
      - name: Post Test Results
        if: steps.changed-files.outputs.files != ''
        uses: marocchino/sticky-pull-request-comment@v2
        with:
          message: |
            ## 変更されたテスト

            ```
            ${{ steps.print-tree.outputs.test_tree }}
            ```
