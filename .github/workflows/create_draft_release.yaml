name: Release
on:
  push:
    branches:
      - main

concurrency:
  group: ${{ github.workflow }}-${{ github.ref }}
  cancel-in-progress: true

jobs:
  release:
    name: Release
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v5

      - name: Create Timestamp
        id: timestamp
        env:
          TZ: Asia/Tokyo
        run: echo tag=$(date +'%Y%m%d%H%M%S') >> $GITHUB_OUTPUT

      - name: Create Release
        run: |
          gh release create ${{ steps.timestamp.outputs.tag }} \
            --title "Release ${{ steps.timestamp.outputs.tag }}" \
            --target ${{ github.sha }} \
            --draft \
            --generate-notes
        env:
          GH_TOKEN: ${{ github.token }}
