name: deploy

on:
  workflow_call:
    inputs:
      environment:
        type: string
        required: true
      role_to_assume:
        required: true
        type: string
      repository:
        required: true
        type: string
      cluster:
        required: true
        type: string
      service:
        required: true
        type: string
      skip_db_check:
        type: boolean
        required: false
        default: false

jobs:
  deploy:
    runs-on: ubuntu-latest
    permissions:
      id-token: write
      contents: read
    environment: ${{ inputs.environment }}
    services:
      db:
        image: mysql:9.4.0
        ports:
          - 3306:3306
        env:
          MYSQL_DATABASE: equtum
          MYSQL_ALLOW_EMPTY_PASSWORD: 1
        options: >-
          --health-cmd "mysqladmin ping"
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
          --volume /tmp/mysql-socket:/var/run/mysql
    steps:
      - name: github-app-access-token
        id: get-github-app
        uses: getsentry/action-github-app-token@v3
        with:
          app_id: ${{ secrets.GH_APP_ID }}
          private_key: ${{ secrets.GH_APP_PRIVATE_KEY }}
      - uses: actions/checkout@v5
        with:
          submodules: recursive
          token: ${{ steps.get-github-app.outputs.token }}
      - name: Check Database Release
        if: ${{ inputs.environment == 'prd' && !inputs.skip_db_check }}
        env:
          GH_TOKEN: ${{ steps.get-github-app.outputs.token }}
        run: |
          cd database
          db_commit=$(git rev-parse HEAD)
          cd ..

          released_commits=$(gh api repos/abelorg/equtum-database/releases --jq '[.[].target_commitish]')
          if [[ ! $released_commits =~ $db_commit ]]; then
            echo "Error: Database commit ${db_commit} has not been released yet"
            exit 1
          fi
      - name: db setup
        run: |
          curl -L https://github.com/golang-migrate/migrate/releases/download/v4.17.1/migrate.linux-amd64.tar.gz | tar xvz
          ./migrate -database "mysql://root@tcp(127.0.0.1:3306)/equtum" -path database/db/migrations up
      - name: auth
        uses: aws-actions/configure-aws-credentials@v4
        with:
          aws-region: ap-northeast-1
          role-to-assume: ${{ inputs.role_to_assume }}
      - name: Login to Amazon ECR
        id: login-ecr
        uses: aws-actions/amazon-ecr-login@v2
      - name: build_and_push_docker_image
        uses: docker/build-push-action@v6
        with:
          context: .
          file: docker/api/deploy.Dockerfile
          push: true
          network: host
          tags: ${{ steps.login-ecr.outputs.registry }}/${{ inputs.repository }}:latest
          build-args: |
            DATABASE_URL=mysql://root@localhost:3306/equtum
      - name: deploy
        run: |
          aws ecs update-service --cluster ${{ inputs.cluster }} --service ${{ inputs.service }} --force-new-deployment
          aws ecs update-service --cluster ${{ inputs.cluster }} --service ${{ inputs.service }}-http2 --force-new-deployment
          aws ecs wait services-stable --cluster ${{ inputs.cluster }} --services ${{ inputs.service }}
          aws ecs wait services-stable --cluster ${{ inputs.cluster }} --services ${{ inputs.service }}-http2

  delete_draft_release:
    if: ${{ inputs.environment == 'prd' }}
    runs-on: ubuntu-latest
    needs: [deploy]
    permissions:
      contents: write
    steps:
      - uses: actions/checkout@v5
      - name: Get Latest release
        id: get_latest_release
        env:
          GH_TOKEN: ${{ secrets.GITHUB_TOKEN }}
        run: |
          lastPublishedRelease==$(gh release list --exclude-drafts --json=createdAt,isDraft --jq="[.[].createdAt | now - fromdate][0]")
          echo "threshold=$(echo ${lastPublishedRelease%.*})" >> $GITHUB_OUTPUT
      - name: Delete drafts
        uses: hugo19941994/delete-draft-releases@v2.0.0
        with:
          threshold: ${{ steps.get_latest_release.outputs.threshold }}s
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
