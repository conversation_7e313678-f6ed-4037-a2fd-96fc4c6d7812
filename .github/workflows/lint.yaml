name: lint

on:
  pull_request:

concurrency:
  group: ${{ github.workflow }}-${{ github.ref }}
  cancel-in-progress: true

jobs:
  lint:
    runs-on: ubuntu-latest
    permissions:
      pull-requests: write
      contents: read
    steps:
      - name: github-app-access-token
        id: get-github-app
        uses: getsentry/action-github-app-token@v3
        with:
          app_id: ${{ secrets.GH_APP_ID }}
          private_key: ${{ secrets.GH_APP_PRIVATE_KEY }}
      - uses: actions/checkout@v5
        with:
          fetch-depth: 0
          submodules: recursive
          token: ${{ steps.get-github-app.outputs.token }}

      - uses: streetsidesoftware/cspell-action@v7

      - name: Check database is in main
        working-directory: database
        run: |
          git merge-base --is-ancestor HEAD origin/main

      - name: Check schema is in main
        working-directory: schema
        run: |
          git merge-base --is-ancestor HEAD origin/main

      - uses: mongolyy/reviewdog-action-biome@v2
        with:
          github_token: ${{ secrets.github_token }}
          reporter: github-pr-review
          fail_on_error: true
