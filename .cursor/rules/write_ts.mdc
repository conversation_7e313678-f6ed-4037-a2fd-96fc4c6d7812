---
description: TypeScriptファイルを記述するときのルール
globs: *.ts
---
新しく記述したロジックには必ずテストを記述してください。また、テストのファイルの置き場所はできる限りそのファイルの近くにしてください。
例えば src/handlers/trainers/user/get_me.ts と src/handlers/trainers/user/get_me.test.ts のように、テストがすぐに参照できる位置に置きたいです。
prismaのclientが必要な際はvPrismaを使うようにして
handlerのテストを書く際は header に認証情報を渡している箇所を参考にして実装してください
認証に必要なユーザーは [fixture.ts](mdc:test/fixture.ts) と [accounts.json](mdc:docker/firebase-emulator/export/auth_export/accounts.json) に入っています 
テストを追加した後は `npm run test {テストファイルのあるディレクトリ}` でテストを実行してください
