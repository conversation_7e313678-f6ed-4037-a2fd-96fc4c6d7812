{"name": "equtum_management_api", "version": "0.0.1", "type": "module", "description": "equtum management api", "scripts": {"start": "tsx src/main.ts", "start:dev": "tsx --watch src/main.ts", "test": "NODE_ENV=test vitest --no-watch", "test:watch": "NODE_ENV=test vitest --watch", "prisma:generate": "npx prisma generate --sql", "schema:generate": "cd schema && npx buf format -w && npm run generate", "lint": "biome lint .", "lint:fix": "biome lint . --apply", "format": "biome format . --write", "check": "biome check . --apply", "biome": "npm run format && npm run lint:fix && npm run check", "typecheck": "tsc --noEmit", "postinstall": "patch-package"}, "dependencies": {"@ai-sdk/anthropic": "^2.0.3", "@ai-sdk/openai": "^2.0.13", "@aws-sdk/client-s3": "^3.624.0", "@aws-sdk/s3-request-presigner": "^3.624.0", "@bufbuild/buf": "^1.49.0", "@bufbuild/protobuf": "^2.2.3", "@bufbuild/protoc-gen-es": "^2.2.3", "@connectrpc/connect": "^2.0.1", "@connectrpc/connect-fastify": "^2.0.1", "@connectrpc/connect-node": "^2.0.1", "@fastify/cors": "^11.1.0", "@fastify/middie": "^9.0.3", "@fastify/request-context": "^6.0.1", "@notionhq/client": "^4.0.0", "@prisma/client": "^6.0.1", "@sendgrid/mail": "^8.1.3", "@sentry/node": "^10.0.0", "@sentry/profiling-node": "^10.0.0", "@slack/web-api": "^7.3.4", "@types/connect": "^3.4.38", "ai": "^5.0.13", "crypto": "^1.0.1", "dotenv": "^17.0.0", "fastify": "^5.2.1", "firebase-admin": "^13.0.0", "fractional-indexing": "^3.2.0", "luxon": "^3.5.0", "nanoid": "^5.0.7", "patch-package": "^8.0.0", "uuid": "^11.0.0"}, "devDependencies": {"@apify/tsconfig": "^0.1.0", "@biomejs/biome": "2.2.0", "@quramy/jest-prisma": "^1.8.1", "@quramy/prisma-fabbrica": "^2.2.2", "@types/luxon": "^3.4.2", "@types/uuid": "^10.0.0", "firebase-tools": "^14.0.0", "prisma": "^6.0.0", "tsx": "^4.15.7", "typescript": "^5.5.2", "vitest": "^3.0.5"}, "overrides": {"whatwg-url": "^14.0.0"}, "author": "siu", "license": "ISC"}