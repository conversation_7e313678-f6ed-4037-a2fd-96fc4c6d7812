import "@fastify/request-context";
import type { StableWithStableStatus } from "@/repositories/stable_repository";
import type { User } from "@/repositories/user_repository";
import type { Organization, Owner } from "@prisma/client";
import "fastify";
import type { App } from "firebase-admin/app";

declare module "@fastify/request-context" {
  interface RequestContextData {
    user: User | null;
    owner: Owner | null;
    stables: StableWithStableStatus[];
    organization: Organization | null;
    requestedAt: Date;
  }
}

declare module "fastify" {
  interface FastifyInstance {
    firebase: App | undefined;
  }
}
