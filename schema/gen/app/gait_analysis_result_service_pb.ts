// @generated by protoc-gen-es v2.2.3 with parameter "target=ts"
// @generated from file app/gait_analysis_result_service.proto (package connectrpc.app.v1, syntax proto3)
/* eslint-disable */

import type { GenFile, GenMessage, GenService } from "@bufbuild/protobuf/codegenv1";
import { fileDesc, messageDesc, serviceDesc } from "@bufbuild/protobuf/codegenv1";
import type { GaitAnalysisResultDto } from "./models/gait_analysis_result_dto_pb";
import { file_app_models_gait_analysis_result_dto } from "./models/gait_analysis_result_dto_pb";
import type { Message } from "@bufbuild/protobuf";

/**
 * Describes the file app/gait_analysis_result_service.proto.
 */
export const file_app_gait_analysis_result_service: GenFile = /*@__PURE__*/
  fileDesc("CiZhcHAvZ2FpdF9hbmFseXNpc19yZXN1bHRfc2VydmljZS5wcm90bxIRY29ubmVjdHJwYy5hcHAudjEiNAodTGlzdEdhaXRBbmFseXNpc1Jlc3VsdFJlcXVlc3QSEwoLdHJhaW5pbmdfaWQYASABKAUihgEKHkxpc3RHYWl0QW5hbHlzaXNSZXN1bHRSZXNwb25zZRJHChVnYWl0X2FuYWx5c2lzX3Jlc3VsdHMYASADKAsyKC5jb25uZWN0cnBjLmFwcC52MS5HYWl0QW5hbHlzaXNSZXN1bHREdG8SGwoTYWxsX3BlcmlvZF9zdGFydF9hdBgCIAEoCTKaAQoZR2FpdEFuYWx5c2lzUmVzdWx0U2VydmljZRJ9ChZMaXN0R2FpdEFuYWx5c2lzUmVzdWx0EjAuY29ubmVjdHJwYy5hcHAudjEuTGlzdEdhaXRBbmFseXNpc1Jlc3VsdFJlcXVlc3QaMS5jb25uZWN0cnBjLmFwcC52MS5MaXN0R2FpdEFuYWx5c2lzUmVzdWx0UmVzcG9uc2ViBnByb3RvMw", [file_app_models_gait_analysis_result_dto]);

/**
 * @generated from message connectrpc.app.v1.ListGaitAnalysisResultRequest
 */
export type ListGaitAnalysisResultRequest = Message<"connectrpc.app.v1.ListGaitAnalysisResultRequest"> & {
  /**
   * @generated from field: int32 training_id = 1;
   */
  trainingId: number;
};

/**
 * Describes the message connectrpc.app.v1.ListGaitAnalysisResultRequest.
 * Use `create(ListGaitAnalysisResultRequestSchema)` to create a new message.
 */
export const ListGaitAnalysisResultRequestSchema: GenMessage<ListGaitAnalysisResultRequest> = /*@__PURE__*/
  messageDesc(file_app_gait_analysis_result_service, 0);

/**
 * @generated from message connectrpc.app.v1.ListGaitAnalysisResultResponse
 */
export type ListGaitAnalysisResultResponse = Message<"connectrpc.app.v1.ListGaitAnalysisResultResponse"> & {
  /**
   * @generated from field: repeated connectrpc.app.v1.GaitAnalysisResultDto gait_analysis_results = 1;
   */
  gaitAnalysisResults: GaitAnalysisResultDto[];

  /**
   * @generated from field: string all_period_start_at = 2;
   */
  allPeriodStartAt: string;
};

/**
 * Describes the message connectrpc.app.v1.ListGaitAnalysisResultResponse.
 * Use `create(ListGaitAnalysisResultResponseSchema)` to create a new message.
 */
export const ListGaitAnalysisResultResponseSchema: GenMessage<ListGaitAnalysisResultResponse> = /*@__PURE__*/
  messageDesc(file_app_gait_analysis_result_service, 1);

/**
 * @generated from service connectrpc.app.v1.GaitAnalysisResultService
 */
export const GaitAnalysisResultService: GenService<{
  /**
   * @generated from rpc connectrpc.app.v1.GaitAnalysisResultService.ListGaitAnalysisResult
   */
  listGaitAnalysisResult: {
    methodKind: "unary";
    input: typeof ListGaitAnalysisResultRequestSchema;
    output: typeof ListGaitAnalysisResultResponseSchema;
  },
}> = /*@__PURE__*/
  serviceDesc(file_app_gait_analysis_result_service, 0);

