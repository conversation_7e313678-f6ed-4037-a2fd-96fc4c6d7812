// @generated by protoc-gen-es v2.2.3 with parameter "target=ts"
// @generated from file app/time_series_heart_beat_result_service.proto (package connectrpc.app.v1, syntax proto3)
/* eslint-disable */

import type { GenFile, GenMessage, GenService } from "@bufbuild/protobuf/codegenv1";
import { fileDesc, messageDesc, serviceDesc } from "@bufbuild/protobuf/codegenv1";
import type { TimeSeriesHeartBeatResultDto } from "./models/time_series_heart_beat_result_dto_pb";
import { file_app_models_time_series_heart_beat_result_dto } from "./models/time_series_heart_beat_result_dto_pb";
import type { Message } from "@bufbuild/protobuf";

/**
 * Describes the file app/time_series_heart_beat_result_service.proto.
 */
export const file_app_time_series_heart_beat_result_service: GenFile = /*@__PURE__*/
  fileDesc("Ci9hcHAvdGltZV9zZXJpZXNfaGVhcnRfYmVhdF9yZXN1bHRfc2VydmljZS5wcm90bxIRY29ubmVjdHJwYy5hcHAudjEiOwokTGlzdFRpbWVTZXJpZXNIZWFydEJlYXRSZXN1bHRSZXF1ZXN0EhMKC3RyYWluaW5nX2lkGAEgASgFIoABCiVMaXN0VGltZVNlcmllc0hlYXJ0QmVhdFJlc3VsdFJlc3BvbnNlElcKHnRpbWVfc2VyaWVzX2hlYXJ0X2JlYXRfcmVzdWx0cxgBIAMoCzIvLmNvbm5lY3RycGMuYXBwLnYxLlRpbWVTZXJpZXNIZWFydEJlYXRSZXN1bHREdG8ytwEKIFRpbWVTZXJpZXNIZWFydEJlYXRSZXN1bHRTZXJ2aWNlEpIBCh1MaXN0VGltZVNlcmllc0hlYXJ0QmVhdFJlc3VsdBI3LmNvbm5lY3RycGMuYXBwLnYxLkxpc3RUaW1lU2VyaWVzSGVhcnRCZWF0UmVzdWx0UmVxdWVzdBo4LmNvbm5lY3RycGMuYXBwLnYxLkxpc3RUaW1lU2VyaWVzSGVhcnRCZWF0UmVzdWx0UmVzcG9uc2ViBnByb3RvMw", [file_app_models_time_series_heart_beat_result_dto]);

/**
 * @generated from message connectrpc.app.v1.ListTimeSeriesHeartBeatResultRequest
 */
export type ListTimeSeriesHeartBeatResultRequest = Message<"connectrpc.app.v1.ListTimeSeriesHeartBeatResultRequest"> & {
  /**
   * @generated from field: int32 training_id = 1;
   */
  trainingId: number;
};

/**
 * Describes the message connectrpc.app.v1.ListTimeSeriesHeartBeatResultRequest.
 * Use `create(ListTimeSeriesHeartBeatResultRequestSchema)` to create a new message.
 */
export const ListTimeSeriesHeartBeatResultRequestSchema: GenMessage<ListTimeSeriesHeartBeatResultRequest> = /*@__PURE__*/
  messageDesc(file_app_time_series_heart_beat_result_service, 0);

/**
 * @generated from message connectrpc.app.v1.ListTimeSeriesHeartBeatResultResponse
 */
export type ListTimeSeriesHeartBeatResultResponse = Message<"connectrpc.app.v1.ListTimeSeriesHeartBeatResultResponse"> & {
  /**
   * @generated from field: repeated connectrpc.app.v1.TimeSeriesHeartBeatResultDto time_series_heart_beat_results = 1;
   */
  timeSeriesHeartBeatResults: TimeSeriesHeartBeatResultDto[];
};

/**
 * Describes the message connectrpc.app.v1.ListTimeSeriesHeartBeatResultResponse.
 * Use `create(ListTimeSeriesHeartBeatResultResponseSchema)` to create a new message.
 */
export const ListTimeSeriesHeartBeatResultResponseSchema: GenMessage<ListTimeSeriesHeartBeatResultResponse> = /*@__PURE__*/
  messageDesc(file_app_time_series_heart_beat_result_service, 1);

/**
 * @generated from service connectrpc.app.v1.TimeSeriesHeartBeatResultService
 */
export const TimeSeriesHeartBeatResultService: GenService<{
  /**
   * @generated from rpc connectrpc.app.v1.TimeSeriesHeartBeatResultService.ListTimeSeriesHeartBeatResult
   */
  listTimeSeriesHeartBeatResult: {
    methodKind: "unary";
    input: typeof ListTimeSeriesHeartBeatResultRequestSchema;
    output: typeof ListTimeSeriesHeartBeatResultResponseSchema;
  },
}> = /*@__PURE__*/
  serviceDesc(file_app_time_series_heart_beat_result_service, 0);

