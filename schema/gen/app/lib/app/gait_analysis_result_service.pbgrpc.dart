//
//  Generated code. Do not modify.
//  source: app/gait_analysis_result_service.proto
//
// @dart = 2.12

// ignore_for_file: annotate_overrides, camel_case_types, comment_references
// ignore_for_file: constant_identifier_names, library_prefixes
// ignore_for_file: non_constant_identifier_names, prefer_final_fields
// ignore_for_file: unnecessary_import, unnecessary_this, unused_import

import 'dart:async' as $async;
import 'dart:core' as $core;

import 'package:grpc/service_api.dart' as $grpc;
import 'package:protobuf/protobuf.dart' as $pb;

import 'gait_analysis_result_service.pb.dart' as $0;

export 'gait_analysis_result_service.pb.dart';

@$pb.GrpcServiceName('connectrpc.app.v1.GaitAnalysisResultService')
class GaitAnalysisResultServiceClient extends $grpc.Client {
  static final _$listGaitAnalysisResult = $grpc.ClientMethod<$0.ListGaitAnalysisResultRequest, $0.ListGaitAnalysisResultResponse>(
      '/connectrpc.app.v1.GaitAnalysisResultService/ListGaitAnalysisResult',
      ($0.ListGaitAnalysisResultRequest value) => value.writeToBuffer(),
      ($core.List<$core.int> value) => $0.ListGaitAnalysisResultResponse.fromBuffer(value));

  GaitAnalysisResultServiceClient($grpc.ClientChannel channel,
      {$grpc.CallOptions? options,
      $core.Iterable<$grpc.ClientInterceptor>? interceptors})
      : super(channel, options: options,
        interceptors: interceptors);

  $grpc.ResponseFuture<$0.ListGaitAnalysisResultResponse> listGaitAnalysisResult($0.ListGaitAnalysisResultRequest request, {$grpc.CallOptions? options}) {
    return $createUnaryCall(_$listGaitAnalysisResult, request, options: options);
  }
}

@$pb.GrpcServiceName('connectrpc.app.v1.GaitAnalysisResultService')
abstract class GaitAnalysisResultServiceBase extends $grpc.Service {
  $core.String get $name => 'connectrpc.app.v1.GaitAnalysisResultService';

  GaitAnalysisResultServiceBase() {
    $addMethod($grpc.ServiceMethod<$0.ListGaitAnalysisResultRequest, $0.ListGaitAnalysisResultResponse>(
        'ListGaitAnalysisResult',
        listGaitAnalysisResult_Pre,
        false,
        false,
        ($core.List<$core.int> value) => $0.ListGaitAnalysisResultRequest.fromBuffer(value),
        ($0.ListGaitAnalysisResultResponse value) => value.writeToBuffer()));
  }

  $async.Future<$0.ListGaitAnalysisResultResponse> listGaitAnalysisResult_Pre($grpc.ServiceCall call, $async.Future<$0.ListGaitAnalysisResultRequest> request) async {
    return listGaitAnalysisResult(call, await request);
  }

  $async.Future<$0.ListGaitAnalysisResultResponse> listGaitAnalysisResult($grpc.ServiceCall call, $0.ListGaitAnalysisResultRequest request);
}
