//
//  Generated code. Do not modify.
//  source: app/time_series_heart_beat_result_service.proto
//
// @dart = 2.12

// ignore_for_file: annotate_overrides, camel_case_types, comment_references
// ignore_for_file: constant_identifier_names, library_prefixes
// ignore_for_file: non_constant_identifier_names, prefer_final_fields
// ignore_for_file: unnecessary_import, unnecessary_this, unused_import

import 'dart:convert' as $convert;
import 'dart:core' as $core;
import 'dart:typed_data' as $typed_data;

@$core.Deprecated('Use listTimeSeriesHeartBeatResultRequestDescriptor instead')
const ListTimeSeriesHeartBeatResultRequest$json = {
  '1': 'ListTimeSeriesHeartBeatResultRequest',
  '2': [
    {'1': 'training_id', '3': 1, '4': 1, '5': 5, '10': 'trainingId'},
  ],
};

/// Descriptor for `ListTimeSeriesHeartBeatResultRequest`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List listTimeSeriesHeartBeatResultRequestDescriptor = $convert.base64Decode(
    'CiRMaXN0VGltZVNlcmllc0hlYXJ0QmVhdFJlc3VsdFJlcXVlc3QSHwoLdHJhaW5pbmdfaWQYAS'
    'ABKAVSCnRyYWluaW5nSWQ=');

@$core.Deprecated('Use listTimeSeriesHeartBeatResultResponseDescriptor instead')
const ListTimeSeriesHeartBeatResultResponse$json = {
  '1': 'ListTimeSeriesHeartBeatResultResponse',
  '2': [
    {'1': 'time_series_heart_beat_results', '3': 1, '4': 3, '5': 11, '6': '.connectrpc.app.v1.TimeSeriesHeartBeatResultDto', '10': 'timeSeriesHeartBeatResults'},
  ],
};

/// Descriptor for `ListTimeSeriesHeartBeatResultResponse`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List listTimeSeriesHeartBeatResultResponseDescriptor = $convert.base64Decode(
    'CiVMaXN0VGltZVNlcmllc0hlYXJ0QmVhdFJlc3VsdFJlc3BvbnNlEnMKHnRpbWVfc2VyaWVzX2'
    'hlYXJ0X2JlYXRfcmVzdWx0cxgBIAMoCzIvLmNvbm5lY3RycGMuYXBwLnYxLlRpbWVTZXJpZXNI'
    'ZWFydEJlYXRSZXN1bHREdG9SGnRpbWVTZXJpZXNIZWFydEJlYXRSZXN1bHRz');

