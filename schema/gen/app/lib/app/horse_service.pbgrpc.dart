//
//  Generated code. Do not modify.
//  source: app/horse_service.proto
//
// @dart = 2.12

// ignore_for_file: annotate_overrides, camel_case_types, comment_references
// ignore_for_file: constant_identifier_names, library_prefixes
// ignore_for_file: non_constant_identifier_names, prefer_final_fields
// ignore_for_file: unnecessary_import, unnecessary_this, unused_import

import 'dart:async' as $async;
import 'dart:core' as $core;

import 'package:grpc/service_api.dart' as $grpc;
import 'package:protobuf/protobuf.dart' as $pb;

import 'horse_service.pb.dart' as $1;

export 'horse_service.pb.dart';

@$pb.GrpcServiceName('connectrpc.app.v1.HorseService')
class HorseServiceClient extends $grpc.Client {
  static final _$createHorseFromMasterHorse = $grpc.ClientMethod<$1.CreateHorseFromMasterHorseRequest, $1.CreateHorseFromMasterHorseResponse>(
      '/connectrpc.app.v1.HorseService/CreateHorseFromMasterHorse',
      ($1.CreateHorseFromMasterHorseRequest value) => value.writeToBuffer(),
      ($core.List<$core.int> value) => $1.CreateHorseFromMasterHorseResponse.fromBuffer(value));
  static final _$createHorse = $grpc.ClientMethod<$1.CreateHorseRequest, $1.CreateHorseResponse>(
      '/connectrpc.app.v1.HorseService/CreateHorse',
      ($1.CreateHorseRequest value) => value.writeToBuffer(),
      ($core.List<$core.int> value) => $1.CreateHorseResponse.fromBuffer(value));
  static final _$listHorses = $grpc.ClientMethod<$1.ListHorsesRequest, $1.ListHorsesResponse>(
      '/connectrpc.app.v1.HorseService/ListHorses',
      ($1.ListHorsesRequest value) => value.writeToBuffer(),
      ($core.List<$core.int> value) => $1.ListHorsesResponse.fromBuffer(value));
  static final _$getHorse = $grpc.ClientMethod<$1.GetHorseRequest, $1.GetHorseResponse>(
      '/connectrpc.app.v1.HorseService/GetHorse',
      ($1.GetHorseRequest value) => value.writeToBuffer(),
      ($core.List<$core.int> value) => $1.GetHorseResponse.fromBuffer(value));
  static final _$patchHorseManageStatus = $grpc.ClientMethod<$1.PatchHorseManageStatusRequest, $1.PatchHorseManageStatusResponse>(
      '/connectrpc.app.v1.HorseService/PatchHorseManageStatus',
      ($1.PatchHorseManageStatusRequest value) => value.writeToBuffer(),
      ($core.List<$core.int> value) => $1.PatchHorseManageStatusResponse.fromBuffer(value));
  static final _$patchHorse = $grpc.ClientMethod<$1.PatchHorseRequest, $1.PatchHorseResponse>(
      '/connectrpc.app.v1.HorseService/PatchHorse',
      ($1.PatchHorseRequest value) => value.writeToBuffer(),
      ($core.List<$core.int> value) => $1.PatchHorseResponse.fromBuffer(value));
  static final _$transferStable = $grpc.ClientMethod<$1.TransferStableRequest, $1.TransferStableResponse>(
      '/connectrpc.app.v1.HorseService/TransferStable',
      ($1.TransferStableRequest value) => value.writeToBuffer(),
      ($core.List<$core.int> value) => $1.TransferStableResponse.fromBuffer(value));
  static final _$deleteHorse = $grpc.ClientMethod<$1.DeleteHorseRequest, $1.DeleteHorseResponse>(
      '/connectrpc.app.v1.HorseService/DeleteHorse',
      ($1.DeleteHorseRequest value) => value.writeToBuffer(),
      ($core.List<$core.int> value) => $1.DeleteHorseResponse.fromBuffer(value));
  static final _$getHorseTrainingScores = $grpc.ClientMethod<$1.GetHorseTrainingScoresRequest, $1.GetHorseTrainingScoresResponse>(
      '/connectrpc.app.v1.HorseService/GetHorseTrainingScores',
      ($1.GetHorseTrainingScoresRequest value) => value.writeToBuffer(),
      ($core.List<$core.int> value) => $1.GetHorseTrainingScoresResponse.fromBuffer(value));

  HorseServiceClient($grpc.ClientChannel channel,
      {$grpc.CallOptions? options,
      $core.Iterable<$grpc.ClientInterceptor>? interceptors})
      : super(channel, options: options,
        interceptors: interceptors);

  $grpc.ResponseFuture<$1.CreateHorseFromMasterHorseResponse> createHorseFromMasterHorse($1.CreateHorseFromMasterHorseRequest request, {$grpc.CallOptions? options}) {
    return $createUnaryCall(_$createHorseFromMasterHorse, request, options: options);
  }

  $grpc.ResponseFuture<$1.CreateHorseResponse> createHorse($1.CreateHorseRequest request, {$grpc.CallOptions? options}) {
    return $createUnaryCall(_$createHorse, request, options: options);
  }

  $grpc.ResponseFuture<$1.ListHorsesResponse> listHorses($1.ListHorsesRequest request, {$grpc.CallOptions? options}) {
    return $createUnaryCall(_$listHorses, request, options: options);
  }

  $grpc.ResponseFuture<$1.GetHorseResponse> getHorse($1.GetHorseRequest request, {$grpc.CallOptions? options}) {
    return $createUnaryCall(_$getHorse, request, options: options);
  }

  $grpc.ResponseFuture<$1.PatchHorseManageStatusResponse> patchHorseManageStatus($1.PatchHorseManageStatusRequest request, {$grpc.CallOptions? options}) {
    return $createUnaryCall(_$patchHorseManageStatus, request, options: options);
  }

  $grpc.ResponseFuture<$1.PatchHorseResponse> patchHorse($1.PatchHorseRequest request, {$grpc.CallOptions? options}) {
    return $createUnaryCall(_$patchHorse, request, options: options);
  }

  $grpc.ResponseFuture<$1.TransferStableResponse> transferStable($1.TransferStableRequest request, {$grpc.CallOptions? options}) {
    return $createUnaryCall(_$transferStable, request, options: options);
  }

  $grpc.ResponseFuture<$1.DeleteHorseResponse> deleteHorse($1.DeleteHorseRequest request, {$grpc.CallOptions? options}) {
    return $createUnaryCall(_$deleteHorse, request, options: options);
  }

  $grpc.ResponseFuture<$1.GetHorseTrainingScoresResponse> getHorseTrainingScores($1.GetHorseTrainingScoresRequest request, {$grpc.CallOptions? options}) {
    return $createUnaryCall(_$getHorseTrainingScores, request, options: options);
  }
}

@$pb.GrpcServiceName('connectrpc.app.v1.HorseService')
abstract class HorseServiceBase extends $grpc.Service {
  $core.String get $name => 'connectrpc.app.v1.HorseService';

  HorseServiceBase() {
    $addMethod($grpc.ServiceMethod<$1.CreateHorseFromMasterHorseRequest, $1.CreateHorseFromMasterHorseResponse>(
        'CreateHorseFromMasterHorse',
        createHorseFromMasterHorse_Pre,
        false,
        false,
        ($core.List<$core.int> value) => $1.CreateHorseFromMasterHorseRequest.fromBuffer(value),
        ($1.CreateHorseFromMasterHorseResponse value) => value.writeToBuffer()));
    $addMethod($grpc.ServiceMethod<$1.CreateHorseRequest, $1.CreateHorseResponse>(
        'CreateHorse',
        createHorse_Pre,
        false,
        false,
        ($core.List<$core.int> value) => $1.CreateHorseRequest.fromBuffer(value),
        ($1.CreateHorseResponse value) => value.writeToBuffer()));
    $addMethod($grpc.ServiceMethod<$1.ListHorsesRequest, $1.ListHorsesResponse>(
        'ListHorses',
        listHorses_Pre,
        false,
        false,
        ($core.List<$core.int> value) => $1.ListHorsesRequest.fromBuffer(value),
        ($1.ListHorsesResponse value) => value.writeToBuffer()));
    $addMethod($grpc.ServiceMethod<$1.GetHorseRequest, $1.GetHorseResponse>(
        'GetHorse',
        getHorse_Pre,
        false,
        false,
        ($core.List<$core.int> value) => $1.GetHorseRequest.fromBuffer(value),
        ($1.GetHorseResponse value) => value.writeToBuffer()));
    $addMethod($grpc.ServiceMethod<$1.PatchHorseManageStatusRequest, $1.PatchHorseManageStatusResponse>(
        'PatchHorseManageStatus',
        patchHorseManageStatus_Pre,
        false,
        false,
        ($core.List<$core.int> value) => $1.PatchHorseManageStatusRequest.fromBuffer(value),
        ($1.PatchHorseManageStatusResponse value) => value.writeToBuffer()));
    $addMethod($grpc.ServiceMethod<$1.PatchHorseRequest, $1.PatchHorseResponse>(
        'PatchHorse',
        patchHorse_Pre,
        false,
        false,
        ($core.List<$core.int> value) => $1.PatchHorseRequest.fromBuffer(value),
        ($1.PatchHorseResponse value) => value.writeToBuffer()));
    $addMethod($grpc.ServiceMethod<$1.TransferStableRequest, $1.TransferStableResponse>(
        'TransferStable',
        transferStable_Pre,
        false,
        false,
        ($core.List<$core.int> value) => $1.TransferStableRequest.fromBuffer(value),
        ($1.TransferStableResponse value) => value.writeToBuffer()));
    $addMethod($grpc.ServiceMethod<$1.DeleteHorseRequest, $1.DeleteHorseResponse>(
        'DeleteHorse',
        deleteHorse_Pre,
        false,
        false,
        ($core.List<$core.int> value) => $1.DeleteHorseRequest.fromBuffer(value),
        ($1.DeleteHorseResponse value) => value.writeToBuffer()));
    $addMethod($grpc.ServiceMethod<$1.GetHorseTrainingScoresRequest, $1.GetHorseTrainingScoresResponse>(
        'GetHorseTrainingScores',
        getHorseTrainingScores_Pre,
        false,
        false,
        ($core.List<$core.int> value) => $1.GetHorseTrainingScoresRequest.fromBuffer(value),
        ($1.GetHorseTrainingScoresResponse value) => value.writeToBuffer()));
  }

  $async.Future<$1.CreateHorseFromMasterHorseResponse> createHorseFromMasterHorse_Pre($grpc.ServiceCall call, $async.Future<$1.CreateHorseFromMasterHorseRequest> request) async {
    return createHorseFromMasterHorse(call, await request);
  }

  $async.Future<$1.CreateHorseResponse> createHorse_Pre($grpc.ServiceCall call, $async.Future<$1.CreateHorseRequest> request) async {
    return createHorse(call, await request);
  }

  $async.Future<$1.ListHorsesResponse> listHorses_Pre($grpc.ServiceCall call, $async.Future<$1.ListHorsesRequest> request) async {
    return listHorses(call, await request);
  }

  $async.Future<$1.GetHorseResponse> getHorse_Pre($grpc.ServiceCall call, $async.Future<$1.GetHorseRequest> request) async {
    return getHorse(call, await request);
  }

  $async.Future<$1.PatchHorseManageStatusResponse> patchHorseManageStatus_Pre($grpc.ServiceCall call, $async.Future<$1.PatchHorseManageStatusRequest> request) async {
    return patchHorseManageStatus(call, await request);
  }

  $async.Future<$1.PatchHorseResponse> patchHorse_Pre($grpc.ServiceCall call, $async.Future<$1.PatchHorseRequest> request) async {
    return patchHorse(call, await request);
  }

  $async.Future<$1.TransferStableResponse> transferStable_Pre($grpc.ServiceCall call, $async.Future<$1.TransferStableRequest> request) async {
    return transferStable(call, await request);
  }

  $async.Future<$1.DeleteHorseResponse> deleteHorse_Pre($grpc.ServiceCall call, $async.Future<$1.DeleteHorseRequest> request) async {
    return deleteHorse(call, await request);
  }

  $async.Future<$1.GetHorseTrainingScoresResponse> getHorseTrainingScores_Pre($grpc.ServiceCall call, $async.Future<$1.GetHorseTrainingScoresRequest> request) async {
    return getHorseTrainingScores(call, await request);
  }

  $async.Future<$1.CreateHorseFromMasterHorseResponse> createHorseFromMasterHorse($grpc.ServiceCall call, $1.CreateHorseFromMasterHorseRequest request);
  $async.Future<$1.CreateHorseResponse> createHorse($grpc.ServiceCall call, $1.CreateHorseRequest request);
  $async.Future<$1.ListHorsesResponse> listHorses($grpc.ServiceCall call, $1.ListHorsesRequest request);
  $async.Future<$1.GetHorseResponse> getHorse($grpc.ServiceCall call, $1.GetHorseRequest request);
  $async.Future<$1.PatchHorseManageStatusResponse> patchHorseManageStatus($grpc.ServiceCall call, $1.PatchHorseManageStatusRequest request);
  $async.Future<$1.PatchHorseResponse> patchHorse($grpc.ServiceCall call, $1.PatchHorseRequest request);
  $async.Future<$1.TransferStableResponse> transferStable($grpc.ServiceCall call, $1.TransferStableRequest request);
  $async.Future<$1.DeleteHorseResponse> deleteHorse($grpc.ServiceCall call, $1.DeleteHorseRequest request);
  $async.Future<$1.GetHorseTrainingScoresResponse> getHorseTrainingScores($grpc.ServiceCall call, $1.GetHorseTrainingScoresRequest request);
}
