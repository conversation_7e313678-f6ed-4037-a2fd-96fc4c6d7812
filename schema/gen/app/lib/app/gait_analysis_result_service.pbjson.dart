//
//  Generated code. Do not modify.
//  source: app/gait_analysis_result_service.proto
//
// @dart = 2.12

// ignore_for_file: annotate_overrides, camel_case_types, comment_references
// ignore_for_file: constant_identifier_names, library_prefixes
// ignore_for_file: non_constant_identifier_names, prefer_final_fields
// ignore_for_file: unnecessary_import, unnecessary_this, unused_import

import 'dart:convert' as $convert;
import 'dart:core' as $core;
import 'dart:typed_data' as $typed_data;

@$core.Deprecated('Use listGaitAnalysisResultRequestDescriptor instead')
const ListGaitAnalysisResultRequest$json = {
  '1': 'ListGaitAnalysisResultRequest',
  '2': [
    {'1': 'training_id', '3': 1, '4': 1, '5': 5, '10': 'trainingId'},
  ],
};

/// Descriptor for `ListGaitAnalysisResultRequest`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List listGaitAnalysisResultRequestDescriptor = $convert.base64Decode(
    'Ch1MaXN0R2FpdEFuYWx5c2lzUmVzdWx0UmVxdWVzdBIfCgt0cmFpbmluZ19pZBgBIAEoBVIKdH'
    'JhaW5pbmdJZA==');

@$core.Deprecated('Use listGaitAnalysisResultResponseDescriptor instead')
const ListGaitAnalysisResultResponse$json = {
  '1': 'ListGaitAnalysisResultResponse',
  '2': [
    {'1': 'gait_analysis_results', '3': 1, '4': 3, '5': 11, '6': '.connectrpc.app.v1.GaitAnalysisResultDto', '10': 'gaitAnalysisResults'},
    {'1': 'all_period_start_at', '3': 2, '4': 1, '5': 9, '10': 'allPeriodStartAt'},
  ],
};

/// Descriptor for `ListGaitAnalysisResultResponse`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List listGaitAnalysisResultResponseDescriptor = $convert.base64Decode(
    'Ch5MaXN0R2FpdEFuYWx5c2lzUmVzdWx0UmVzcG9uc2USXAoVZ2FpdF9hbmFseXNpc19yZXN1bH'
    'RzGAEgAygLMiguY29ubmVjdHJwYy5hcHAudjEuR2FpdEFuYWx5c2lzUmVzdWx0RHRvUhNnYWl0'
    'QW5hbHlzaXNSZXN1bHRzEi0KE2FsbF9wZXJpb2Rfc3RhcnRfYXQYAiABKAlSEGFsbFBlcmlvZF'
    'N0YXJ0QXQ=');

