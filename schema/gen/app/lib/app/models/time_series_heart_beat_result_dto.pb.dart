//
//  Generated code. Do not modify.
//  source: app/models/time_series_heart_beat_result_dto.proto
//
// @dart = 2.12

// ignore_for_file: annotate_overrides, camel_case_types, comment_references
// ignore_for_file: constant_identifier_names, library_prefixes
// ignore_for_file: non_constant_identifier_names, prefer_final_fields
// ignore_for_file: unnecessary_import, unnecessary_this, unused_import

import 'dart:core' as $core;

import 'package:protobuf/protobuf.dart' as $pb;

class TimeSeriesHeartBeatResultDto extends $pb.GeneratedMessage {
  factory TimeSeriesHeartBeatResultDto({
    $core.int? time,
    $core.int? heartRate,
    $core.double? sympatheticNerve,
    $core.double? parasympatheticNerve,
  }) {
    final $result = create();
    if (time != null) {
      $result.time = time;
    }
    if (heartRate != null) {
      $result.heartRate = heartRate;
    }
    if (sympatheticNerve != null) {
      $result.sympatheticNerve = sympatheticNerve;
    }
    if (parasympatheticNerve != null) {
      $result.parasympatheticNerve = parasympatheticNerve;
    }
    return $result;
  }
  TimeSeriesHeartBeatResultDto._() : super();
  factory TimeSeriesHeartBeatResultDto.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory TimeSeriesHeartBeatResultDto.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);

  static final $pb.BuilderInfo _i = $pb.BuilderInfo(_omitMessageNames ? '' : 'TimeSeriesHeartBeatResultDto', package: const $pb.PackageName(_omitMessageNames ? '' : 'connectrpc.app.v1'), createEmptyInstance: create)
    ..a<$core.int>(1, _omitFieldNames ? '' : 'time', $pb.PbFieldType.O3)
    ..a<$core.int>(2, _omitFieldNames ? '' : 'heartRate', $pb.PbFieldType.O3)
    ..a<$core.double>(3, _omitFieldNames ? '' : 'sympatheticNerve', $pb.PbFieldType.OD)
    ..a<$core.double>(4, _omitFieldNames ? '' : 'parasympatheticNerve', $pb.PbFieldType.OD)
    ..hasRequiredFields = false
  ;

  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  TimeSeriesHeartBeatResultDto clone() => TimeSeriesHeartBeatResultDto()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  TimeSeriesHeartBeatResultDto copyWith(void Function(TimeSeriesHeartBeatResultDto) updates) => super.copyWith((message) => updates(message as TimeSeriesHeartBeatResultDto)) as TimeSeriesHeartBeatResultDto;

  $pb.BuilderInfo get info_ => _i;

  @$core.pragma('dart2js:noInline')
  static TimeSeriesHeartBeatResultDto create() => TimeSeriesHeartBeatResultDto._();
  TimeSeriesHeartBeatResultDto createEmptyInstance() => create();
  static $pb.PbList<TimeSeriesHeartBeatResultDto> createRepeated() => $pb.PbList<TimeSeriesHeartBeatResultDto>();
  @$core.pragma('dart2js:noInline')
  static TimeSeriesHeartBeatResultDto getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<TimeSeriesHeartBeatResultDto>(create);
  static TimeSeriesHeartBeatResultDto? _defaultInstance;

  @$pb.TagNumber(1)
  $core.int get time => $_getIZ(0);
  @$pb.TagNumber(1)
  set time($core.int v) { $_setSignedInt32(0, v); }
  @$pb.TagNumber(1)
  $core.bool hasTime() => $_has(0);
  @$pb.TagNumber(1)
  void clearTime() => clearField(1);

  @$pb.TagNumber(2)
  $core.int get heartRate => $_getIZ(1);
  @$pb.TagNumber(2)
  set heartRate($core.int v) { $_setSignedInt32(1, v); }
  @$pb.TagNumber(2)
  $core.bool hasHeartRate() => $_has(1);
  @$pb.TagNumber(2)
  void clearHeartRate() => clearField(2);

  @$pb.TagNumber(3)
  $core.double get sympatheticNerve => $_getN(2);
  @$pb.TagNumber(3)
  set sympatheticNerve($core.double v) { $_setDouble(2, v); }
  @$pb.TagNumber(3)
  $core.bool hasSympatheticNerve() => $_has(2);
  @$pb.TagNumber(3)
  void clearSympatheticNerve() => clearField(3);

  @$pb.TagNumber(4)
  $core.double get parasympatheticNerve => $_getN(3);
  @$pb.TagNumber(4)
  set parasympatheticNerve($core.double v) { $_setDouble(3, v); }
  @$pb.TagNumber(4)
  $core.bool hasParasympatheticNerve() => $_has(3);
  @$pb.TagNumber(4)
  void clearParasympatheticNerve() => clearField(4);
}


const _omitFieldNames = $core.bool.fromEnvironment('protobuf.omit_field_names');
const _omitMessageNames = $core.bool.fromEnvironment('protobuf.omit_message_names');
