//
//  Generated code. Do not modify.
//  source: app/models/horse_dto.proto
//
// @dart = 2.12

// ignore_for_file: annotate_overrides, camel_case_types, comment_references
// ignore_for_file: constant_identifier_names, library_prefixes
// ignore_for_file: non_constant_identifier_names, prefer_final_fields
// ignore_for_file: unnecessary_import, unnecessary_this, unused_import

import 'dart:convert' as $convert;
import 'dart:core' as $core;
import 'dart:typed_data' as $typed_data;

@$core.Deprecated('Use todaysHorseTrainingStatusDescriptor instead')
const TodaysHorseTrainingStatus$json = {
  '1': 'TodaysHorseTrainingStatus',
  '2': [
    {'1': 'TODAYS_HORSE_TRAINING_STATUS_UNSPECIFIED', '2': 0},
    {'1': 'TODAYS_HORSE_TRAINING_STATUS_BEFORE_MEASURING', '2': 1},
    {'1': 'TODAYS_HORSE_TRAINING_STATUS_IN_MEASURING', '2': 2},
    {'1': 'TODAYS_HORSE_TRAINING_STATUS_MEASURING_COMPLETED', '2': 3},
    {'1': 'TODAYS_HORSE_TRAINING_STATUS_IN_ANALYZING', '2': 4},
    {'1': 'TODAYS_HORSE_TRAINING_STATUS_ANALYZING_COMPLETED', '2': 5},
  ],
};

/// Descriptor for `TodaysHorseTrainingStatus`. Decode as a `google.protobuf.EnumDescriptorProto`.
final $typed_data.Uint8List todaysHorseTrainingStatusDescriptor = $convert.base64Decode(
    'ChlUb2RheXNIb3JzZVRyYWluaW5nU3RhdHVzEiwKKFRPREFZU19IT1JTRV9UUkFJTklOR19TVE'
    'FUVVNfVU5TUEVDSUZJRUQQABIxCi1UT0RBWVNfSE9SU0VfVFJBSU5JTkdfU1RBVFVTX0JFRk9S'
    'RV9NRUFTVVJJTkcQARItCilUT0RBWVNfSE9SU0VfVFJBSU5JTkdfU1RBVFVTX0lOX01FQVNVUk'
    'lORxACEjQKMFRPREFZU19IT1JTRV9UUkFJTklOR19TVEFUVVNfTUVBU1VSSU5HX0NPTVBMRVRF'
    'RBADEi0KKVRPREFZU19IT1JTRV9UUkFJTklOR19TVEFUVVNfSU5fQU5BTFlaSU5HEAQSNAowVE'
    '9EQVlTX0hPUlNFX1RSQUlOSU5HX1NUQVRVU19BTkFMWVpJTkdfQ09NUExFVEVEEAU=');

@$core.Deprecated('Use horseDtoDescriptor instead')
const HorseDto$json = {
  '1': 'HorseDto',
  '2': [
    {'1': 'horse_id', '3': 1, '4': 1, '5': 5, '10': 'horseId'},
    {'1': 'master_horse_id', '3': 2, '4': 1, '5': 9, '10': 'masterHorseId'},
    {'1': 'organization_uuid', '3': 3, '4': 1, '5': 9, '10': 'organizationUuid'},
    {'1': 'stable_uuid', '3': 4, '4': 1, '5': 9, '10': 'stableUuid'},
    {'1': 'name', '3': 5, '4': 1, '5': 9, '10': 'name'},
    {'1': 'gender', '3': 6, '4': 1, '5': 9, '10': 'gender'},
    {'1': 'age', '3': 7, '4': 1, '5': 5, '10': 'age'},
    {'1': 'birth_year', '3': 8, '4': 1, '5': 5, '10': 'birthYear'},
    {'1': 'in_stable', '3': 9, '4': 1, '5': 8, '10': 'inStable'},
    {'1': 'profile_pic_path', '3': 10, '4': 1, '5': 9, '10': 'profilePicPath'},
    {'1': 'manage_status', '3': 11, '4': 1, '5': 9, '10': 'manageStatus'},
    {'1': 'has_trained_today', '3': 12, '4': 1, '5': 8, '10': 'hasTrainedToday'},
    {'1': 'todays_horse_training_status', '3': 13, '4': 1, '5': 14, '6': '.connectrpc.app.v1.TodaysHorseTrainingStatus', '10': 'todaysHorseTrainingStatus'},
    {'1': 'last_trained_at', '3': 14, '4': 1, '5': 5, '9': 0, '10': 'lastTrainedAt', '17': true},
  ],
  '8': [
    {'1': '_last_trained_at'},
  ],
};

/// Descriptor for `HorseDto`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List horseDtoDescriptor = $convert.base64Decode(
    'CghIb3JzZUR0bxIZCghob3JzZV9pZBgBIAEoBVIHaG9yc2VJZBImCg9tYXN0ZXJfaG9yc2VfaW'
    'QYAiABKAlSDW1hc3RlckhvcnNlSWQSKwoRb3JnYW5pemF0aW9uX3V1aWQYAyABKAlSEG9yZ2Fu'
    'aXphdGlvblV1aWQSHwoLc3RhYmxlX3V1aWQYBCABKAlSCnN0YWJsZVV1aWQSEgoEbmFtZRgFIA'
    'EoCVIEbmFtZRIWCgZnZW5kZXIYBiABKAlSBmdlbmRlchIQCgNhZ2UYByABKAVSA2FnZRIdCgpi'
    'aXJ0aF95ZWFyGAggASgFUgliaXJ0aFllYXISGwoJaW5fc3RhYmxlGAkgASgIUghpblN0YWJsZR'
    'IoChBwcm9maWxlX3BpY19wYXRoGAogASgJUg5wcm9maWxlUGljUGF0aBIjCg1tYW5hZ2Vfc3Rh'
    'dHVzGAsgASgJUgxtYW5hZ2VTdGF0dXMSKgoRaGFzX3RyYWluZWRfdG9kYXkYDCABKAhSD2hhc1'
    'RyYWluZWRUb2RheRJtChx0b2RheXNfaG9yc2VfdHJhaW5pbmdfc3RhdHVzGA0gASgOMiwuY29u'
    'bmVjdHJwYy5hcHAudjEuVG9kYXlzSG9yc2VUcmFpbmluZ1N0YXR1c1IZdG9kYXlzSG9yc2VUcm'
    'FpbmluZ1N0YXR1cxIrCg9sYXN0X3RyYWluZWRfYXQYDiABKAVIAFINbGFzdFRyYWluZWRBdIgB'
    'AUISChBfbGFzdF90cmFpbmVkX2F0');

