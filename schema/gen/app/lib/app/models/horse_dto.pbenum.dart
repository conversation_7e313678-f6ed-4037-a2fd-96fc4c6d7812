//
//  Generated code. Do not modify.
//  source: app/models/horse_dto.proto
//
// @dart = 2.12

// ignore_for_file: annotate_overrides, camel_case_types, comment_references
// ignore_for_file: constant_identifier_names, library_prefixes
// ignore_for_file: non_constant_identifier_names, prefer_final_fields
// ignore_for_file: unnecessary_import, unnecessary_this, unused_import

import 'dart:core' as $core;

import 'package:protobuf/protobuf.dart' as $pb;

class TodaysHorseTrainingStatus extends $pb.ProtobufEnum {
  static const TodaysHorseTrainingStatus TODAYS_HORSE_TRAINING_STATUS_UNSPECIFIED = TodaysHorseTrainingStatus._(0, _omitEnumNames ? '' : 'TODAYS_HORSE_TRAINING_STATUS_UNSPECIFIED');
  static const TodaysHorseTrainingStatus TODAYS_HORSE_TRAINING_STATUS_BEFORE_MEASURING = TodaysHorseTrainingStatus._(1, _omitEnumNames ? '' : 'TODAYS_HORSE_TRAINING_STATUS_BEFORE_MEASURING');
  static const TodaysHorseTrainingStatus TODAYS_HORSE_TRAINING_STATUS_IN_MEASURING = TodaysHorseTrainingStatus._(2, _omitEnumNames ? '' : 'TODAYS_HORSE_TRAINING_STATUS_IN_MEASURING');
  static const TodaysHorseTrainingStatus TODAYS_HORSE_TRAINING_STATUS_MEASURING_COMPLETED = TodaysHorseTrainingStatus._(3, _omitEnumNames ? '' : 'TODAYS_HORSE_TRAINING_STATUS_MEASURING_COMPLETED');
  static const TodaysHorseTrainingStatus TODAYS_HORSE_TRAINING_STATUS_IN_ANALYZING = TodaysHorseTrainingStatus._(4, _omitEnumNames ? '' : 'TODAYS_HORSE_TRAINING_STATUS_IN_ANALYZING');
  static const TodaysHorseTrainingStatus TODAYS_HORSE_TRAINING_STATUS_ANALYZING_COMPLETED = TodaysHorseTrainingStatus._(5, _omitEnumNames ? '' : 'TODAYS_HORSE_TRAINING_STATUS_ANALYZING_COMPLETED');

  static const $core.List<TodaysHorseTrainingStatus> values = <TodaysHorseTrainingStatus> [
    TODAYS_HORSE_TRAINING_STATUS_UNSPECIFIED,
    TODAYS_HORSE_TRAINING_STATUS_BEFORE_MEASURING,
    TODAYS_HORSE_TRAINING_STATUS_IN_MEASURING,
    TODAYS_HORSE_TRAINING_STATUS_MEASURING_COMPLETED,
    TODAYS_HORSE_TRAINING_STATUS_IN_ANALYZING,
    TODAYS_HORSE_TRAINING_STATUS_ANALYZING_COMPLETED,
  ];

  static final $core.Map<$core.int, TodaysHorseTrainingStatus> _byValue = $pb.ProtobufEnum.initByValue(values);
  static TodaysHorseTrainingStatus? valueOf($core.int value) => _byValue[value];

  const TodaysHorseTrainingStatus._($core.int v, $core.String n) : super(v, n);
}


const _omitEnumNames = $core.bool.fromEnvironment('protobuf.omit_enum_names');
