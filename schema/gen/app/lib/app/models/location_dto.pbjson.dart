//
//  Generated code. Do not modify.
//  source: app/models/location_dto.proto
//
// @dart = 2.12

// ignore_for_file: annotate_overrides, camel_case_types, comment_references
// ignore_for_file: constant_identifier_names, library_prefixes
// ignore_for_file: non_constant_identifier_names, prefer_final_fields
// ignore_for_file: unnecessary_import, unnecessary_this, unused_import

import 'dart:convert' as $convert;
import 'dart:core' as $core;
import 'dart:typed_data' as $typed_data;

@$core.Deprecated('Use locationDtoDescriptor instead')
const LocationDto$json = {
  '1': 'LocationDto',
  '2': [
    {'1': 'latitude', '3': 1, '4': 1, '5': 2, '10': 'latitude'},
    {'1': 'longitude', '3': 2, '4': 1, '5': 2, '10': 'longitude'},
  ],
};

/// Descriptor for `LocationDto`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List locationDtoDescriptor = $convert.base64Decode(
    'CgtMb2NhdGlvbkR0bxIaCghsYXRpdHVkZRgBIAEoAlIIbGF0aXR1ZGUSHAoJbG9uZ2l0dWRlGA'
    'IgASgCUglsb25naXR1ZGU=');

