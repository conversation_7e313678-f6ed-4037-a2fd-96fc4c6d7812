//
//  Generated code. Do not modify.
//  source: app/models/horse_dto.proto
//
// @dart = 2.12

// ignore_for_file: annotate_overrides, camel_case_types, comment_references
// ignore_for_file: constant_identifier_names, library_prefixes
// ignore_for_file: non_constant_identifier_names, prefer_final_fields
// ignore_for_file: unnecessary_import, unnecessary_this, unused_import

import 'dart:core' as $core;

import 'package:protobuf/protobuf.dart' as $pb;

import 'horse_dto.pbenum.dart';

export 'horse_dto.pbenum.dart';

class HorseDto extends $pb.GeneratedMessage {
  factory HorseDto({
    $core.int? horseId,
    $core.String? masterHorseId,
    $core.String? organizationUuid,
    $core.String? stableUuid,
    $core.String? name,
    $core.String? gender,
    $core.int? age,
    $core.int? birthYear,
    $core.bool? inStable,
    $core.String? profilePicPath,
    $core.String? manageStatus,
    $core.bool? hasTrainedToday,
    TodaysHorseTrainingStatus? todaysHorseTrainingStatus,
    $core.int? lastTrainedAt,
  }) {
    final $result = create();
    if (horseId != null) {
      $result.horseId = horseId;
    }
    if (masterHorseId != null) {
      $result.masterHorseId = masterHorseId;
    }
    if (organizationUuid != null) {
      $result.organizationUuid = organizationUuid;
    }
    if (stableUuid != null) {
      $result.stableUuid = stableUuid;
    }
    if (name != null) {
      $result.name = name;
    }
    if (gender != null) {
      $result.gender = gender;
    }
    if (age != null) {
      $result.age = age;
    }
    if (birthYear != null) {
      $result.birthYear = birthYear;
    }
    if (inStable != null) {
      $result.inStable = inStable;
    }
    if (profilePicPath != null) {
      $result.profilePicPath = profilePicPath;
    }
    if (manageStatus != null) {
      $result.manageStatus = manageStatus;
    }
    if (hasTrainedToday != null) {
      $result.hasTrainedToday = hasTrainedToday;
    }
    if (todaysHorseTrainingStatus != null) {
      $result.todaysHorseTrainingStatus = todaysHorseTrainingStatus;
    }
    if (lastTrainedAt != null) {
      $result.lastTrainedAt = lastTrainedAt;
    }
    return $result;
  }
  HorseDto._() : super();
  factory HorseDto.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory HorseDto.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);

  static final $pb.BuilderInfo _i = $pb.BuilderInfo(_omitMessageNames ? '' : 'HorseDto', package: const $pb.PackageName(_omitMessageNames ? '' : 'connectrpc.app.v1'), createEmptyInstance: create)
    ..a<$core.int>(1, _omitFieldNames ? '' : 'horseId', $pb.PbFieldType.O3)
    ..aOS(2, _omitFieldNames ? '' : 'masterHorseId')
    ..aOS(3, _omitFieldNames ? '' : 'organizationUuid')
    ..aOS(4, _omitFieldNames ? '' : 'stableUuid')
    ..aOS(5, _omitFieldNames ? '' : 'name')
    ..aOS(6, _omitFieldNames ? '' : 'gender')
    ..a<$core.int>(7, _omitFieldNames ? '' : 'age', $pb.PbFieldType.O3)
    ..a<$core.int>(8, _omitFieldNames ? '' : 'birthYear', $pb.PbFieldType.O3)
    ..aOB(9, _omitFieldNames ? '' : 'inStable')
    ..aOS(10, _omitFieldNames ? '' : 'profilePicPath')
    ..aOS(11, _omitFieldNames ? '' : 'manageStatus')
    ..aOB(12, _omitFieldNames ? '' : 'hasTrainedToday')
    ..e<TodaysHorseTrainingStatus>(13, _omitFieldNames ? '' : 'todaysHorseTrainingStatus', $pb.PbFieldType.OE, defaultOrMaker: TodaysHorseTrainingStatus.TODAYS_HORSE_TRAINING_STATUS_UNSPECIFIED, valueOf: TodaysHorseTrainingStatus.valueOf, enumValues: TodaysHorseTrainingStatus.values)
    ..a<$core.int>(14, _omitFieldNames ? '' : 'lastTrainedAt', $pb.PbFieldType.O3)
    ..hasRequiredFields = false
  ;

  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  HorseDto clone() => HorseDto()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  HorseDto copyWith(void Function(HorseDto) updates) => super.copyWith((message) => updates(message as HorseDto)) as HorseDto;

  $pb.BuilderInfo get info_ => _i;

  @$core.pragma('dart2js:noInline')
  static HorseDto create() => HorseDto._();
  HorseDto createEmptyInstance() => create();
  static $pb.PbList<HorseDto> createRepeated() => $pb.PbList<HorseDto>();
  @$core.pragma('dart2js:noInline')
  static HorseDto getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<HorseDto>(create);
  static HorseDto? _defaultInstance;

  @$pb.TagNumber(1)
  $core.int get horseId => $_getIZ(0);
  @$pb.TagNumber(1)
  set horseId($core.int v) { $_setSignedInt32(0, v); }
  @$pb.TagNumber(1)
  $core.bool hasHorseId() => $_has(0);
  @$pb.TagNumber(1)
  void clearHorseId() => clearField(1);

  @$pb.TagNumber(2)
  $core.String get masterHorseId => $_getSZ(1);
  @$pb.TagNumber(2)
  set masterHorseId($core.String v) { $_setString(1, v); }
  @$pb.TagNumber(2)
  $core.bool hasMasterHorseId() => $_has(1);
  @$pb.TagNumber(2)
  void clearMasterHorseId() => clearField(2);

  @$pb.TagNumber(3)
  $core.String get organizationUuid => $_getSZ(2);
  @$pb.TagNumber(3)
  set organizationUuid($core.String v) { $_setString(2, v); }
  @$pb.TagNumber(3)
  $core.bool hasOrganizationUuid() => $_has(2);
  @$pb.TagNumber(3)
  void clearOrganizationUuid() => clearField(3);

  @$pb.TagNumber(4)
  $core.String get stableUuid => $_getSZ(3);
  @$pb.TagNumber(4)
  set stableUuid($core.String v) { $_setString(3, v); }
  @$pb.TagNumber(4)
  $core.bool hasStableUuid() => $_has(3);
  @$pb.TagNumber(4)
  void clearStableUuid() => clearField(4);

  @$pb.TagNumber(5)
  $core.String get name => $_getSZ(4);
  @$pb.TagNumber(5)
  set name($core.String v) { $_setString(4, v); }
  @$pb.TagNumber(5)
  $core.bool hasName() => $_has(4);
  @$pb.TagNumber(5)
  void clearName() => clearField(5);

  @$pb.TagNumber(6)
  $core.String get gender => $_getSZ(5);
  @$pb.TagNumber(6)
  set gender($core.String v) { $_setString(5, v); }
  @$pb.TagNumber(6)
  $core.bool hasGender() => $_has(5);
  @$pb.TagNumber(6)
  void clearGender() => clearField(6);

  @$pb.TagNumber(7)
  $core.int get age => $_getIZ(6);
  @$pb.TagNumber(7)
  set age($core.int v) { $_setSignedInt32(6, v); }
  @$pb.TagNumber(7)
  $core.bool hasAge() => $_has(6);
  @$pb.TagNumber(7)
  void clearAge() => clearField(7);

  @$pb.TagNumber(8)
  $core.int get birthYear => $_getIZ(7);
  @$pb.TagNumber(8)
  set birthYear($core.int v) { $_setSignedInt32(7, v); }
  @$pb.TagNumber(8)
  $core.bool hasBirthYear() => $_has(7);
  @$pb.TagNumber(8)
  void clearBirthYear() => clearField(8);

  @$pb.TagNumber(9)
  $core.bool get inStable => $_getBF(8);
  @$pb.TagNumber(9)
  set inStable($core.bool v) { $_setBool(8, v); }
  @$pb.TagNumber(9)
  $core.bool hasInStable() => $_has(8);
  @$pb.TagNumber(9)
  void clearInStable() => clearField(9);

  @$pb.TagNumber(10)
  $core.String get profilePicPath => $_getSZ(9);
  @$pb.TagNumber(10)
  set profilePicPath($core.String v) { $_setString(9, v); }
  @$pb.TagNumber(10)
  $core.bool hasProfilePicPath() => $_has(9);
  @$pb.TagNumber(10)
  void clearProfilePicPath() => clearField(10);

  @$pb.TagNumber(11)
  $core.String get manageStatus => $_getSZ(10);
  @$pb.TagNumber(11)
  set manageStatus($core.String v) { $_setString(10, v); }
  @$pb.TagNumber(11)
  $core.bool hasManageStatus() => $_has(10);
  @$pb.TagNumber(11)
  void clearManageStatus() => clearField(11);

  @$pb.TagNumber(12)
  $core.bool get hasTrainedToday => $_getBF(11);
  @$pb.TagNumber(12)
  set hasTrainedToday($core.bool v) { $_setBool(11, v); }
  @$pb.TagNumber(12)
  $core.bool hasHasTrainedToday() => $_has(11);
  @$pb.TagNumber(12)
  void clearHasTrainedToday() => clearField(12);

  @$pb.TagNumber(13)
  TodaysHorseTrainingStatus get todaysHorseTrainingStatus => $_getN(12);
  @$pb.TagNumber(13)
  set todaysHorseTrainingStatus(TodaysHorseTrainingStatus v) { setField(13, v); }
  @$pb.TagNumber(13)
  $core.bool hasTodaysHorseTrainingStatus() => $_has(12);
  @$pb.TagNumber(13)
  void clearTodaysHorseTrainingStatus() => clearField(13);

  @$pb.TagNumber(14)
  $core.int get lastTrainedAt => $_getIZ(13);
  @$pb.TagNumber(14)
  set lastTrainedAt($core.int v) { $_setSignedInt32(13, v); }
  @$pb.TagNumber(14)
  $core.bool hasLastTrainedAt() => $_has(13);
  @$pb.TagNumber(14)
  void clearLastTrainedAt() => clearField(14);
}


const _omitFieldNames = $core.bool.fromEnvironment('protobuf.omit_field_names');
const _omitMessageNames = $core.bool.fromEnvironment('protobuf.omit_message_names');
