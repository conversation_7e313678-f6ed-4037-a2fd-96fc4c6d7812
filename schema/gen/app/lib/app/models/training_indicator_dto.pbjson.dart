//
//  Generated code. Do not modify.
//  source: app/models/training_indicator_dto.proto
//
// @dart = 2.12

// ignore_for_file: annotate_overrides, camel_case_types, comment_references
// ignore_for_file: constant_identifier_names, library_prefixes
// ignore_for_file: non_constant_identifier_names, prefer_final_fields
// ignore_for_file: unnecessary_import, unnecessary_this, unused_import

import 'dart:convert' as $convert;
import 'dart:core' as $core;
import 'dart:typed_data' as $typed_data;

@$core.Deprecated('Use trainingIndicatorDtoDescriptor instead')
const TrainingIndicatorDto$json = {
  '1': 'TrainingIndicatorDto',
  '2': [
    {'1': 'training_indicator_id', '3': 1, '4': 1, '5': 5, '10': 'trainingIndicatorId'},
    {'1': 'facility_name', '3': 2, '4': 1, '5': 9, '9': 0, '10': 'facilityName', '17': true},
    {'1': 'max_heart_rate', '3': 3, '4': 1, '5': 5, '9': 1, '10': 'maxHeartRate', '17': true},
    {'1': 'thr100', '3': 4, '4': 1, '5': 5, '9': 2, '10': 'thr100', '17': true},
    {'1': 'v200', '3': 5, '4': 1, '5': 1, '9': 3, '10': 'v200', '17': true},
    {'1': 'one_minute_heart_rate', '3': 6, '4': 1, '5': 5, '9': 4, '10': 'oneMinuteHeartRate', '17': true},
    {'1': 'thirty_seconds_after_goal_heart_rate', '3': 7, '4': 1, '5': 5, '9': 5, '10': 'thirtySecondsAfterGoalHeartRate', '17': true},
    {'1': 'one_minute_after_goal_heart_rate', '3': 8, '4': 1, '5': 5, '9': 6, '10': 'oneMinuteAfterGoalHeartRate', '17': true},
    {'1': 'two_minutes_after_goal_heart_rate', '3': 9, '4': 1, '5': 5, '9': 7, '10': 'twoMinutesAfterGoalHeartRate', '17': true},
    {'1': 'two_minutes_after_goal_min_heart_rate', '3': 10, '4': 1, '5': 5, '9': 8, '10': 'twoMinutesAfterGoalMinHeartRate', '17': true},
    {'1': 'three_minutes_min_heart_rate', '3': 11, '4': 1, '5': 5, '9': 9, '10': 'threeMinutesMinHeartRate', '17': true},
    {'1': 'heart_rate_gap', '3': 12, '4': 1, '5': 5, '9': 10, '10': 'heartRateGap', '17': true},
  ],
  '8': [
    {'1': '_facility_name'},
    {'1': '_max_heart_rate'},
    {'1': '_thr100'},
    {'1': '_v200'},
    {'1': '_one_minute_heart_rate'},
    {'1': '_thirty_seconds_after_goal_heart_rate'},
    {'1': '_one_minute_after_goal_heart_rate'},
    {'1': '_two_minutes_after_goal_heart_rate'},
    {'1': '_two_minutes_after_goal_min_heart_rate'},
    {'1': '_three_minutes_min_heart_rate'},
    {'1': '_heart_rate_gap'},
  ],
};

/// Descriptor for `TrainingIndicatorDto`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List trainingIndicatorDtoDescriptor = $convert.base64Decode(
    'ChRUcmFpbmluZ0luZGljYXRvckR0bxIyChV0cmFpbmluZ19pbmRpY2F0b3JfaWQYASABKAVSE3'
    'RyYWluaW5nSW5kaWNhdG9ySWQSKAoNZmFjaWxpdHlfbmFtZRgCIAEoCUgAUgxmYWNpbGl0eU5h'
    'bWWIAQESKQoObWF4X2hlYXJ0X3JhdGUYAyABKAVIAVIMbWF4SGVhcnRSYXRliAEBEhsKBnRocj'
    'EwMBgEIAEoBUgCUgZ0aHIxMDCIAQESFwoEdjIwMBgFIAEoAUgDUgR2MjAwiAEBEjYKFW9uZV9t'
    'aW51dGVfaGVhcnRfcmF0ZRgGIAEoBUgEUhJvbmVNaW51dGVIZWFydFJhdGWIAQESUgokdGhpcn'
    'R5X3NlY29uZHNfYWZ0ZXJfZ29hbF9oZWFydF9yYXRlGAcgASgFSAVSH3RoaXJ0eVNlY29uZHNB'
    'ZnRlckdvYWxIZWFydFJhdGWIAQESSgogb25lX21pbnV0ZV9hZnRlcl9nb2FsX2hlYXJ0X3JhdG'
    'UYCCABKAVIBlIbb25lTWludXRlQWZ0ZXJHb2FsSGVhcnRSYXRliAEBEkwKIXR3b19taW51dGVz'
    'X2FmdGVyX2dvYWxfaGVhcnRfcmF0ZRgJIAEoBUgHUhx0d29NaW51dGVzQWZ0ZXJHb2FsSGVhcn'
    'RSYXRliAEBElMKJXR3b19taW51dGVzX2FmdGVyX2dvYWxfbWluX2hlYXJ0X3JhdGUYCiABKAVI'
    'CFIfdHdvTWludXRlc0FmdGVyR29hbE1pbkhlYXJ0UmF0ZYgBARJDChx0aHJlZV9taW51dGVzX2'
    '1pbl9oZWFydF9yYXRlGAsgASgFSAlSGHRocmVlTWludXRlc01pbkhlYXJ0UmF0ZYgBARIpCg5o'
    'ZWFydF9yYXRlX2dhcBgMIAEoBUgKUgxoZWFydFJhdGVHYXCIAQFCEAoOX2ZhY2lsaXR5X25hbW'
    'VCEQoPX21heF9oZWFydF9yYXRlQgkKB190aHIxMDBCBwoFX3YyMDBCGAoWX29uZV9taW51dGVf'
    'aGVhcnRfcmF0ZUInCiVfdGhpcnR5X3NlY29uZHNfYWZ0ZXJfZ29hbF9oZWFydF9yYXRlQiMKIV'
    '9vbmVfbWludXRlX2FmdGVyX2dvYWxfaGVhcnRfcmF0ZUIkCiJfdHdvX21pbnV0ZXNfYWZ0ZXJf'
    'Z29hbF9oZWFydF9yYXRlQigKJl90d29fbWludXRlc19hZnRlcl9nb2FsX21pbl9oZWFydF9yYX'
    'RlQh8KHV90aHJlZV9taW51dGVzX21pbl9oZWFydF9yYXRlQhEKD19oZWFydF9yYXRlX2dhcA==');

