//
//  Generated code. Do not modify.
//  source: app/models/training_indicator_dto.proto
//
// @dart = 2.12

// ignore_for_file: annotate_overrides, camel_case_types, comment_references
// ignore_for_file: constant_identifier_names, library_prefixes
// ignore_for_file: non_constant_identifier_names, prefer_final_fields
// ignore_for_file: unnecessary_import, unnecessary_this, unused_import

import 'dart:core' as $core;

import 'package:protobuf/protobuf.dart' as $pb;

class TrainingIndicatorDto extends $pb.GeneratedMessage {
  factory TrainingIndicatorDto({
    $core.int? trainingIndicatorId,
    $core.String? facilityName,
    $core.int? maxHeartRate,
    $core.int? thr100,
    $core.double? v200,
    $core.int? oneMinuteHeartRate,
    $core.int? thirtySecondsAfterGoalHeartRate,
    $core.int? oneMinuteAfterGoalHeartRate,
    $core.int? twoMinutesAfterGoalHeartRate,
    $core.int? twoMinutesAfterGoalMinHeartRate,
    $core.int? threeMinutesMinHeartRate,
    $core.int? heartRateGap,
  }) {
    final $result = create();
    if (trainingIndicatorId != null) {
      $result.trainingIndicatorId = trainingIndicatorId;
    }
    if (facilityName != null) {
      $result.facilityName = facilityName;
    }
    if (maxHeartRate != null) {
      $result.maxHeartRate = maxHeartRate;
    }
    if (thr100 != null) {
      $result.thr100 = thr100;
    }
    if (v200 != null) {
      $result.v200 = v200;
    }
    if (oneMinuteHeartRate != null) {
      $result.oneMinuteHeartRate = oneMinuteHeartRate;
    }
    if (thirtySecondsAfterGoalHeartRate != null) {
      $result.thirtySecondsAfterGoalHeartRate = thirtySecondsAfterGoalHeartRate;
    }
    if (oneMinuteAfterGoalHeartRate != null) {
      $result.oneMinuteAfterGoalHeartRate = oneMinuteAfterGoalHeartRate;
    }
    if (twoMinutesAfterGoalHeartRate != null) {
      $result.twoMinutesAfterGoalHeartRate = twoMinutesAfterGoalHeartRate;
    }
    if (twoMinutesAfterGoalMinHeartRate != null) {
      $result.twoMinutesAfterGoalMinHeartRate = twoMinutesAfterGoalMinHeartRate;
    }
    if (threeMinutesMinHeartRate != null) {
      $result.threeMinutesMinHeartRate = threeMinutesMinHeartRate;
    }
    if (heartRateGap != null) {
      $result.heartRateGap = heartRateGap;
    }
    return $result;
  }
  TrainingIndicatorDto._() : super();
  factory TrainingIndicatorDto.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory TrainingIndicatorDto.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);

  static final $pb.BuilderInfo _i = $pb.BuilderInfo(_omitMessageNames ? '' : 'TrainingIndicatorDto', package: const $pb.PackageName(_omitMessageNames ? '' : 'connectrpc.app.v1'), createEmptyInstance: create)
    ..a<$core.int>(1, _omitFieldNames ? '' : 'trainingIndicatorId', $pb.PbFieldType.O3)
    ..aOS(2, _omitFieldNames ? '' : 'facilityName')
    ..a<$core.int>(3, _omitFieldNames ? '' : 'maxHeartRate', $pb.PbFieldType.O3)
    ..a<$core.int>(4, _omitFieldNames ? '' : 'thr100', $pb.PbFieldType.O3)
    ..a<$core.double>(5, _omitFieldNames ? '' : 'v200', $pb.PbFieldType.OD)
    ..a<$core.int>(6, _omitFieldNames ? '' : 'oneMinuteHeartRate', $pb.PbFieldType.O3)
    ..a<$core.int>(7, _omitFieldNames ? '' : 'thirtySecondsAfterGoalHeartRate', $pb.PbFieldType.O3)
    ..a<$core.int>(8, _omitFieldNames ? '' : 'oneMinuteAfterGoalHeartRate', $pb.PbFieldType.O3)
    ..a<$core.int>(9, _omitFieldNames ? '' : 'twoMinutesAfterGoalHeartRate', $pb.PbFieldType.O3)
    ..a<$core.int>(10, _omitFieldNames ? '' : 'twoMinutesAfterGoalMinHeartRate', $pb.PbFieldType.O3)
    ..a<$core.int>(11, _omitFieldNames ? '' : 'threeMinutesMinHeartRate', $pb.PbFieldType.O3)
    ..a<$core.int>(12, _omitFieldNames ? '' : 'heartRateGap', $pb.PbFieldType.O3)
    ..hasRequiredFields = false
  ;

  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  TrainingIndicatorDto clone() => TrainingIndicatorDto()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  TrainingIndicatorDto copyWith(void Function(TrainingIndicatorDto) updates) => super.copyWith((message) => updates(message as TrainingIndicatorDto)) as TrainingIndicatorDto;

  $pb.BuilderInfo get info_ => _i;

  @$core.pragma('dart2js:noInline')
  static TrainingIndicatorDto create() => TrainingIndicatorDto._();
  TrainingIndicatorDto createEmptyInstance() => create();
  static $pb.PbList<TrainingIndicatorDto> createRepeated() => $pb.PbList<TrainingIndicatorDto>();
  @$core.pragma('dart2js:noInline')
  static TrainingIndicatorDto getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<TrainingIndicatorDto>(create);
  static TrainingIndicatorDto? _defaultInstance;

  @$pb.TagNumber(1)
  $core.int get trainingIndicatorId => $_getIZ(0);
  @$pb.TagNumber(1)
  set trainingIndicatorId($core.int v) { $_setSignedInt32(0, v); }
  @$pb.TagNumber(1)
  $core.bool hasTrainingIndicatorId() => $_has(0);
  @$pb.TagNumber(1)
  void clearTrainingIndicatorId() => clearField(1);

  @$pb.TagNumber(2)
  $core.String get facilityName => $_getSZ(1);
  @$pb.TagNumber(2)
  set facilityName($core.String v) { $_setString(1, v); }
  @$pb.TagNumber(2)
  $core.bool hasFacilityName() => $_has(1);
  @$pb.TagNumber(2)
  void clearFacilityName() => clearField(2);

  @$pb.TagNumber(3)
  $core.int get maxHeartRate => $_getIZ(2);
  @$pb.TagNumber(3)
  set maxHeartRate($core.int v) { $_setSignedInt32(2, v); }
  @$pb.TagNumber(3)
  $core.bool hasMaxHeartRate() => $_has(2);
  @$pb.TagNumber(3)
  void clearMaxHeartRate() => clearField(3);

  @$pb.TagNumber(4)
  $core.int get thr100 => $_getIZ(3);
  @$pb.TagNumber(4)
  set thr100($core.int v) { $_setSignedInt32(3, v); }
  @$pb.TagNumber(4)
  $core.bool hasThr100() => $_has(3);
  @$pb.TagNumber(4)
  void clearThr100() => clearField(4);

  @$pb.TagNumber(5)
  $core.double get v200 => $_getN(4);
  @$pb.TagNumber(5)
  set v200($core.double v) { $_setDouble(4, v); }
  @$pb.TagNumber(5)
  $core.bool hasV200() => $_has(4);
  @$pb.TagNumber(5)
  void clearV200() => clearField(5);

  @$pb.TagNumber(6)
  $core.int get oneMinuteHeartRate => $_getIZ(5);
  @$pb.TagNumber(6)
  set oneMinuteHeartRate($core.int v) { $_setSignedInt32(5, v); }
  @$pb.TagNumber(6)
  $core.bool hasOneMinuteHeartRate() => $_has(5);
  @$pb.TagNumber(6)
  void clearOneMinuteHeartRate() => clearField(6);

  @$pb.TagNumber(7)
  $core.int get thirtySecondsAfterGoalHeartRate => $_getIZ(6);
  @$pb.TagNumber(7)
  set thirtySecondsAfterGoalHeartRate($core.int v) { $_setSignedInt32(6, v); }
  @$pb.TagNumber(7)
  $core.bool hasThirtySecondsAfterGoalHeartRate() => $_has(6);
  @$pb.TagNumber(7)
  void clearThirtySecondsAfterGoalHeartRate() => clearField(7);

  @$pb.TagNumber(8)
  $core.int get oneMinuteAfterGoalHeartRate => $_getIZ(7);
  @$pb.TagNumber(8)
  set oneMinuteAfterGoalHeartRate($core.int v) { $_setSignedInt32(7, v); }
  @$pb.TagNumber(8)
  $core.bool hasOneMinuteAfterGoalHeartRate() => $_has(7);
  @$pb.TagNumber(8)
  void clearOneMinuteAfterGoalHeartRate() => clearField(8);

  @$pb.TagNumber(9)
  $core.int get twoMinutesAfterGoalHeartRate => $_getIZ(8);
  @$pb.TagNumber(9)
  set twoMinutesAfterGoalHeartRate($core.int v) { $_setSignedInt32(8, v); }
  @$pb.TagNumber(9)
  $core.bool hasTwoMinutesAfterGoalHeartRate() => $_has(8);
  @$pb.TagNumber(9)
  void clearTwoMinutesAfterGoalHeartRate() => clearField(9);

  @$pb.TagNumber(10)
  $core.int get twoMinutesAfterGoalMinHeartRate => $_getIZ(9);
  @$pb.TagNumber(10)
  set twoMinutesAfterGoalMinHeartRate($core.int v) { $_setSignedInt32(9, v); }
  @$pb.TagNumber(10)
  $core.bool hasTwoMinutesAfterGoalMinHeartRate() => $_has(9);
  @$pb.TagNumber(10)
  void clearTwoMinutesAfterGoalMinHeartRate() => clearField(10);

  @$pb.TagNumber(11)
  $core.int get threeMinutesMinHeartRate => $_getIZ(10);
  @$pb.TagNumber(11)
  set threeMinutesMinHeartRate($core.int v) { $_setSignedInt32(10, v); }
  @$pb.TagNumber(11)
  $core.bool hasThreeMinutesMinHeartRate() => $_has(10);
  @$pb.TagNumber(11)
  void clearThreeMinutesMinHeartRate() => clearField(11);

  @$pb.TagNumber(12)
  $core.int get heartRateGap => $_getIZ(11);
  @$pb.TagNumber(12)
  set heartRateGap($core.int v) { $_setSignedInt32(11, v); }
  @$pb.TagNumber(12)
  $core.bool hasHeartRateGap() => $_has(11);
  @$pb.TagNumber(12)
  void clearHeartRateGap() => clearField(12);
}


const _omitFieldNames = $core.bool.fromEnvironment('protobuf.omit_field_names');
const _omitMessageNames = $core.bool.fromEnvironment('protobuf.omit_message_names');
