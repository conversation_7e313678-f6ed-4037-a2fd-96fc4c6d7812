//
//  Generated code. Do not modify.
//  source: app/models/training_indicator_label_dto.proto
//
// @dart = 2.12

// ignore_for_file: annotate_overrides, camel_case_types, comment_references
// ignore_for_file: constant_identifier_names, library_prefixes
// ignore_for_file: non_constant_identifier_names, prefer_final_fields
// ignore_for_file: unnecessary_import, unnecessary_this, unused_import

import 'dart:convert' as $convert;
import 'dart:core' as $core;
import 'dart:typed_data' as $typed_data;

@$core.Deprecated('Use trainingIndicatorLabelDtoDescriptor instead')
const TrainingIndicatorLabelDto$json = {
  '1': 'TrainingIndicatorLabelDto',
  '2': [
    {'1': 'training_indicator_label_id', '3': 1, '4': 1, '5': 5, '10': 'trainingIndicatorLabelId'},
    {'1': 'label', '3': 2, '4': 1, '5': 9, '10': 'label'},
    {'1': 'time', '3': 3, '4': 1, '5': 5, '10': 'time'},
    {'1': 'distance', '3': 4, '4': 1, '5': 5, '10': 'distance'},
  ],
};

/// Descriptor for `TrainingIndicatorLabelDto`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List trainingIndicatorLabelDtoDescriptor = $convert.base64Decode(
    'ChlUcmFpbmluZ0luZGljYXRvckxhYmVsRHRvEj0KG3RyYWluaW5nX2luZGljYXRvcl9sYWJlbF'
    '9pZBgBIAEoBVIYdHJhaW5pbmdJbmRpY2F0b3JMYWJlbElkEhQKBWxhYmVsGAIgASgJUgVsYWJl'
    'bBISCgR0aW1lGAMgASgFUgR0aW1lEhoKCGRpc3RhbmNlGAQgASgFUghkaXN0YW5jZQ==');

