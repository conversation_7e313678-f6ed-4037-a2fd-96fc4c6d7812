//
//  Generated code. Do not modify.
//  source: app/models/gait_analysis_result_dto.proto
//
// @dart = 2.12

// ignore_for_file: annotate_overrides, camel_case_types, comment_references
// ignore_for_file: constant_identifier_names, library_prefixes
// ignore_for_file: non_constant_identifier_names, prefer_final_fields
// ignore_for_file: unnecessary_import, unnecessary_this, unused_import

import 'dart:core' as $core;

import 'package:protobuf/protobuf.dart' as $pb;

import 'location_dto.pb.dart' as $4;

class GaitAnalysisResultDto extends $pb.GeneratedMessage {
  factory GaitAnalysisResultDto({
    $core.String? gaitAnalysisResultUuid,
    $core.int? trainingId,
    $core.String? startAt,
    $core.String? endAt,
    $core.String? gait,
    $core.double? impactLeftFront,
    $core.double? impactRightFront,
    $core.double? impactLeftBack,
    $core.double? impactRightBack,
    $core.double? swingTimeRatioLeftFront,
    $core.double? swingTimeRatioRightFront,
    $core.double? swingTimeRatioLeftBack,
    $core.double? swingTimeRatioRightBack,
    $core.double? footOnAngleLeftFront,
    $core.double? footOnAngleRightFront,
    $core.double? footOnAngleLeftBack,
    $core.double? footOnAngleRightBack,
    $core.double? footOffAngleLeftFront,
    $core.double? footOffAngleRightFront,
    $core.double? footOffAngleLeftBack,
    $core.double? footOffAngleRightBack,
    $core.Iterable<$4.LocationDto>? locations,
  }) {
    final $result = create();
    if (gaitAnalysisResultUuid != null) {
      $result.gaitAnalysisResultUuid = gaitAnalysisResultUuid;
    }
    if (trainingId != null) {
      $result.trainingId = trainingId;
    }
    if (startAt != null) {
      $result.startAt = startAt;
    }
    if (endAt != null) {
      $result.endAt = endAt;
    }
    if (gait != null) {
      $result.gait = gait;
    }
    if (impactLeftFront != null) {
      $result.impactLeftFront = impactLeftFront;
    }
    if (impactRightFront != null) {
      $result.impactRightFront = impactRightFront;
    }
    if (impactLeftBack != null) {
      $result.impactLeftBack = impactLeftBack;
    }
    if (impactRightBack != null) {
      $result.impactRightBack = impactRightBack;
    }
    if (swingTimeRatioLeftFront != null) {
      $result.swingTimeRatioLeftFront = swingTimeRatioLeftFront;
    }
    if (swingTimeRatioRightFront != null) {
      $result.swingTimeRatioRightFront = swingTimeRatioRightFront;
    }
    if (swingTimeRatioLeftBack != null) {
      $result.swingTimeRatioLeftBack = swingTimeRatioLeftBack;
    }
    if (swingTimeRatioRightBack != null) {
      $result.swingTimeRatioRightBack = swingTimeRatioRightBack;
    }
    if (footOnAngleLeftFront != null) {
      $result.footOnAngleLeftFront = footOnAngleLeftFront;
    }
    if (footOnAngleRightFront != null) {
      $result.footOnAngleRightFront = footOnAngleRightFront;
    }
    if (footOnAngleLeftBack != null) {
      $result.footOnAngleLeftBack = footOnAngleLeftBack;
    }
    if (footOnAngleRightBack != null) {
      $result.footOnAngleRightBack = footOnAngleRightBack;
    }
    if (footOffAngleLeftFront != null) {
      $result.footOffAngleLeftFront = footOffAngleLeftFront;
    }
    if (footOffAngleRightFront != null) {
      $result.footOffAngleRightFront = footOffAngleRightFront;
    }
    if (footOffAngleLeftBack != null) {
      $result.footOffAngleLeftBack = footOffAngleLeftBack;
    }
    if (footOffAngleRightBack != null) {
      $result.footOffAngleRightBack = footOffAngleRightBack;
    }
    if (locations != null) {
      $result.locations.addAll(locations);
    }
    return $result;
  }
  GaitAnalysisResultDto._() : super();
  factory GaitAnalysisResultDto.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory GaitAnalysisResultDto.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);

  static final $pb.BuilderInfo _i = $pb.BuilderInfo(_omitMessageNames ? '' : 'GaitAnalysisResultDto', package: const $pb.PackageName(_omitMessageNames ? '' : 'connectrpc.app.v1'), createEmptyInstance: create)
    ..aOS(1, _omitFieldNames ? '' : 'gaitAnalysisResultUuid')
    ..a<$core.int>(2, _omitFieldNames ? '' : 'trainingId', $pb.PbFieldType.O3)
    ..aOS(3, _omitFieldNames ? '' : 'startAt')
    ..aOS(4, _omitFieldNames ? '' : 'endAt')
    ..aOS(5, _omitFieldNames ? '' : 'gait')
    ..a<$core.double>(6, _omitFieldNames ? '' : 'impactLeftFront', $pb.PbFieldType.OF)
    ..a<$core.double>(7, _omitFieldNames ? '' : 'impactRightFront', $pb.PbFieldType.OF)
    ..a<$core.double>(8, _omitFieldNames ? '' : 'impactLeftBack', $pb.PbFieldType.OF)
    ..a<$core.double>(9, _omitFieldNames ? '' : 'impactRightBack', $pb.PbFieldType.OF)
    ..a<$core.double>(10, _omitFieldNames ? '' : 'swingTimeRatioLeftFront', $pb.PbFieldType.OF)
    ..a<$core.double>(11, _omitFieldNames ? '' : 'swingTimeRatioRightFront', $pb.PbFieldType.OF)
    ..a<$core.double>(12, _omitFieldNames ? '' : 'swingTimeRatioLeftBack', $pb.PbFieldType.OF)
    ..a<$core.double>(13, _omitFieldNames ? '' : 'swingTimeRatioRightBack', $pb.PbFieldType.OF)
    ..a<$core.double>(14, _omitFieldNames ? '' : 'footOnAngleLeftFront', $pb.PbFieldType.OF)
    ..a<$core.double>(15, _omitFieldNames ? '' : 'footOnAngleRightFront', $pb.PbFieldType.OF)
    ..a<$core.double>(16, _omitFieldNames ? '' : 'footOnAngleLeftBack', $pb.PbFieldType.OF)
    ..a<$core.double>(17, _omitFieldNames ? '' : 'footOnAngleRightBack', $pb.PbFieldType.OF)
    ..a<$core.double>(18, _omitFieldNames ? '' : 'footOffAngleLeftFront', $pb.PbFieldType.OF)
    ..a<$core.double>(19, _omitFieldNames ? '' : 'footOffAngleRightFront', $pb.PbFieldType.OF)
    ..a<$core.double>(20, _omitFieldNames ? '' : 'footOffAngleLeftBack', $pb.PbFieldType.OF)
    ..a<$core.double>(21, _omitFieldNames ? '' : 'footOffAngleRightBack', $pb.PbFieldType.OF)
    ..pc<$4.LocationDto>(22, _omitFieldNames ? '' : 'locations', $pb.PbFieldType.PM, subBuilder: $4.LocationDto.create)
    ..hasRequiredFields = false
  ;

  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  GaitAnalysisResultDto clone() => GaitAnalysisResultDto()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  GaitAnalysisResultDto copyWith(void Function(GaitAnalysisResultDto) updates) => super.copyWith((message) => updates(message as GaitAnalysisResultDto)) as GaitAnalysisResultDto;

  $pb.BuilderInfo get info_ => _i;

  @$core.pragma('dart2js:noInline')
  static GaitAnalysisResultDto create() => GaitAnalysisResultDto._();
  GaitAnalysisResultDto createEmptyInstance() => create();
  static $pb.PbList<GaitAnalysisResultDto> createRepeated() => $pb.PbList<GaitAnalysisResultDto>();
  @$core.pragma('dart2js:noInline')
  static GaitAnalysisResultDto getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<GaitAnalysisResultDto>(create);
  static GaitAnalysisResultDto? _defaultInstance;

  @$pb.TagNumber(1)
  $core.String get gaitAnalysisResultUuid => $_getSZ(0);
  @$pb.TagNumber(1)
  set gaitAnalysisResultUuid($core.String v) { $_setString(0, v); }
  @$pb.TagNumber(1)
  $core.bool hasGaitAnalysisResultUuid() => $_has(0);
  @$pb.TagNumber(1)
  void clearGaitAnalysisResultUuid() => clearField(1);

  @$pb.TagNumber(2)
  $core.int get trainingId => $_getIZ(1);
  @$pb.TagNumber(2)
  set trainingId($core.int v) { $_setSignedInt32(1, v); }
  @$pb.TagNumber(2)
  $core.bool hasTrainingId() => $_has(1);
  @$pb.TagNumber(2)
  void clearTrainingId() => clearField(2);

  @$pb.TagNumber(3)
  $core.String get startAt => $_getSZ(2);
  @$pb.TagNumber(3)
  set startAt($core.String v) { $_setString(2, v); }
  @$pb.TagNumber(3)
  $core.bool hasStartAt() => $_has(2);
  @$pb.TagNumber(3)
  void clearStartAt() => clearField(3);

  @$pb.TagNumber(4)
  $core.String get endAt => $_getSZ(3);
  @$pb.TagNumber(4)
  set endAt($core.String v) { $_setString(3, v); }
  @$pb.TagNumber(4)
  $core.bool hasEndAt() => $_has(3);
  @$pb.TagNumber(4)
  void clearEndAt() => clearField(4);

  @$pb.TagNumber(5)
  $core.String get gait => $_getSZ(4);
  @$pb.TagNumber(5)
  set gait($core.String v) { $_setString(4, v); }
  @$pb.TagNumber(5)
  $core.bool hasGait() => $_has(4);
  @$pb.TagNumber(5)
  void clearGait() => clearField(5);

  @$pb.TagNumber(6)
  $core.double get impactLeftFront => $_getN(5);
  @$pb.TagNumber(6)
  set impactLeftFront($core.double v) { $_setFloat(5, v); }
  @$pb.TagNumber(6)
  $core.bool hasImpactLeftFront() => $_has(5);
  @$pb.TagNumber(6)
  void clearImpactLeftFront() => clearField(6);

  @$pb.TagNumber(7)
  $core.double get impactRightFront => $_getN(6);
  @$pb.TagNumber(7)
  set impactRightFront($core.double v) { $_setFloat(6, v); }
  @$pb.TagNumber(7)
  $core.bool hasImpactRightFront() => $_has(6);
  @$pb.TagNumber(7)
  void clearImpactRightFront() => clearField(7);

  @$pb.TagNumber(8)
  $core.double get impactLeftBack => $_getN(7);
  @$pb.TagNumber(8)
  set impactLeftBack($core.double v) { $_setFloat(7, v); }
  @$pb.TagNumber(8)
  $core.bool hasImpactLeftBack() => $_has(7);
  @$pb.TagNumber(8)
  void clearImpactLeftBack() => clearField(8);

  @$pb.TagNumber(9)
  $core.double get impactRightBack => $_getN(8);
  @$pb.TagNumber(9)
  set impactRightBack($core.double v) { $_setFloat(8, v); }
  @$pb.TagNumber(9)
  $core.bool hasImpactRightBack() => $_has(8);
  @$pb.TagNumber(9)
  void clearImpactRightBack() => clearField(9);

  @$pb.TagNumber(10)
  $core.double get swingTimeRatioLeftFront => $_getN(9);
  @$pb.TagNumber(10)
  set swingTimeRatioLeftFront($core.double v) { $_setFloat(9, v); }
  @$pb.TagNumber(10)
  $core.bool hasSwingTimeRatioLeftFront() => $_has(9);
  @$pb.TagNumber(10)
  void clearSwingTimeRatioLeftFront() => clearField(10);

  @$pb.TagNumber(11)
  $core.double get swingTimeRatioRightFront => $_getN(10);
  @$pb.TagNumber(11)
  set swingTimeRatioRightFront($core.double v) { $_setFloat(10, v); }
  @$pb.TagNumber(11)
  $core.bool hasSwingTimeRatioRightFront() => $_has(10);
  @$pb.TagNumber(11)
  void clearSwingTimeRatioRightFront() => clearField(11);

  @$pb.TagNumber(12)
  $core.double get swingTimeRatioLeftBack => $_getN(11);
  @$pb.TagNumber(12)
  set swingTimeRatioLeftBack($core.double v) { $_setFloat(11, v); }
  @$pb.TagNumber(12)
  $core.bool hasSwingTimeRatioLeftBack() => $_has(11);
  @$pb.TagNumber(12)
  void clearSwingTimeRatioLeftBack() => clearField(12);

  @$pb.TagNumber(13)
  $core.double get swingTimeRatioRightBack => $_getN(12);
  @$pb.TagNumber(13)
  set swingTimeRatioRightBack($core.double v) { $_setFloat(12, v); }
  @$pb.TagNumber(13)
  $core.bool hasSwingTimeRatioRightBack() => $_has(12);
  @$pb.TagNumber(13)
  void clearSwingTimeRatioRightBack() => clearField(13);

  @$pb.TagNumber(14)
  $core.double get footOnAngleLeftFront => $_getN(13);
  @$pb.TagNumber(14)
  set footOnAngleLeftFront($core.double v) { $_setFloat(13, v); }
  @$pb.TagNumber(14)
  $core.bool hasFootOnAngleLeftFront() => $_has(13);
  @$pb.TagNumber(14)
  void clearFootOnAngleLeftFront() => clearField(14);

  @$pb.TagNumber(15)
  $core.double get footOnAngleRightFront => $_getN(14);
  @$pb.TagNumber(15)
  set footOnAngleRightFront($core.double v) { $_setFloat(14, v); }
  @$pb.TagNumber(15)
  $core.bool hasFootOnAngleRightFront() => $_has(14);
  @$pb.TagNumber(15)
  void clearFootOnAngleRightFront() => clearField(15);

  @$pb.TagNumber(16)
  $core.double get footOnAngleLeftBack => $_getN(15);
  @$pb.TagNumber(16)
  set footOnAngleLeftBack($core.double v) { $_setFloat(15, v); }
  @$pb.TagNumber(16)
  $core.bool hasFootOnAngleLeftBack() => $_has(15);
  @$pb.TagNumber(16)
  void clearFootOnAngleLeftBack() => clearField(16);

  @$pb.TagNumber(17)
  $core.double get footOnAngleRightBack => $_getN(16);
  @$pb.TagNumber(17)
  set footOnAngleRightBack($core.double v) { $_setFloat(16, v); }
  @$pb.TagNumber(17)
  $core.bool hasFootOnAngleRightBack() => $_has(16);
  @$pb.TagNumber(17)
  void clearFootOnAngleRightBack() => clearField(17);

  @$pb.TagNumber(18)
  $core.double get footOffAngleLeftFront => $_getN(17);
  @$pb.TagNumber(18)
  set footOffAngleLeftFront($core.double v) { $_setFloat(17, v); }
  @$pb.TagNumber(18)
  $core.bool hasFootOffAngleLeftFront() => $_has(17);
  @$pb.TagNumber(18)
  void clearFootOffAngleLeftFront() => clearField(18);

  @$pb.TagNumber(19)
  $core.double get footOffAngleRightFront => $_getN(18);
  @$pb.TagNumber(19)
  set footOffAngleRightFront($core.double v) { $_setFloat(18, v); }
  @$pb.TagNumber(19)
  $core.bool hasFootOffAngleRightFront() => $_has(18);
  @$pb.TagNumber(19)
  void clearFootOffAngleRightFront() => clearField(19);

  @$pb.TagNumber(20)
  $core.double get footOffAngleLeftBack => $_getN(19);
  @$pb.TagNumber(20)
  set footOffAngleLeftBack($core.double v) { $_setFloat(19, v); }
  @$pb.TagNumber(20)
  $core.bool hasFootOffAngleLeftBack() => $_has(19);
  @$pb.TagNumber(20)
  void clearFootOffAngleLeftBack() => clearField(20);

  @$pb.TagNumber(21)
  $core.double get footOffAngleRightBack => $_getN(20);
  @$pb.TagNumber(21)
  set footOffAngleRightBack($core.double v) { $_setFloat(20, v); }
  @$pb.TagNumber(21)
  $core.bool hasFootOffAngleRightBack() => $_has(20);
  @$pb.TagNumber(21)
  void clearFootOffAngleRightBack() => clearField(21);

  @$pb.TagNumber(22)
  $core.List<$4.LocationDto> get locations => $_getList(21);
}


const _omitFieldNames = $core.bool.fromEnvironment('protobuf.omit_field_names');
const _omitMessageNames = $core.bool.fromEnvironment('protobuf.omit_message_names');
