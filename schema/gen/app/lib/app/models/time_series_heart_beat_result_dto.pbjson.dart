//
//  Generated code. Do not modify.
//  source: app/models/time_series_heart_beat_result_dto.proto
//
// @dart = 2.12

// ignore_for_file: annotate_overrides, camel_case_types, comment_references
// ignore_for_file: constant_identifier_names, library_prefixes
// ignore_for_file: non_constant_identifier_names, prefer_final_fields
// ignore_for_file: unnecessary_import, unnecessary_this, unused_import

import 'dart:convert' as $convert;
import 'dart:core' as $core;
import 'dart:typed_data' as $typed_data;

@$core.Deprecated('Use timeSeriesHeartBeatResultDtoDescriptor instead')
const TimeSeriesHeartBeatResultDto$json = {
  '1': 'TimeSeriesHeartBeatResultDto',
  '2': [
    {'1': 'time', '3': 1, '4': 1, '5': 5, '10': 'time'},
    {'1': 'heart_rate', '3': 2, '4': 1, '5': 5, '9': 0, '10': 'heartRate', '17': true},
    {'1': 'sympathetic_nerve', '3': 3, '4': 1, '5': 1, '9': 1, '10': 'sympatheticNerve', '17': true},
    {'1': 'parasympathetic_nerve', '3': 4, '4': 1, '5': 1, '9': 2, '10': 'parasympatheticNerve', '17': true},
  ],
  '8': [
    {'1': '_heart_rate'},
    {'1': '_sympathetic_nerve'},
    {'1': '_parasympathetic_nerve'},
  ],
};

/// Descriptor for `TimeSeriesHeartBeatResultDto`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List timeSeriesHeartBeatResultDtoDescriptor = $convert.base64Decode(
    'ChxUaW1lU2VyaWVzSGVhcnRCZWF0UmVzdWx0RHRvEhIKBHRpbWUYASABKAVSBHRpbWUSIgoKaG'
    'VhcnRfcmF0ZRgCIAEoBUgAUgloZWFydFJhdGWIAQESMAoRc3ltcGF0aGV0aWNfbmVydmUYAyAB'
    'KAFIAVIQc3ltcGF0aGV0aWNOZXJ2ZYgBARI4ChVwYXJhc3ltcGF0aGV0aWNfbmVydmUYBCABKA'
    'FIAlIUcGFyYXN5bXBhdGhldGljTmVydmWIAQFCDQoLX2hlYXJ0X3JhdGVCFAoSX3N5bXBhdGhl'
    'dGljX25lcnZlQhgKFl9wYXJhc3ltcGF0aGV0aWNfbmVydmU=');

