//
//  Generated code. Do not modify.
//  source: app/models/gait_analysis_result_dto.proto
//
// @dart = 2.12

// ignore_for_file: annotate_overrides, camel_case_types, comment_references
// ignore_for_file: constant_identifier_names, library_prefixes
// ignore_for_file: non_constant_identifier_names, prefer_final_fields
// ignore_for_file: unnecessary_import, unnecessary_this, unused_import

import 'dart:convert' as $convert;
import 'dart:core' as $core;
import 'dart:typed_data' as $typed_data;

@$core.Deprecated('Use gaitAnalysisResultDtoDescriptor instead')
const GaitAnalysisResultDto$json = {
  '1': 'GaitAnalysisResultDto',
  '2': [
    {'1': 'gait_analysis_result_uuid', '3': 1, '4': 1, '5': 9, '10': 'gaitAnalysisResultUuid'},
    {'1': 'training_id', '3': 2, '4': 1, '5': 5, '10': 'trainingId'},
    {'1': 'start_at', '3': 3, '4': 1, '5': 9, '10': 'startAt'},
    {'1': 'end_at', '3': 4, '4': 1, '5': 9, '10': 'endAt'},
    {'1': 'gait', '3': 5, '4': 1, '5': 9, '10': 'gait'},
    {'1': 'impact_left_front', '3': 6, '4': 1, '5': 2, '9': 0, '10': 'impactLeftFront', '17': true},
    {'1': 'impact_right_front', '3': 7, '4': 1, '5': 2, '9': 1, '10': 'impactRightFront', '17': true},
    {'1': 'impact_left_back', '3': 8, '4': 1, '5': 2, '9': 2, '10': 'impactLeftBack', '17': true},
    {'1': 'impact_right_back', '3': 9, '4': 1, '5': 2, '9': 3, '10': 'impactRightBack', '17': true},
    {'1': 'swing_time_ratio_left_front', '3': 10, '4': 1, '5': 2, '9': 4, '10': 'swingTimeRatioLeftFront', '17': true},
    {'1': 'swing_time_ratio_right_front', '3': 11, '4': 1, '5': 2, '9': 5, '10': 'swingTimeRatioRightFront', '17': true},
    {'1': 'swing_time_ratio_left_back', '3': 12, '4': 1, '5': 2, '9': 6, '10': 'swingTimeRatioLeftBack', '17': true},
    {'1': 'swing_time_ratio_right_back', '3': 13, '4': 1, '5': 2, '9': 7, '10': 'swingTimeRatioRightBack', '17': true},
    {'1': 'foot_on_angle_left_front', '3': 14, '4': 1, '5': 2, '9': 8, '10': 'footOnAngleLeftFront', '17': true},
    {'1': 'foot_on_angle_right_front', '3': 15, '4': 1, '5': 2, '9': 9, '10': 'footOnAngleRightFront', '17': true},
    {'1': 'foot_on_angle_left_back', '3': 16, '4': 1, '5': 2, '9': 10, '10': 'footOnAngleLeftBack', '17': true},
    {'1': 'foot_on_angle_right_back', '3': 17, '4': 1, '5': 2, '9': 11, '10': 'footOnAngleRightBack', '17': true},
    {'1': 'foot_off_angle_left_front', '3': 18, '4': 1, '5': 2, '9': 12, '10': 'footOffAngleLeftFront', '17': true},
    {'1': 'foot_off_angle_right_front', '3': 19, '4': 1, '5': 2, '9': 13, '10': 'footOffAngleRightFront', '17': true},
    {'1': 'foot_off_angle_left_back', '3': 20, '4': 1, '5': 2, '9': 14, '10': 'footOffAngleLeftBack', '17': true},
    {'1': 'foot_off_angle_right_back', '3': 21, '4': 1, '5': 2, '9': 15, '10': 'footOffAngleRightBack', '17': true},
    {'1': 'locations', '3': 22, '4': 3, '5': 11, '6': '.connectrpc.app.v1.LocationDto', '10': 'locations'},
  ],
  '8': [
    {'1': '_impact_left_front'},
    {'1': '_impact_right_front'},
    {'1': '_impact_left_back'},
    {'1': '_impact_right_back'},
    {'1': '_swing_time_ratio_left_front'},
    {'1': '_swing_time_ratio_right_front'},
    {'1': '_swing_time_ratio_left_back'},
    {'1': '_swing_time_ratio_right_back'},
    {'1': '_foot_on_angle_left_front'},
    {'1': '_foot_on_angle_right_front'},
    {'1': '_foot_on_angle_left_back'},
    {'1': '_foot_on_angle_right_back'},
    {'1': '_foot_off_angle_left_front'},
    {'1': '_foot_off_angle_right_front'},
    {'1': '_foot_off_angle_left_back'},
    {'1': '_foot_off_angle_right_back'},
  ],
};

/// Descriptor for `GaitAnalysisResultDto`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List gaitAnalysisResultDtoDescriptor = $convert.base64Decode(
    'ChVHYWl0QW5hbHlzaXNSZXN1bHREdG8SOQoZZ2FpdF9hbmFseXNpc19yZXN1bHRfdXVpZBgBIA'
    'EoCVIWZ2FpdEFuYWx5c2lzUmVzdWx0VXVpZBIfCgt0cmFpbmluZ19pZBgCIAEoBVIKdHJhaW5p'
    'bmdJZBIZCghzdGFydF9hdBgDIAEoCVIHc3RhcnRBdBIVCgZlbmRfYXQYBCABKAlSBWVuZEF0Eh'
    'IKBGdhaXQYBSABKAlSBGdhaXQSLwoRaW1wYWN0X2xlZnRfZnJvbnQYBiABKAJIAFIPaW1wYWN0'
    'TGVmdEZyb250iAEBEjEKEmltcGFjdF9yaWdodF9mcm9udBgHIAEoAkgBUhBpbXBhY3RSaWdodE'
    'Zyb250iAEBEi0KEGltcGFjdF9sZWZ0X2JhY2sYCCABKAJIAlIOaW1wYWN0TGVmdEJhY2uIAQES'
    'LwoRaW1wYWN0X3JpZ2h0X2JhY2sYCSABKAJIA1IPaW1wYWN0UmlnaHRCYWNriAEBEkEKG3N3aW'
    '5nX3RpbWVfcmF0aW9fbGVmdF9mcm9udBgKIAEoAkgEUhdzd2luZ1RpbWVSYXRpb0xlZnRGcm9u'
    'dIgBARJDChxzd2luZ190aW1lX3JhdGlvX3JpZ2h0X2Zyb250GAsgASgCSAVSGHN3aW5nVGltZV'
    'JhdGlvUmlnaHRGcm9udIgBARI/Chpzd2luZ190aW1lX3JhdGlvX2xlZnRfYmFjaxgMIAEoAkgG'
    'UhZzd2luZ1RpbWVSYXRpb0xlZnRCYWNriAEBEkEKG3N3aW5nX3RpbWVfcmF0aW9fcmlnaHRfYm'
    'FjaxgNIAEoAkgHUhdzd2luZ1RpbWVSYXRpb1JpZ2h0QmFja4gBARI7Chhmb290X29uX2FuZ2xl'
    'X2xlZnRfZnJvbnQYDiABKAJICFIUZm9vdE9uQW5nbGVMZWZ0RnJvbnSIAQESPQoZZm9vdF9vbl'
    '9hbmdsZV9yaWdodF9mcm9udBgPIAEoAkgJUhVmb290T25BbmdsZVJpZ2h0RnJvbnSIAQESOQoX'
    'Zm9vdF9vbl9hbmdsZV9sZWZ0X2JhY2sYECABKAJIClITZm9vdE9uQW5nbGVMZWZ0QmFja4gBAR'
    'I7Chhmb290X29uX2FuZ2xlX3JpZ2h0X2JhY2sYESABKAJIC1IUZm9vdE9uQW5nbGVSaWdodEJh'
    'Y2uIAQESPQoZZm9vdF9vZmZfYW5nbGVfbGVmdF9mcm9udBgSIAEoAkgMUhVmb290T2ZmQW5nbG'
    'VMZWZ0RnJvbnSIAQESPwoaZm9vdF9vZmZfYW5nbGVfcmlnaHRfZnJvbnQYEyABKAJIDVIWZm9v'
    'dE9mZkFuZ2xlUmlnaHRGcm9udIgBARI7Chhmb290X29mZl9hbmdsZV9sZWZ0X2JhY2sYFCABKA'
    'JIDlIUZm9vdE9mZkFuZ2xlTGVmdEJhY2uIAQESPQoZZm9vdF9vZmZfYW5nbGVfcmlnaHRfYmFj'
    'axgVIAEoAkgPUhVmb290T2ZmQW5nbGVSaWdodEJhY2uIAQESPAoJbG9jYXRpb25zGBYgAygLMh'
    '4uY29ubmVjdHJwYy5hcHAudjEuTG9jYXRpb25EdG9SCWxvY2F0aW9uc0IUChJfaW1wYWN0X2xl'
    'ZnRfZnJvbnRCFQoTX2ltcGFjdF9yaWdodF9mcm9udEITChFfaW1wYWN0X2xlZnRfYmFja0IUCh'
    'JfaW1wYWN0X3JpZ2h0X2JhY2tCHgocX3N3aW5nX3RpbWVfcmF0aW9fbGVmdF9mcm9udEIfCh1f'
    'c3dpbmdfdGltZV9yYXRpb19yaWdodF9mcm9udEIdChtfc3dpbmdfdGltZV9yYXRpb19sZWZ0X2'
    'JhY2tCHgocX3N3aW5nX3RpbWVfcmF0aW9fcmlnaHRfYmFja0IbChlfZm9vdF9vbl9hbmdsZV9s'
    'ZWZ0X2Zyb250QhwKGl9mb290X29uX2FuZ2xlX3JpZ2h0X2Zyb250QhoKGF9mb290X29uX2FuZ2'
    'xlX2xlZnRfYmFja0IbChlfZm9vdF9vbl9hbmdsZV9yaWdodF9iYWNrQhwKGl9mb290X29mZl9h'
    'bmdsZV9sZWZ0X2Zyb250Qh0KG19mb290X29mZl9hbmdsZV9yaWdodF9mcm9udEIbChlfZm9vdF'
    '9vZmZfYW5nbGVfbGVmdF9iYWNrQhwKGl9mb290X29mZl9hbmdsZV9yaWdodF9iYWNr');

