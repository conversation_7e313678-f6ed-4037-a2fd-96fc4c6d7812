//
//  Generated code. Do not modify.
//  source: app/models/training_indicator_label_dto.proto
//
// @dart = 2.12

// ignore_for_file: annotate_overrides, camel_case_types, comment_references
// ignore_for_file: constant_identifier_names, library_prefixes
// ignore_for_file: non_constant_identifier_names, prefer_final_fields
// ignore_for_file: unnecessary_import, unnecessary_this, unused_import

import 'dart:core' as $core;

import 'package:protobuf/protobuf.dart' as $pb;

class TrainingIndicatorLabelDto extends $pb.GeneratedMessage {
  factory TrainingIndicatorLabelDto({
    $core.int? trainingIndicatorLabelId,
    $core.String? label,
    $core.int? time,
    $core.int? distance,
  }) {
    final $result = create();
    if (trainingIndicatorLabelId != null) {
      $result.trainingIndicatorLabelId = trainingIndicatorLabelId;
    }
    if (label != null) {
      $result.label = label;
    }
    if (time != null) {
      $result.time = time;
    }
    if (distance != null) {
      $result.distance = distance;
    }
    return $result;
  }
  TrainingIndicatorLabelDto._() : super();
  factory TrainingIndicatorLabelDto.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory TrainingIndicatorLabelDto.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);

  static final $pb.BuilderInfo _i = $pb.BuilderInfo(_omitMessageNames ? '' : 'TrainingIndicatorLabelDto', package: const $pb.PackageName(_omitMessageNames ? '' : 'connectrpc.app.v1'), createEmptyInstance: create)
    ..a<$core.int>(1, _omitFieldNames ? '' : 'trainingIndicatorLabelId', $pb.PbFieldType.O3)
    ..aOS(2, _omitFieldNames ? '' : 'label')
    ..a<$core.int>(3, _omitFieldNames ? '' : 'time', $pb.PbFieldType.O3)
    ..a<$core.int>(4, _omitFieldNames ? '' : 'distance', $pb.PbFieldType.O3)
    ..hasRequiredFields = false
  ;

  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  TrainingIndicatorLabelDto clone() => TrainingIndicatorLabelDto()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  TrainingIndicatorLabelDto copyWith(void Function(TrainingIndicatorLabelDto) updates) => super.copyWith((message) => updates(message as TrainingIndicatorLabelDto)) as TrainingIndicatorLabelDto;

  $pb.BuilderInfo get info_ => _i;

  @$core.pragma('dart2js:noInline')
  static TrainingIndicatorLabelDto create() => TrainingIndicatorLabelDto._();
  TrainingIndicatorLabelDto createEmptyInstance() => create();
  static $pb.PbList<TrainingIndicatorLabelDto> createRepeated() => $pb.PbList<TrainingIndicatorLabelDto>();
  @$core.pragma('dart2js:noInline')
  static TrainingIndicatorLabelDto getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<TrainingIndicatorLabelDto>(create);
  static TrainingIndicatorLabelDto? _defaultInstance;

  @$pb.TagNumber(1)
  $core.int get trainingIndicatorLabelId => $_getIZ(0);
  @$pb.TagNumber(1)
  set trainingIndicatorLabelId($core.int v) { $_setSignedInt32(0, v); }
  @$pb.TagNumber(1)
  $core.bool hasTrainingIndicatorLabelId() => $_has(0);
  @$pb.TagNumber(1)
  void clearTrainingIndicatorLabelId() => clearField(1);

  @$pb.TagNumber(2)
  $core.String get label => $_getSZ(1);
  @$pb.TagNumber(2)
  set label($core.String v) { $_setString(1, v); }
  @$pb.TagNumber(2)
  $core.bool hasLabel() => $_has(1);
  @$pb.TagNumber(2)
  void clearLabel() => clearField(2);

  @$pb.TagNumber(3)
  $core.int get time => $_getIZ(2);
  @$pb.TagNumber(3)
  set time($core.int v) { $_setSignedInt32(2, v); }
  @$pb.TagNumber(3)
  $core.bool hasTime() => $_has(2);
  @$pb.TagNumber(3)
  void clearTime() => clearField(3);

  @$pb.TagNumber(4)
  $core.int get distance => $_getIZ(3);
  @$pb.TagNumber(4)
  set distance($core.int v) { $_setSignedInt32(3, v); }
  @$pb.TagNumber(4)
  $core.bool hasDistance() => $_has(3);
  @$pb.TagNumber(4)
  void clearDistance() => clearField(4);
}


const _omitFieldNames = $core.bool.fromEnvironment('protobuf.omit_field_names');
const _omitMessageNames = $core.bool.fromEnvironment('protobuf.omit_message_names');
