//
//  Generated code. Do not modify.
//  source: app/training_indicator_service.proto
//
// @dart = 2.12

// ignore_for_file: annotate_overrides, camel_case_types, comment_references
// ignore_for_file: constant_identifier_names, library_prefixes
// ignore_for_file: non_constant_identifier_names, prefer_final_fields
// ignore_for_file: unnecessary_import, unnecessary_this, unused_import

import 'dart:convert' as $convert;
import 'dart:core' as $core;
import 'dart:typed_data' as $typed_data;

@$core.Deprecated('Use getTrainingIndicatorRequestDescriptor instead')
const GetTrainingIndicatorRequest$json = {
  '1': 'GetTrainingIndicatorRequest',
  '2': [
    {'1': 'training_id', '3': 1, '4': 1, '5': 5, '10': 'trainingId'},
  ],
};

/// Descriptor for `GetTrainingIndicatorRequest`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List getTrainingIndicatorRequestDescriptor = $convert.base64Decode(
    'ChtHZXRUcmFpbmluZ0luZGljYXRvclJlcXVlc3QSHwoLdHJhaW5pbmdfaWQYASABKAVSCnRyYW'
    'luaW5nSWQ=');

@$core.Deprecated('Use listTrainingIndicatorLabelsRequestDescriptor instead')
const ListTrainingIndicatorLabelsRequest$json = {
  '1': 'ListTrainingIndicatorLabelsRequest',
  '2': [
    {'1': 'training_id', '3': 1, '4': 1, '5': 5, '10': 'trainingId'},
  ],
};

/// Descriptor for `ListTrainingIndicatorLabelsRequest`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List listTrainingIndicatorLabelsRequestDescriptor = $convert.base64Decode(
    'CiJMaXN0VHJhaW5pbmdJbmRpY2F0b3JMYWJlbHNSZXF1ZXN0Eh8KC3RyYWluaW5nX2lkGAEgAS'
    'gFUgp0cmFpbmluZ0lk');

@$core.Deprecated('Use getTrainingIndicatorResponseDescriptor instead')
const GetTrainingIndicatorResponse$json = {
  '1': 'GetTrainingIndicatorResponse',
  '2': [
    {'1': 'training_indicator', '3': 1, '4': 1, '5': 11, '6': '.connectrpc.app.v1.TrainingIndicatorDto', '9': 0, '10': 'trainingIndicator', '17': true},
  ],
  '8': [
    {'1': '_training_indicator'},
  ],
};

/// Descriptor for `GetTrainingIndicatorResponse`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List getTrainingIndicatorResponseDescriptor = $convert.base64Decode(
    'ChxHZXRUcmFpbmluZ0luZGljYXRvclJlc3BvbnNlElsKEnRyYWluaW5nX2luZGljYXRvchgBIA'
    'EoCzInLmNvbm5lY3RycGMuYXBwLnYxLlRyYWluaW5nSW5kaWNhdG9yRHRvSABSEXRyYWluaW5n'
    'SW5kaWNhdG9yiAEBQhUKE190cmFpbmluZ19pbmRpY2F0b3I=');

@$core.Deprecated('Use listTrainingIndicatorLabelsResponseDescriptor instead')
const ListTrainingIndicatorLabelsResponse$json = {
  '1': 'ListTrainingIndicatorLabelsResponse',
  '2': [
    {'1': 'training_indicator_labels', '3': 1, '4': 3, '5': 11, '6': '.connectrpc.app.v1.TrainingIndicatorLabelDto', '10': 'trainingIndicatorLabels'},
  ],
};

/// Descriptor for `ListTrainingIndicatorLabelsResponse`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List listTrainingIndicatorLabelsResponseDescriptor = $convert.base64Decode(
    'CiNMaXN0VHJhaW5pbmdJbmRpY2F0b3JMYWJlbHNSZXNwb25zZRJoChl0cmFpbmluZ19pbmRpY2'
    'F0b3JfbGFiZWxzGAEgAygLMiwuY29ubmVjdHJwYy5hcHAudjEuVHJhaW5pbmdJbmRpY2F0b3JM'
    'YWJlbER0b1IXdHJhaW5pbmdJbmRpY2F0b3JMYWJlbHM=');

