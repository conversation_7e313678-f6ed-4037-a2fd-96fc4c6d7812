//
//  Generated code. Do not modify.
//  source: app/horse_service.proto
//
// @dart = 2.12

// ignore_for_file: annotate_overrides, camel_case_types, comment_references
// ignore_for_file: constant_identifier_names, library_prefixes
// ignore_for_file: non_constant_identifier_names, prefer_final_fields
// ignore_for_file: unnecessary_import, unnecessary_this, unused_import

import 'dart:core' as $core;

import 'package:protobuf/protobuf.dart' as $pb;

class SortBy extends $pb.ProtobufEnum {
  static const SortBy SORT_BY_UNSPECIFIED = SortBy._(0, _omitEnumNames ? '' : 'SORT_BY_UNSPECIFIED');
  static const SortBy SORT_BY_NAME = SortBy._(1, _omitEnumNames ? '' : 'SORT_BY_NAME');
  static const SortBy SORT_BY_TRAINING_DATE = SortBy._(2, _omitEnumNames ? '' : 'SORT_BY_TRAINING_DATE');

  static const $core.List<SortBy> values = <SortBy> [
    SORT_BY_UNSPECIFIED,
    SORT_BY_NAME,
    SORT_BY_TRAINING_DATE,
  ];

  static final $core.Map<$core.int, SortBy> _byValue = $pb.ProtobufEnum.initByValue(values);
  static SortBy? valueOf($core.int value) => _byValue[value];

  const SortBy._($core.int v, $core.String n) : super(v, n);
}

class ManageStatus extends $pb.ProtobufEnum {
  static const ManageStatus MANAGE_STATUS_UNSPECIFIED = ManageStatus._(0, _omitEnumNames ? '' : 'MANAGE_STATUS_UNSPECIFIED');
  static const ManageStatus MANAGE_STATUS_MANAGED = ManageStatus._(1, _omitEnumNames ? '' : 'MANAGE_STATUS_MANAGED');
  static const ManageStatus MANAGE_STATUS_UNMANAGED = ManageStatus._(2, _omitEnumNames ? '' : 'MANAGE_STATUS_UNMANAGED');

  static const $core.List<ManageStatus> values = <ManageStatus> [
    MANAGE_STATUS_UNSPECIFIED,
    MANAGE_STATUS_MANAGED,
    MANAGE_STATUS_UNMANAGED,
  ];

  static final $core.Map<$core.int, ManageStatus> _byValue = $pb.ProtobufEnum.initByValue(values);
  static ManageStatus? valueOf($core.int value) => _byValue[value];

  const ManageStatus._($core.int v, $core.String n) : super(v, n);
}

class PitchScore extends $pb.ProtobufEnum {
  static const PitchScore PITCH_SCORE_UNSPECIFIED = PitchScore._(0, _omitEnumNames ? '' : 'PITCH_SCORE_UNSPECIFIED');
  static const PitchScore PITCH_SCORE_PITCH_LOW = PitchScore._(1, _omitEnumNames ? '' : 'PITCH_SCORE_PITCH_LOW');
  static const PitchScore PITCH_SCORE_PITCH_SLIGHTLY_LOW = PitchScore._(2, _omitEnumNames ? '' : 'PITCH_SCORE_PITCH_SLIGHTLY_LOW');
  static const PitchScore PITCH_SCORE_PITCH_MEDIUM = PitchScore._(3, _omitEnumNames ? '' : 'PITCH_SCORE_PITCH_MEDIUM');
  static const PitchScore PITCH_SCORE_PITCH_SLIGHTLY_HIGH = PitchScore._(4, _omitEnumNames ? '' : 'PITCH_SCORE_PITCH_SLIGHTLY_HIGH');
  static const PitchScore PITCH_SCORE_PITCH_HIGH = PitchScore._(5, _omitEnumNames ? '' : 'PITCH_SCORE_PITCH_HIGH');

  static const $core.List<PitchScore> values = <PitchScore> [
    PITCH_SCORE_UNSPECIFIED,
    PITCH_SCORE_PITCH_LOW,
    PITCH_SCORE_PITCH_SLIGHTLY_LOW,
    PITCH_SCORE_PITCH_MEDIUM,
    PITCH_SCORE_PITCH_SLIGHTLY_HIGH,
    PITCH_SCORE_PITCH_HIGH,
  ];

  static final $core.Map<$core.int, PitchScore> _byValue = $pb.ProtobufEnum.initByValue(values);
  static PitchScore? valueOf($core.int value) => _byValue[value];

  const PitchScore._($core.int v, $core.String n) : super(v, n);
}


const _omitEnumNames = $core.bool.fromEnvironment('protobuf.omit_enum_names');
