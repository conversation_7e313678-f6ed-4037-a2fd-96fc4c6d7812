//
//  Generated code. Do not modify.
//  source: app/time_series_heart_beat_result_service.proto
//
// @dart = 2.12

// ignore_for_file: annotate_overrides, camel_case_types, comment_references
// ignore_for_file: constant_identifier_names, library_prefixes
// ignore_for_file: non_constant_identifier_names, prefer_final_fields
// ignore_for_file: unnecessary_import, unnecessary_this, unused_import

import 'dart:core' as $core;

import 'package:protobuf/protobuf.dart' as $pb;

import 'models/time_series_heart_beat_result_dto.pb.dart' as $7;

class ListTimeSeriesHeartBeatResultRequest extends $pb.GeneratedMessage {
  factory ListTimeSeriesHeartBeatResultRequest({
    $core.int? trainingId,
  }) {
    final $result = create();
    if (trainingId != null) {
      $result.trainingId = trainingId;
    }
    return $result;
  }
  ListTimeSeriesHeartBeatResultRequest._() : super();
  factory ListTimeSeriesHeartBeatResultRequest.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory ListTimeSeriesHeartBeatResultRequest.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);

  static final $pb.BuilderInfo _i = $pb.BuilderInfo(_omitMessageNames ? '' : 'ListTimeSeriesHeartBeatResultRequest', package: const $pb.PackageName(_omitMessageNames ? '' : 'connectrpc.app.v1'), createEmptyInstance: create)
    ..a<$core.int>(1, _omitFieldNames ? '' : 'trainingId', $pb.PbFieldType.O3)
    ..hasRequiredFields = false
  ;

  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  ListTimeSeriesHeartBeatResultRequest clone() => ListTimeSeriesHeartBeatResultRequest()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  ListTimeSeriesHeartBeatResultRequest copyWith(void Function(ListTimeSeriesHeartBeatResultRequest) updates) => super.copyWith((message) => updates(message as ListTimeSeriesHeartBeatResultRequest)) as ListTimeSeriesHeartBeatResultRequest;

  $pb.BuilderInfo get info_ => _i;

  @$core.pragma('dart2js:noInline')
  static ListTimeSeriesHeartBeatResultRequest create() => ListTimeSeriesHeartBeatResultRequest._();
  ListTimeSeriesHeartBeatResultRequest createEmptyInstance() => create();
  static $pb.PbList<ListTimeSeriesHeartBeatResultRequest> createRepeated() => $pb.PbList<ListTimeSeriesHeartBeatResultRequest>();
  @$core.pragma('dart2js:noInline')
  static ListTimeSeriesHeartBeatResultRequest getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<ListTimeSeriesHeartBeatResultRequest>(create);
  static ListTimeSeriesHeartBeatResultRequest? _defaultInstance;

  @$pb.TagNumber(1)
  $core.int get trainingId => $_getIZ(0);
  @$pb.TagNumber(1)
  set trainingId($core.int v) { $_setSignedInt32(0, v); }
  @$pb.TagNumber(1)
  $core.bool hasTrainingId() => $_has(0);
  @$pb.TagNumber(1)
  void clearTrainingId() => clearField(1);
}

class ListTimeSeriesHeartBeatResultResponse extends $pb.GeneratedMessage {
  factory ListTimeSeriesHeartBeatResultResponse({
    $core.Iterable<$7.TimeSeriesHeartBeatResultDto>? timeSeriesHeartBeatResults,
  }) {
    final $result = create();
    if (timeSeriesHeartBeatResults != null) {
      $result.timeSeriesHeartBeatResults.addAll(timeSeriesHeartBeatResults);
    }
    return $result;
  }
  ListTimeSeriesHeartBeatResultResponse._() : super();
  factory ListTimeSeriesHeartBeatResultResponse.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory ListTimeSeriesHeartBeatResultResponse.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);

  static final $pb.BuilderInfo _i = $pb.BuilderInfo(_omitMessageNames ? '' : 'ListTimeSeriesHeartBeatResultResponse', package: const $pb.PackageName(_omitMessageNames ? '' : 'connectrpc.app.v1'), createEmptyInstance: create)
    ..pc<$7.TimeSeriesHeartBeatResultDto>(1, _omitFieldNames ? '' : 'timeSeriesHeartBeatResults', $pb.PbFieldType.PM, subBuilder: $7.TimeSeriesHeartBeatResultDto.create)
    ..hasRequiredFields = false
  ;

  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  ListTimeSeriesHeartBeatResultResponse clone() => ListTimeSeriesHeartBeatResultResponse()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  ListTimeSeriesHeartBeatResultResponse copyWith(void Function(ListTimeSeriesHeartBeatResultResponse) updates) => super.copyWith((message) => updates(message as ListTimeSeriesHeartBeatResultResponse)) as ListTimeSeriesHeartBeatResultResponse;

  $pb.BuilderInfo get info_ => _i;

  @$core.pragma('dart2js:noInline')
  static ListTimeSeriesHeartBeatResultResponse create() => ListTimeSeriesHeartBeatResultResponse._();
  ListTimeSeriesHeartBeatResultResponse createEmptyInstance() => create();
  static $pb.PbList<ListTimeSeriesHeartBeatResultResponse> createRepeated() => $pb.PbList<ListTimeSeriesHeartBeatResultResponse>();
  @$core.pragma('dart2js:noInline')
  static ListTimeSeriesHeartBeatResultResponse getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<ListTimeSeriesHeartBeatResultResponse>(create);
  static ListTimeSeriesHeartBeatResultResponse? _defaultInstance;

  @$pb.TagNumber(1)
  $core.List<$7.TimeSeriesHeartBeatResultDto> get timeSeriesHeartBeatResults => $_getList(0);
}


const _omitFieldNames = $core.bool.fromEnvironment('protobuf.omit_field_names');
const _omitMessageNames = $core.bool.fromEnvironment('protobuf.omit_message_names');
