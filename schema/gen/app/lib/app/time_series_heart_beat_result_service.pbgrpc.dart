//
//  Generated code. Do not modify.
//  source: app/time_series_heart_beat_result_service.proto
//
// @dart = 2.12

// ignore_for_file: annotate_overrides, camel_case_types, comment_references
// ignore_for_file: constant_identifier_names, library_prefixes
// ignore_for_file: non_constant_identifier_names, prefer_final_fields
// ignore_for_file: unnecessary_import, unnecessary_this, unused_import

import 'dart:async' as $async;
import 'dart:core' as $core;

import 'package:grpc/service_api.dart' as $grpc;
import 'package:protobuf/protobuf.dart' as $pb;

import 'time_series_heart_beat_result_service.pb.dart' as $2;

export 'time_series_heart_beat_result_service.pb.dart';

@$pb.GrpcServiceName('connectrpc.app.v1.TimeSeriesHeartBeatResultService')
class TimeSeriesHeartBeatResultServiceClient extends $grpc.Client {
  static final _$listTimeSeriesHeartBeatResult = $grpc.ClientMethod<$2.ListTimeSeriesHeartBeatResultRequest, $2.ListTimeSeriesHeartBeatResultResponse>(
      '/connectrpc.app.v1.TimeSeriesHeartBeatResultService/ListTimeSeriesHeartBeatResult',
      ($2.ListTimeSeriesHeartBeatResultRequest value) => value.writeToBuffer(),
      ($core.List<$core.int> value) => $2.ListTimeSeriesHeartBeatResultResponse.fromBuffer(value));

  TimeSeriesHeartBeatResultServiceClient($grpc.ClientChannel channel,
      {$grpc.CallOptions? options,
      $core.Iterable<$grpc.ClientInterceptor>? interceptors})
      : super(channel, options: options,
        interceptors: interceptors);

  $grpc.ResponseFuture<$2.ListTimeSeriesHeartBeatResultResponse> listTimeSeriesHeartBeatResult($2.ListTimeSeriesHeartBeatResultRequest request, {$grpc.CallOptions? options}) {
    return $createUnaryCall(_$listTimeSeriesHeartBeatResult, request, options: options);
  }
}

@$pb.GrpcServiceName('connectrpc.app.v1.TimeSeriesHeartBeatResultService')
abstract class TimeSeriesHeartBeatResultServiceBase extends $grpc.Service {
  $core.String get $name => 'connectrpc.app.v1.TimeSeriesHeartBeatResultService';

  TimeSeriesHeartBeatResultServiceBase() {
    $addMethod($grpc.ServiceMethod<$2.ListTimeSeriesHeartBeatResultRequest, $2.ListTimeSeriesHeartBeatResultResponse>(
        'ListTimeSeriesHeartBeatResult',
        listTimeSeriesHeartBeatResult_Pre,
        false,
        false,
        ($core.List<$core.int> value) => $2.ListTimeSeriesHeartBeatResultRequest.fromBuffer(value),
        ($2.ListTimeSeriesHeartBeatResultResponse value) => value.writeToBuffer()));
  }

  $async.Future<$2.ListTimeSeriesHeartBeatResultResponse> listTimeSeriesHeartBeatResult_Pre($grpc.ServiceCall call, $async.Future<$2.ListTimeSeriesHeartBeatResultRequest> request) async {
    return listTimeSeriesHeartBeatResult(call, await request);
  }

  $async.Future<$2.ListTimeSeriesHeartBeatResultResponse> listTimeSeriesHeartBeatResult($grpc.ServiceCall call, $2.ListTimeSeriesHeartBeatResultRequest request);
}
