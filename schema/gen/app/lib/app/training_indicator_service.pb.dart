//
//  Generated code. Do not modify.
//  source: app/training_indicator_service.proto
//
// @dart = 2.12

// ignore_for_file: annotate_overrides, camel_case_types, comment_references
// ignore_for_file: constant_identifier_names, library_prefixes
// ignore_for_file: non_constant_identifier_names, prefer_final_fields
// ignore_for_file: unnecessary_import, unnecessary_this, unused_import

import 'dart:core' as $core;

import 'package:protobuf/protobuf.dart' as $pb;

import 'models/training_indicator_dto.pb.dart' as $8;
import 'models/training_indicator_label_dto.pb.dart' as $9;

class GetTrainingIndicatorRequest extends $pb.GeneratedMessage {
  factory GetTrainingIndicatorRequest({
    $core.int? trainingId,
  }) {
    final $result = create();
    if (trainingId != null) {
      $result.trainingId = trainingId;
    }
    return $result;
  }
  GetTrainingIndicatorRequest._() : super();
  factory GetTrainingIndicatorRequest.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory GetTrainingIndicatorRequest.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);

  static final $pb.BuilderInfo _i = $pb.BuilderInfo(_omitMessageNames ? '' : 'GetTrainingIndicatorRequest', package: const $pb.PackageName(_omitMessageNames ? '' : 'connectrpc.app.v1'), createEmptyInstance: create)
    ..a<$core.int>(1, _omitFieldNames ? '' : 'trainingId', $pb.PbFieldType.O3)
    ..hasRequiredFields = false
  ;

  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  GetTrainingIndicatorRequest clone() => GetTrainingIndicatorRequest()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  GetTrainingIndicatorRequest copyWith(void Function(GetTrainingIndicatorRequest) updates) => super.copyWith((message) => updates(message as GetTrainingIndicatorRequest)) as GetTrainingIndicatorRequest;

  $pb.BuilderInfo get info_ => _i;

  @$core.pragma('dart2js:noInline')
  static GetTrainingIndicatorRequest create() => GetTrainingIndicatorRequest._();
  GetTrainingIndicatorRequest createEmptyInstance() => create();
  static $pb.PbList<GetTrainingIndicatorRequest> createRepeated() => $pb.PbList<GetTrainingIndicatorRequest>();
  @$core.pragma('dart2js:noInline')
  static GetTrainingIndicatorRequest getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<GetTrainingIndicatorRequest>(create);
  static GetTrainingIndicatorRequest? _defaultInstance;

  @$pb.TagNumber(1)
  $core.int get trainingId => $_getIZ(0);
  @$pb.TagNumber(1)
  set trainingId($core.int v) { $_setSignedInt32(0, v); }
  @$pb.TagNumber(1)
  $core.bool hasTrainingId() => $_has(0);
  @$pb.TagNumber(1)
  void clearTrainingId() => clearField(1);
}

class ListTrainingIndicatorLabelsRequest extends $pb.GeneratedMessage {
  factory ListTrainingIndicatorLabelsRequest({
    $core.int? trainingId,
  }) {
    final $result = create();
    if (trainingId != null) {
      $result.trainingId = trainingId;
    }
    return $result;
  }
  ListTrainingIndicatorLabelsRequest._() : super();
  factory ListTrainingIndicatorLabelsRequest.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory ListTrainingIndicatorLabelsRequest.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);

  static final $pb.BuilderInfo _i = $pb.BuilderInfo(_omitMessageNames ? '' : 'ListTrainingIndicatorLabelsRequest', package: const $pb.PackageName(_omitMessageNames ? '' : 'connectrpc.app.v1'), createEmptyInstance: create)
    ..a<$core.int>(1, _omitFieldNames ? '' : 'trainingId', $pb.PbFieldType.O3)
    ..hasRequiredFields = false
  ;

  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  ListTrainingIndicatorLabelsRequest clone() => ListTrainingIndicatorLabelsRequest()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  ListTrainingIndicatorLabelsRequest copyWith(void Function(ListTrainingIndicatorLabelsRequest) updates) => super.copyWith((message) => updates(message as ListTrainingIndicatorLabelsRequest)) as ListTrainingIndicatorLabelsRequest;

  $pb.BuilderInfo get info_ => _i;

  @$core.pragma('dart2js:noInline')
  static ListTrainingIndicatorLabelsRequest create() => ListTrainingIndicatorLabelsRequest._();
  ListTrainingIndicatorLabelsRequest createEmptyInstance() => create();
  static $pb.PbList<ListTrainingIndicatorLabelsRequest> createRepeated() => $pb.PbList<ListTrainingIndicatorLabelsRequest>();
  @$core.pragma('dart2js:noInline')
  static ListTrainingIndicatorLabelsRequest getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<ListTrainingIndicatorLabelsRequest>(create);
  static ListTrainingIndicatorLabelsRequest? _defaultInstance;

  @$pb.TagNumber(1)
  $core.int get trainingId => $_getIZ(0);
  @$pb.TagNumber(1)
  set trainingId($core.int v) { $_setSignedInt32(0, v); }
  @$pb.TagNumber(1)
  $core.bool hasTrainingId() => $_has(0);
  @$pb.TagNumber(1)
  void clearTrainingId() => clearField(1);
}

class GetTrainingIndicatorResponse extends $pb.GeneratedMessage {
  factory GetTrainingIndicatorResponse({
    $8.TrainingIndicatorDto? trainingIndicator,
  }) {
    final $result = create();
    if (trainingIndicator != null) {
      $result.trainingIndicator = trainingIndicator;
    }
    return $result;
  }
  GetTrainingIndicatorResponse._() : super();
  factory GetTrainingIndicatorResponse.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory GetTrainingIndicatorResponse.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);

  static final $pb.BuilderInfo _i = $pb.BuilderInfo(_omitMessageNames ? '' : 'GetTrainingIndicatorResponse', package: const $pb.PackageName(_omitMessageNames ? '' : 'connectrpc.app.v1'), createEmptyInstance: create)
    ..aOM<$8.TrainingIndicatorDto>(1, _omitFieldNames ? '' : 'trainingIndicator', subBuilder: $8.TrainingIndicatorDto.create)
    ..hasRequiredFields = false
  ;

  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  GetTrainingIndicatorResponse clone() => GetTrainingIndicatorResponse()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  GetTrainingIndicatorResponse copyWith(void Function(GetTrainingIndicatorResponse) updates) => super.copyWith((message) => updates(message as GetTrainingIndicatorResponse)) as GetTrainingIndicatorResponse;

  $pb.BuilderInfo get info_ => _i;

  @$core.pragma('dart2js:noInline')
  static GetTrainingIndicatorResponse create() => GetTrainingIndicatorResponse._();
  GetTrainingIndicatorResponse createEmptyInstance() => create();
  static $pb.PbList<GetTrainingIndicatorResponse> createRepeated() => $pb.PbList<GetTrainingIndicatorResponse>();
  @$core.pragma('dart2js:noInline')
  static GetTrainingIndicatorResponse getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<GetTrainingIndicatorResponse>(create);
  static GetTrainingIndicatorResponse? _defaultInstance;

  @$pb.TagNumber(1)
  $8.TrainingIndicatorDto get trainingIndicator => $_getN(0);
  @$pb.TagNumber(1)
  set trainingIndicator($8.TrainingIndicatorDto v) { setField(1, v); }
  @$pb.TagNumber(1)
  $core.bool hasTrainingIndicator() => $_has(0);
  @$pb.TagNumber(1)
  void clearTrainingIndicator() => clearField(1);
  @$pb.TagNumber(1)
  $8.TrainingIndicatorDto ensureTrainingIndicator() => $_ensure(0);
}

class ListTrainingIndicatorLabelsResponse extends $pb.GeneratedMessage {
  factory ListTrainingIndicatorLabelsResponse({
    $core.Iterable<$9.TrainingIndicatorLabelDto>? trainingIndicatorLabels,
  }) {
    final $result = create();
    if (trainingIndicatorLabels != null) {
      $result.trainingIndicatorLabels.addAll(trainingIndicatorLabels);
    }
    return $result;
  }
  ListTrainingIndicatorLabelsResponse._() : super();
  factory ListTrainingIndicatorLabelsResponse.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory ListTrainingIndicatorLabelsResponse.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);

  static final $pb.BuilderInfo _i = $pb.BuilderInfo(_omitMessageNames ? '' : 'ListTrainingIndicatorLabelsResponse', package: const $pb.PackageName(_omitMessageNames ? '' : 'connectrpc.app.v1'), createEmptyInstance: create)
    ..pc<$9.TrainingIndicatorLabelDto>(1, _omitFieldNames ? '' : 'trainingIndicatorLabels', $pb.PbFieldType.PM, subBuilder: $9.TrainingIndicatorLabelDto.create)
    ..hasRequiredFields = false
  ;

  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  ListTrainingIndicatorLabelsResponse clone() => ListTrainingIndicatorLabelsResponse()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  ListTrainingIndicatorLabelsResponse copyWith(void Function(ListTrainingIndicatorLabelsResponse) updates) => super.copyWith((message) => updates(message as ListTrainingIndicatorLabelsResponse)) as ListTrainingIndicatorLabelsResponse;

  $pb.BuilderInfo get info_ => _i;

  @$core.pragma('dart2js:noInline')
  static ListTrainingIndicatorLabelsResponse create() => ListTrainingIndicatorLabelsResponse._();
  ListTrainingIndicatorLabelsResponse createEmptyInstance() => create();
  static $pb.PbList<ListTrainingIndicatorLabelsResponse> createRepeated() => $pb.PbList<ListTrainingIndicatorLabelsResponse>();
  @$core.pragma('dart2js:noInline')
  static ListTrainingIndicatorLabelsResponse getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<ListTrainingIndicatorLabelsResponse>(create);
  static ListTrainingIndicatorLabelsResponse? _defaultInstance;

  @$pb.TagNumber(1)
  $core.List<$9.TrainingIndicatorLabelDto> get trainingIndicatorLabels => $_getList(0);
}


const _omitFieldNames = $core.bool.fromEnvironment('protobuf.omit_field_names');
const _omitMessageNames = $core.bool.fromEnvironment('protobuf.omit_message_names');
