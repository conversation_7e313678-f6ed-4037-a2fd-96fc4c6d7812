//
//  Generated code. Do not modify.
//  source: app/gait_analysis_result_service.proto
//
// @dart = 2.12

// ignore_for_file: annotate_overrides, camel_case_types, comment_references
// ignore_for_file: constant_identifier_names, library_prefixes
// ignore_for_file: non_constant_identifier_names, prefer_final_fields
// ignore_for_file: unnecessary_import, unnecessary_this, unused_import

import 'dart:core' as $core;

import 'package:protobuf/protobuf.dart' as $pb;

import 'models/gait_analysis_result_dto.pb.dart' as $5;

class ListGaitAnalysisResultRequest extends $pb.GeneratedMessage {
  factory ListGaitAnalysisResultRequest({
    $core.int? trainingId,
  }) {
    final $result = create();
    if (trainingId != null) {
      $result.trainingId = trainingId;
    }
    return $result;
  }
  ListGaitAnalysisResultRequest._() : super();
  factory ListGaitAnalysisResultRequest.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory ListGaitAnalysisResultRequest.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);

  static final $pb.BuilderInfo _i = $pb.BuilderInfo(_omitMessageNames ? '' : 'ListGaitAnalysisResultRequest', package: const $pb.PackageName(_omitMessageNames ? '' : 'connectrpc.app.v1'), createEmptyInstance: create)
    ..a<$core.int>(1, _omitFieldNames ? '' : 'trainingId', $pb.PbFieldType.O3)
    ..hasRequiredFields = false
  ;

  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  ListGaitAnalysisResultRequest clone() => ListGaitAnalysisResultRequest()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  ListGaitAnalysisResultRequest copyWith(void Function(ListGaitAnalysisResultRequest) updates) => super.copyWith((message) => updates(message as ListGaitAnalysisResultRequest)) as ListGaitAnalysisResultRequest;

  $pb.BuilderInfo get info_ => _i;

  @$core.pragma('dart2js:noInline')
  static ListGaitAnalysisResultRequest create() => ListGaitAnalysisResultRequest._();
  ListGaitAnalysisResultRequest createEmptyInstance() => create();
  static $pb.PbList<ListGaitAnalysisResultRequest> createRepeated() => $pb.PbList<ListGaitAnalysisResultRequest>();
  @$core.pragma('dart2js:noInline')
  static ListGaitAnalysisResultRequest getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<ListGaitAnalysisResultRequest>(create);
  static ListGaitAnalysisResultRequest? _defaultInstance;

  @$pb.TagNumber(1)
  $core.int get trainingId => $_getIZ(0);
  @$pb.TagNumber(1)
  set trainingId($core.int v) { $_setSignedInt32(0, v); }
  @$pb.TagNumber(1)
  $core.bool hasTrainingId() => $_has(0);
  @$pb.TagNumber(1)
  void clearTrainingId() => clearField(1);
}

class ListGaitAnalysisResultResponse extends $pb.GeneratedMessage {
  factory ListGaitAnalysisResultResponse({
    $core.Iterable<$5.GaitAnalysisResultDto>? gaitAnalysisResults,
    $core.String? allPeriodStartAt,
  }) {
    final $result = create();
    if (gaitAnalysisResults != null) {
      $result.gaitAnalysisResults.addAll(gaitAnalysisResults);
    }
    if (allPeriodStartAt != null) {
      $result.allPeriodStartAt = allPeriodStartAt;
    }
    return $result;
  }
  ListGaitAnalysisResultResponse._() : super();
  factory ListGaitAnalysisResultResponse.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory ListGaitAnalysisResultResponse.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);

  static final $pb.BuilderInfo _i = $pb.BuilderInfo(_omitMessageNames ? '' : 'ListGaitAnalysisResultResponse', package: const $pb.PackageName(_omitMessageNames ? '' : 'connectrpc.app.v1'), createEmptyInstance: create)
    ..pc<$5.GaitAnalysisResultDto>(1, _omitFieldNames ? '' : 'gaitAnalysisResults', $pb.PbFieldType.PM, subBuilder: $5.GaitAnalysisResultDto.create)
    ..aOS(2, _omitFieldNames ? '' : 'allPeriodStartAt')
    ..hasRequiredFields = false
  ;

  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  ListGaitAnalysisResultResponse clone() => ListGaitAnalysisResultResponse()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  ListGaitAnalysisResultResponse copyWith(void Function(ListGaitAnalysisResultResponse) updates) => super.copyWith((message) => updates(message as ListGaitAnalysisResultResponse)) as ListGaitAnalysisResultResponse;

  $pb.BuilderInfo get info_ => _i;

  @$core.pragma('dart2js:noInline')
  static ListGaitAnalysisResultResponse create() => ListGaitAnalysisResultResponse._();
  ListGaitAnalysisResultResponse createEmptyInstance() => create();
  static $pb.PbList<ListGaitAnalysisResultResponse> createRepeated() => $pb.PbList<ListGaitAnalysisResultResponse>();
  @$core.pragma('dart2js:noInline')
  static ListGaitAnalysisResultResponse getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<ListGaitAnalysisResultResponse>(create);
  static ListGaitAnalysisResultResponse? _defaultInstance;

  @$pb.TagNumber(1)
  $core.List<$5.GaitAnalysisResultDto> get gaitAnalysisResults => $_getList(0);

  @$pb.TagNumber(2)
  $core.String get allPeriodStartAt => $_getSZ(1);
  @$pb.TagNumber(2)
  set allPeriodStartAt($core.String v) { $_setString(1, v); }
  @$pb.TagNumber(2)
  $core.bool hasAllPeriodStartAt() => $_has(1);
  @$pb.TagNumber(2)
  void clearAllPeriodStartAt() => clearField(2);
}


const _omitFieldNames = $core.bool.fromEnvironment('protobuf.omit_field_names');
const _omitMessageNames = $core.bool.fromEnvironment('protobuf.omit_message_names');
