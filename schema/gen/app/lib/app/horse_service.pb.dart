//
//  Generated code. Do not modify.
//  source: app/horse_service.proto
//
// @dart = 2.12

// ignore_for_file: annotate_overrides, camel_case_types, comment_references
// ignore_for_file: constant_identifier_names, library_prefixes
// ignore_for_file: non_constant_identifier_names, prefer_final_fields
// ignore_for_file: unnecessary_import, unnecessary_this, unused_import

import 'dart:core' as $core;

import 'package:fixnum/fixnum.dart' as $fixnum;
import 'package:protobuf/protobuf.dart' as $pb;

import 'horse_service.pbenum.dart';
import 'models/horse_dto.pb.dart' as $6;

export 'horse_service.pbenum.dart';

class ListHorsesRequest extends $pb.GeneratedMessage {
  factory ListHorsesRequest({
    SortBy? sortBy,
  }) {
    final $result = create();
    if (sortBy != null) {
      $result.sortBy = sortBy;
    }
    return $result;
  }
  ListHorsesRequest._() : super();
  factory ListHorsesRequest.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory ListHorsesRequest.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);

  static final $pb.BuilderInfo _i = $pb.BuilderInfo(_omitMessageNames ? '' : 'ListHorsesRequest', package: const $pb.PackageName(_omitMessageNames ? '' : 'connectrpc.app.v1'), createEmptyInstance: create)
    ..e<SortBy>(1, _omitFieldNames ? '' : 'sortBy', $pb.PbFieldType.OE, defaultOrMaker: SortBy.SORT_BY_UNSPECIFIED, valueOf: SortBy.valueOf, enumValues: SortBy.values)
    ..hasRequiredFields = false
  ;

  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  ListHorsesRequest clone() => ListHorsesRequest()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  ListHorsesRequest copyWith(void Function(ListHorsesRequest) updates) => super.copyWith((message) => updates(message as ListHorsesRequest)) as ListHorsesRequest;

  $pb.BuilderInfo get info_ => _i;

  @$core.pragma('dart2js:noInline')
  static ListHorsesRequest create() => ListHorsesRequest._();
  ListHorsesRequest createEmptyInstance() => create();
  static $pb.PbList<ListHorsesRequest> createRepeated() => $pb.PbList<ListHorsesRequest>();
  @$core.pragma('dart2js:noInline')
  static ListHorsesRequest getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<ListHorsesRequest>(create);
  static ListHorsesRequest? _defaultInstance;

  @$pb.TagNumber(1)
  SortBy get sortBy => $_getN(0);
  @$pb.TagNumber(1)
  set sortBy(SortBy v) { setField(1, v); }
  @$pb.TagNumber(1)
  $core.bool hasSortBy() => $_has(0);
  @$pb.TagNumber(1)
  void clearSortBy() => clearField(1);
}

class ListHorsesResponse extends $pb.GeneratedMessage {
  factory ListHorsesResponse({
    $core.Iterable<$6.HorseDto>? horses,
  }) {
    final $result = create();
    if (horses != null) {
      $result.horses.addAll(horses);
    }
    return $result;
  }
  ListHorsesResponse._() : super();
  factory ListHorsesResponse.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory ListHorsesResponse.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);

  static final $pb.BuilderInfo _i = $pb.BuilderInfo(_omitMessageNames ? '' : 'ListHorsesResponse', package: const $pb.PackageName(_omitMessageNames ? '' : 'connectrpc.app.v1'), createEmptyInstance: create)
    ..pc<$6.HorseDto>(1, _omitFieldNames ? '' : 'horses', $pb.PbFieldType.PM, subBuilder: $6.HorseDto.create)
    ..hasRequiredFields = false
  ;

  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  ListHorsesResponse clone() => ListHorsesResponse()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  ListHorsesResponse copyWith(void Function(ListHorsesResponse) updates) => super.copyWith((message) => updates(message as ListHorsesResponse)) as ListHorsesResponse;

  $pb.BuilderInfo get info_ => _i;

  @$core.pragma('dart2js:noInline')
  static ListHorsesResponse create() => ListHorsesResponse._();
  ListHorsesResponse createEmptyInstance() => create();
  static $pb.PbList<ListHorsesResponse> createRepeated() => $pb.PbList<ListHorsesResponse>();
  @$core.pragma('dart2js:noInline')
  static ListHorsesResponse getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<ListHorsesResponse>(create);
  static ListHorsesResponse? _defaultInstance;

  @$pb.TagNumber(1)
  $core.List<$6.HorseDto> get horses => $_getList(0);
}

class GetHorseRequest extends $pb.GeneratedMessage {
  factory GetHorseRequest({
    $fixnum.Int64? horseId,
  }) {
    final $result = create();
    if (horseId != null) {
      $result.horseId = horseId;
    }
    return $result;
  }
  GetHorseRequest._() : super();
  factory GetHorseRequest.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory GetHorseRequest.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);

  static final $pb.BuilderInfo _i = $pb.BuilderInfo(_omitMessageNames ? '' : 'GetHorseRequest', package: const $pb.PackageName(_omitMessageNames ? '' : 'connectrpc.app.v1'), createEmptyInstance: create)
    ..aInt64(1, _omitFieldNames ? '' : 'horseId')
    ..hasRequiredFields = false
  ;

  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  GetHorseRequest clone() => GetHorseRequest()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  GetHorseRequest copyWith(void Function(GetHorseRequest) updates) => super.copyWith((message) => updates(message as GetHorseRequest)) as GetHorseRequest;

  $pb.BuilderInfo get info_ => _i;

  @$core.pragma('dart2js:noInline')
  static GetHorseRequest create() => GetHorseRequest._();
  GetHorseRequest createEmptyInstance() => create();
  static $pb.PbList<GetHorseRequest> createRepeated() => $pb.PbList<GetHorseRequest>();
  @$core.pragma('dart2js:noInline')
  static GetHorseRequest getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<GetHorseRequest>(create);
  static GetHorseRequest? _defaultInstance;

  @$pb.TagNumber(1)
  $fixnum.Int64 get horseId => $_getI64(0);
  @$pb.TagNumber(1)
  set horseId($fixnum.Int64 v) { $_setInt64(0, v); }
  @$pb.TagNumber(1)
  $core.bool hasHorseId() => $_has(0);
  @$pb.TagNumber(1)
  void clearHorseId() => clearField(1);
}

class GetHorseResponse extends $pb.GeneratedMessage {
  factory GetHorseResponse({
    $6.HorseDto? horse,
  }) {
    final $result = create();
    if (horse != null) {
      $result.horse = horse;
    }
    return $result;
  }
  GetHorseResponse._() : super();
  factory GetHorseResponse.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory GetHorseResponse.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);

  static final $pb.BuilderInfo _i = $pb.BuilderInfo(_omitMessageNames ? '' : 'GetHorseResponse', package: const $pb.PackageName(_omitMessageNames ? '' : 'connectrpc.app.v1'), createEmptyInstance: create)
    ..aOM<$6.HorseDto>(1, _omitFieldNames ? '' : 'horse', subBuilder: $6.HorseDto.create)
    ..hasRequiredFields = false
  ;

  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  GetHorseResponse clone() => GetHorseResponse()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  GetHorseResponse copyWith(void Function(GetHorseResponse) updates) => super.copyWith((message) => updates(message as GetHorseResponse)) as GetHorseResponse;

  $pb.BuilderInfo get info_ => _i;

  @$core.pragma('dart2js:noInline')
  static GetHorseResponse create() => GetHorseResponse._();
  GetHorseResponse createEmptyInstance() => create();
  static $pb.PbList<GetHorseResponse> createRepeated() => $pb.PbList<GetHorseResponse>();
  @$core.pragma('dart2js:noInline')
  static GetHorseResponse getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<GetHorseResponse>(create);
  static GetHorseResponse? _defaultInstance;

  @$pb.TagNumber(1)
  $6.HorseDto get horse => $_getN(0);
  @$pb.TagNumber(1)
  set horse($6.HorseDto v) { setField(1, v); }
  @$pb.TagNumber(1)
  $core.bool hasHorse() => $_has(0);
  @$pb.TagNumber(1)
  void clearHorse() => clearField(1);
  @$pb.TagNumber(1)
  $6.HorseDto ensureHorse() => $_ensure(0);
}

class CreateHorseFromMasterHorseRequest extends $pb.GeneratedMessage {
  factory CreateHorseFromMasterHorseRequest({
    $core.String? stableUuid,
    $core.bool? inStable,
    $core.String? masterHorseId,
    $core.String? profilePicPath,
  }) {
    final $result = create();
    if (stableUuid != null) {
      $result.stableUuid = stableUuid;
    }
    if (inStable != null) {
      $result.inStable = inStable;
    }
    if (masterHorseId != null) {
      $result.masterHorseId = masterHorseId;
    }
    if (profilePicPath != null) {
      $result.profilePicPath = profilePicPath;
    }
    return $result;
  }
  CreateHorseFromMasterHorseRequest._() : super();
  factory CreateHorseFromMasterHorseRequest.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory CreateHorseFromMasterHorseRequest.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);

  static final $pb.BuilderInfo _i = $pb.BuilderInfo(_omitMessageNames ? '' : 'CreateHorseFromMasterHorseRequest', package: const $pb.PackageName(_omitMessageNames ? '' : 'connectrpc.app.v1'), createEmptyInstance: create)
    ..aOS(1, _omitFieldNames ? '' : 'stableUuid')
    ..aOB(2, _omitFieldNames ? '' : 'inStable')
    ..aOS(3, _omitFieldNames ? '' : 'masterHorseId')
    ..aOS(4, _omitFieldNames ? '' : 'profilePicPath')
    ..hasRequiredFields = false
  ;

  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  CreateHorseFromMasterHorseRequest clone() => CreateHorseFromMasterHorseRequest()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  CreateHorseFromMasterHorseRequest copyWith(void Function(CreateHorseFromMasterHorseRequest) updates) => super.copyWith((message) => updates(message as CreateHorseFromMasterHorseRequest)) as CreateHorseFromMasterHorseRequest;

  $pb.BuilderInfo get info_ => _i;

  @$core.pragma('dart2js:noInline')
  static CreateHorseFromMasterHorseRequest create() => CreateHorseFromMasterHorseRequest._();
  CreateHorseFromMasterHorseRequest createEmptyInstance() => create();
  static $pb.PbList<CreateHorseFromMasterHorseRequest> createRepeated() => $pb.PbList<CreateHorseFromMasterHorseRequest>();
  @$core.pragma('dart2js:noInline')
  static CreateHorseFromMasterHorseRequest getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<CreateHorseFromMasterHorseRequest>(create);
  static CreateHorseFromMasterHorseRequest? _defaultInstance;

  @$pb.TagNumber(1)
  $core.String get stableUuid => $_getSZ(0);
  @$pb.TagNumber(1)
  set stableUuid($core.String v) { $_setString(0, v); }
  @$pb.TagNumber(1)
  $core.bool hasStableUuid() => $_has(0);
  @$pb.TagNumber(1)
  void clearStableUuid() => clearField(1);

  @$pb.TagNumber(2)
  $core.bool get inStable => $_getBF(1);
  @$pb.TagNumber(2)
  set inStable($core.bool v) { $_setBool(1, v); }
  @$pb.TagNumber(2)
  $core.bool hasInStable() => $_has(1);
  @$pb.TagNumber(2)
  void clearInStable() => clearField(2);

  @$pb.TagNumber(3)
  $core.String get masterHorseId => $_getSZ(2);
  @$pb.TagNumber(3)
  set masterHorseId($core.String v) { $_setString(2, v); }
  @$pb.TagNumber(3)
  $core.bool hasMasterHorseId() => $_has(2);
  @$pb.TagNumber(3)
  void clearMasterHorseId() => clearField(3);

  @$pb.TagNumber(4)
  $core.String get profilePicPath => $_getSZ(3);
  @$pb.TagNumber(4)
  set profilePicPath($core.String v) { $_setString(3, v); }
  @$pb.TagNumber(4)
  $core.bool hasProfilePicPath() => $_has(3);
  @$pb.TagNumber(4)
  void clearProfilePicPath() => clearField(4);
}

class CreateHorseFromMasterHorseResponse extends $pb.GeneratedMessage {
  factory CreateHorseFromMasterHorseResponse({
    $6.HorseDto? horse,
  }) {
    final $result = create();
    if (horse != null) {
      $result.horse = horse;
    }
    return $result;
  }
  CreateHorseFromMasterHorseResponse._() : super();
  factory CreateHorseFromMasterHorseResponse.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory CreateHorseFromMasterHorseResponse.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);

  static final $pb.BuilderInfo _i = $pb.BuilderInfo(_omitMessageNames ? '' : 'CreateHorseFromMasterHorseResponse', package: const $pb.PackageName(_omitMessageNames ? '' : 'connectrpc.app.v1'), createEmptyInstance: create)
    ..aOM<$6.HorseDto>(1, _omitFieldNames ? '' : 'horse', subBuilder: $6.HorseDto.create)
    ..hasRequiredFields = false
  ;

  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  CreateHorseFromMasterHorseResponse clone() => CreateHorseFromMasterHorseResponse()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  CreateHorseFromMasterHorseResponse copyWith(void Function(CreateHorseFromMasterHorseResponse) updates) => super.copyWith((message) => updates(message as CreateHorseFromMasterHorseResponse)) as CreateHorseFromMasterHorseResponse;

  $pb.BuilderInfo get info_ => _i;

  @$core.pragma('dart2js:noInline')
  static CreateHorseFromMasterHorseResponse create() => CreateHorseFromMasterHorseResponse._();
  CreateHorseFromMasterHorseResponse createEmptyInstance() => create();
  static $pb.PbList<CreateHorseFromMasterHorseResponse> createRepeated() => $pb.PbList<CreateHorseFromMasterHorseResponse>();
  @$core.pragma('dart2js:noInline')
  static CreateHorseFromMasterHorseResponse getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<CreateHorseFromMasterHorseResponse>(create);
  static CreateHorseFromMasterHorseResponse? _defaultInstance;

  @$pb.TagNumber(1)
  $6.HorseDto get horse => $_getN(0);
  @$pb.TagNumber(1)
  set horse($6.HorseDto v) { setField(1, v); }
  @$pb.TagNumber(1)
  $core.bool hasHorse() => $_has(0);
  @$pb.TagNumber(1)
  void clearHorse() => clearField(1);
  @$pb.TagNumber(1)
  $6.HorseDto ensureHorse() => $_ensure(0);
}

class CreateHorseRequest extends $pb.GeneratedMessage {
  factory CreateHorseRequest({
    $core.String? stableUuid,
    $core.bool? inStable,
    $core.String? name,
    $core.String? gender,
    $core.int? birthYear,
    $core.String? profilePicPath,
  }) {
    final $result = create();
    if (stableUuid != null) {
      $result.stableUuid = stableUuid;
    }
    if (inStable != null) {
      $result.inStable = inStable;
    }
    if (name != null) {
      $result.name = name;
    }
    if (gender != null) {
      $result.gender = gender;
    }
    if (birthYear != null) {
      $result.birthYear = birthYear;
    }
    if (profilePicPath != null) {
      $result.profilePicPath = profilePicPath;
    }
    return $result;
  }
  CreateHorseRequest._() : super();
  factory CreateHorseRequest.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory CreateHorseRequest.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);

  static final $pb.BuilderInfo _i = $pb.BuilderInfo(_omitMessageNames ? '' : 'CreateHorseRequest', package: const $pb.PackageName(_omitMessageNames ? '' : 'connectrpc.app.v1'), createEmptyInstance: create)
    ..aOS(1, _omitFieldNames ? '' : 'stableUuid')
    ..aOB(2, _omitFieldNames ? '' : 'inStable')
    ..aOS(3, _omitFieldNames ? '' : 'name')
    ..aOS(4, _omitFieldNames ? '' : 'gender')
    ..a<$core.int>(5, _omitFieldNames ? '' : 'birthYear', $pb.PbFieldType.O3)
    ..aOS(6, _omitFieldNames ? '' : 'profilePicPath')
    ..hasRequiredFields = false
  ;

  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  CreateHorseRequest clone() => CreateHorseRequest()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  CreateHorseRequest copyWith(void Function(CreateHorseRequest) updates) => super.copyWith((message) => updates(message as CreateHorseRequest)) as CreateHorseRequest;

  $pb.BuilderInfo get info_ => _i;

  @$core.pragma('dart2js:noInline')
  static CreateHorseRequest create() => CreateHorseRequest._();
  CreateHorseRequest createEmptyInstance() => create();
  static $pb.PbList<CreateHorseRequest> createRepeated() => $pb.PbList<CreateHorseRequest>();
  @$core.pragma('dart2js:noInline')
  static CreateHorseRequest getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<CreateHorseRequest>(create);
  static CreateHorseRequest? _defaultInstance;

  @$pb.TagNumber(1)
  $core.String get stableUuid => $_getSZ(0);
  @$pb.TagNumber(1)
  set stableUuid($core.String v) { $_setString(0, v); }
  @$pb.TagNumber(1)
  $core.bool hasStableUuid() => $_has(0);
  @$pb.TagNumber(1)
  void clearStableUuid() => clearField(1);

  @$pb.TagNumber(2)
  $core.bool get inStable => $_getBF(1);
  @$pb.TagNumber(2)
  set inStable($core.bool v) { $_setBool(1, v); }
  @$pb.TagNumber(2)
  $core.bool hasInStable() => $_has(1);
  @$pb.TagNumber(2)
  void clearInStable() => clearField(2);

  @$pb.TagNumber(3)
  $core.String get name => $_getSZ(2);
  @$pb.TagNumber(3)
  set name($core.String v) { $_setString(2, v); }
  @$pb.TagNumber(3)
  $core.bool hasName() => $_has(2);
  @$pb.TagNumber(3)
  void clearName() => clearField(3);

  @$pb.TagNumber(4)
  $core.String get gender => $_getSZ(3);
  @$pb.TagNumber(4)
  set gender($core.String v) { $_setString(3, v); }
  @$pb.TagNumber(4)
  $core.bool hasGender() => $_has(3);
  @$pb.TagNumber(4)
  void clearGender() => clearField(4);

  @$pb.TagNumber(5)
  $core.int get birthYear => $_getIZ(4);
  @$pb.TagNumber(5)
  set birthYear($core.int v) { $_setSignedInt32(4, v); }
  @$pb.TagNumber(5)
  $core.bool hasBirthYear() => $_has(4);
  @$pb.TagNumber(5)
  void clearBirthYear() => clearField(5);

  @$pb.TagNumber(6)
  $core.String get profilePicPath => $_getSZ(5);
  @$pb.TagNumber(6)
  set profilePicPath($core.String v) { $_setString(5, v); }
  @$pb.TagNumber(6)
  $core.bool hasProfilePicPath() => $_has(5);
  @$pb.TagNumber(6)
  void clearProfilePicPath() => clearField(6);
}

class CreateHorseResponse extends $pb.GeneratedMessage {
  factory CreateHorseResponse({
    $6.HorseDto? horse,
  }) {
    final $result = create();
    if (horse != null) {
      $result.horse = horse;
    }
    return $result;
  }
  CreateHorseResponse._() : super();
  factory CreateHorseResponse.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory CreateHorseResponse.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);

  static final $pb.BuilderInfo _i = $pb.BuilderInfo(_omitMessageNames ? '' : 'CreateHorseResponse', package: const $pb.PackageName(_omitMessageNames ? '' : 'connectrpc.app.v1'), createEmptyInstance: create)
    ..aOM<$6.HorseDto>(1, _omitFieldNames ? '' : 'horse', subBuilder: $6.HorseDto.create)
    ..hasRequiredFields = false
  ;

  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  CreateHorseResponse clone() => CreateHorseResponse()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  CreateHorseResponse copyWith(void Function(CreateHorseResponse) updates) => super.copyWith((message) => updates(message as CreateHorseResponse)) as CreateHorseResponse;

  $pb.BuilderInfo get info_ => _i;

  @$core.pragma('dart2js:noInline')
  static CreateHorseResponse create() => CreateHorseResponse._();
  CreateHorseResponse createEmptyInstance() => create();
  static $pb.PbList<CreateHorseResponse> createRepeated() => $pb.PbList<CreateHorseResponse>();
  @$core.pragma('dart2js:noInline')
  static CreateHorseResponse getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<CreateHorseResponse>(create);
  static CreateHorseResponse? _defaultInstance;

  @$pb.TagNumber(1)
  $6.HorseDto get horse => $_getN(0);
  @$pb.TagNumber(1)
  set horse($6.HorseDto v) { setField(1, v); }
  @$pb.TagNumber(1)
  $core.bool hasHorse() => $_has(0);
  @$pb.TagNumber(1)
  void clearHorse() => clearField(1);
  @$pb.TagNumber(1)
  $6.HorseDto ensureHorse() => $_ensure(0);
}

class PatchHorseRequest extends $pb.GeneratedMessage {
  factory PatchHorseRequest({
    $fixnum.Int64? horseId,
    $core.bool? inStable,
    $core.String? profilePicPath,
  }) {
    final $result = create();
    if (horseId != null) {
      $result.horseId = horseId;
    }
    if (inStable != null) {
      $result.inStable = inStable;
    }
    if (profilePicPath != null) {
      $result.profilePicPath = profilePicPath;
    }
    return $result;
  }
  PatchHorseRequest._() : super();
  factory PatchHorseRequest.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory PatchHorseRequest.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);

  static final $pb.BuilderInfo _i = $pb.BuilderInfo(_omitMessageNames ? '' : 'PatchHorseRequest', package: const $pb.PackageName(_omitMessageNames ? '' : 'connectrpc.app.v1'), createEmptyInstance: create)
    ..aInt64(1, _omitFieldNames ? '' : 'horseId')
    ..aOB(2, _omitFieldNames ? '' : 'inStable')
    ..aOS(3, _omitFieldNames ? '' : 'profilePicPath')
    ..hasRequiredFields = false
  ;

  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  PatchHorseRequest clone() => PatchHorseRequest()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  PatchHorseRequest copyWith(void Function(PatchHorseRequest) updates) => super.copyWith((message) => updates(message as PatchHorseRequest)) as PatchHorseRequest;

  $pb.BuilderInfo get info_ => _i;

  @$core.pragma('dart2js:noInline')
  static PatchHorseRequest create() => PatchHorseRequest._();
  PatchHorseRequest createEmptyInstance() => create();
  static $pb.PbList<PatchHorseRequest> createRepeated() => $pb.PbList<PatchHorseRequest>();
  @$core.pragma('dart2js:noInline')
  static PatchHorseRequest getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<PatchHorseRequest>(create);
  static PatchHorseRequest? _defaultInstance;

  @$pb.TagNumber(1)
  $fixnum.Int64 get horseId => $_getI64(0);
  @$pb.TagNumber(1)
  set horseId($fixnum.Int64 v) { $_setInt64(0, v); }
  @$pb.TagNumber(1)
  $core.bool hasHorseId() => $_has(0);
  @$pb.TagNumber(1)
  void clearHorseId() => clearField(1);

  @$pb.TagNumber(2)
  $core.bool get inStable => $_getBF(1);
  @$pb.TagNumber(2)
  set inStable($core.bool v) { $_setBool(1, v); }
  @$pb.TagNumber(2)
  $core.bool hasInStable() => $_has(1);
  @$pb.TagNumber(2)
  void clearInStable() => clearField(2);

  @$pb.TagNumber(3)
  $core.String get profilePicPath => $_getSZ(2);
  @$pb.TagNumber(3)
  set profilePicPath($core.String v) { $_setString(2, v); }
  @$pb.TagNumber(3)
  $core.bool hasProfilePicPath() => $_has(2);
  @$pb.TagNumber(3)
  void clearProfilePicPath() => clearField(3);
}

class PatchHorseResponse extends $pb.GeneratedMessage {
  factory PatchHorseResponse() => create();
  PatchHorseResponse._() : super();
  factory PatchHorseResponse.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory PatchHorseResponse.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);

  static final $pb.BuilderInfo _i = $pb.BuilderInfo(_omitMessageNames ? '' : 'PatchHorseResponse', package: const $pb.PackageName(_omitMessageNames ? '' : 'connectrpc.app.v1'), createEmptyInstance: create)
    ..hasRequiredFields = false
  ;

  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  PatchHorseResponse clone() => PatchHorseResponse()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  PatchHorseResponse copyWith(void Function(PatchHorseResponse) updates) => super.copyWith((message) => updates(message as PatchHorseResponse)) as PatchHorseResponse;

  $pb.BuilderInfo get info_ => _i;

  @$core.pragma('dart2js:noInline')
  static PatchHorseResponse create() => PatchHorseResponse._();
  PatchHorseResponse createEmptyInstance() => create();
  static $pb.PbList<PatchHorseResponse> createRepeated() => $pb.PbList<PatchHorseResponse>();
  @$core.pragma('dart2js:noInline')
  static PatchHorseResponse getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<PatchHorseResponse>(create);
  static PatchHorseResponse? _defaultInstance;
}

class PatchHorseManageStatusRequest extends $pb.GeneratedMessage {
  factory PatchHorseManageStatusRequest({
    $fixnum.Int64? horseId,
    ManageStatus? manageStatus,
  }) {
    final $result = create();
    if (horseId != null) {
      $result.horseId = horseId;
    }
    if (manageStatus != null) {
      $result.manageStatus = manageStatus;
    }
    return $result;
  }
  PatchHorseManageStatusRequest._() : super();
  factory PatchHorseManageStatusRequest.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory PatchHorseManageStatusRequest.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);

  static final $pb.BuilderInfo _i = $pb.BuilderInfo(_omitMessageNames ? '' : 'PatchHorseManageStatusRequest', package: const $pb.PackageName(_omitMessageNames ? '' : 'connectrpc.app.v1'), createEmptyInstance: create)
    ..aInt64(1, _omitFieldNames ? '' : 'horseId')
    ..e<ManageStatus>(2, _omitFieldNames ? '' : 'manageStatus', $pb.PbFieldType.OE, defaultOrMaker: ManageStatus.MANAGE_STATUS_UNSPECIFIED, valueOf: ManageStatus.valueOf, enumValues: ManageStatus.values)
    ..hasRequiredFields = false
  ;

  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  PatchHorseManageStatusRequest clone() => PatchHorseManageStatusRequest()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  PatchHorseManageStatusRequest copyWith(void Function(PatchHorseManageStatusRequest) updates) => super.copyWith((message) => updates(message as PatchHorseManageStatusRequest)) as PatchHorseManageStatusRequest;

  $pb.BuilderInfo get info_ => _i;

  @$core.pragma('dart2js:noInline')
  static PatchHorseManageStatusRequest create() => PatchHorseManageStatusRequest._();
  PatchHorseManageStatusRequest createEmptyInstance() => create();
  static $pb.PbList<PatchHorseManageStatusRequest> createRepeated() => $pb.PbList<PatchHorseManageStatusRequest>();
  @$core.pragma('dart2js:noInline')
  static PatchHorseManageStatusRequest getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<PatchHorseManageStatusRequest>(create);
  static PatchHorseManageStatusRequest? _defaultInstance;

  @$pb.TagNumber(1)
  $fixnum.Int64 get horseId => $_getI64(0);
  @$pb.TagNumber(1)
  set horseId($fixnum.Int64 v) { $_setInt64(0, v); }
  @$pb.TagNumber(1)
  $core.bool hasHorseId() => $_has(0);
  @$pb.TagNumber(1)
  void clearHorseId() => clearField(1);

  @$pb.TagNumber(2)
  ManageStatus get manageStatus => $_getN(1);
  @$pb.TagNumber(2)
  set manageStatus(ManageStatus v) { setField(2, v); }
  @$pb.TagNumber(2)
  $core.bool hasManageStatus() => $_has(1);
  @$pb.TagNumber(2)
  void clearManageStatus() => clearField(2);
}

class PatchHorseManageStatusResponse extends $pb.GeneratedMessage {
  factory PatchHorseManageStatusResponse() => create();
  PatchHorseManageStatusResponse._() : super();
  factory PatchHorseManageStatusResponse.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory PatchHorseManageStatusResponse.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);

  static final $pb.BuilderInfo _i = $pb.BuilderInfo(_omitMessageNames ? '' : 'PatchHorseManageStatusResponse', package: const $pb.PackageName(_omitMessageNames ? '' : 'connectrpc.app.v1'), createEmptyInstance: create)
    ..hasRequiredFields = false
  ;

  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  PatchHorseManageStatusResponse clone() => PatchHorseManageStatusResponse()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  PatchHorseManageStatusResponse copyWith(void Function(PatchHorseManageStatusResponse) updates) => super.copyWith((message) => updates(message as PatchHorseManageStatusResponse)) as PatchHorseManageStatusResponse;

  $pb.BuilderInfo get info_ => _i;

  @$core.pragma('dart2js:noInline')
  static PatchHorseManageStatusResponse create() => PatchHorseManageStatusResponse._();
  PatchHorseManageStatusResponse createEmptyInstance() => create();
  static $pb.PbList<PatchHorseManageStatusResponse> createRepeated() => $pb.PbList<PatchHorseManageStatusResponse>();
  @$core.pragma('dart2js:noInline')
  static PatchHorseManageStatusResponse getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<PatchHorseManageStatusResponse>(create);
  static PatchHorseManageStatusResponse? _defaultInstance;
}

class TransferStableRequest extends $pb.GeneratedMessage {
  factory TransferStableRequest({
    $fixnum.Int64? horseId,
    $core.String? stableUuid,
  }) {
    final $result = create();
    if (horseId != null) {
      $result.horseId = horseId;
    }
    if (stableUuid != null) {
      $result.stableUuid = stableUuid;
    }
    return $result;
  }
  TransferStableRequest._() : super();
  factory TransferStableRequest.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory TransferStableRequest.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);

  static final $pb.BuilderInfo _i = $pb.BuilderInfo(_omitMessageNames ? '' : 'TransferStableRequest', package: const $pb.PackageName(_omitMessageNames ? '' : 'connectrpc.app.v1'), createEmptyInstance: create)
    ..aInt64(1, _omitFieldNames ? '' : 'horseId')
    ..aOS(2, _omitFieldNames ? '' : 'stableUuid')
    ..hasRequiredFields = false
  ;

  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  TransferStableRequest clone() => TransferStableRequest()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  TransferStableRequest copyWith(void Function(TransferStableRequest) updates) => super.copyWith((message) => updates(message as TransferStableRequest)) as TransferStableRequest;

  $pb.BuilderInfo get info_ => _i;

  @$core.pragma('dart2js:noInline')
  static TransferStableRequest create() => TransferStableRequest._();
  TransferStableRequest createEmptyInstance() => create();
  static $pb.PbList<TransferStableRequest> createRepeated() => $pb.PbList<TransferStableRequest>();
  @$core.pragma('dart2js:noInline')
  static TransferStableRequest getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<TransferStableRequest>(create);
  static TransferStableRequest? _defaultInstance;

  @$pb.TagNumber(1)
  $fixnum.Int64 get horseId => $_getI64(0);
  @$pb.TagNumber(1)
  set horseId($fixnum.Int64 v) { $_setInt64(0, v); }
  @$pb.TagNumber(1)
  $core.bool hasHorseId() => $_has(0);
  @$pb.TagNumber(1)
  void clearHorseId() => clearField(1);

  @$pb.TagNumber(2)
  $core.String get stableUuid => $_getSZ(1);
  @$pb.TagNumber(2)
  set stableUuid($core.String v) { $_setString(1, v); }
  @$pb.TagNumber(2)
  $core.bool hasStableUuid() => $_has(1);
  @$pb.TagNumber(2)
  void clearStableUuid() => clearField(2);
}

class TransferStableResponse extends $pb.GeneratedMessage {
  factory TransferStableResponse() => create();
  TransferStableResponse._() : super();
  factory TransferStableResponse.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory TransferStableResponse.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);

  static final $pb.BuilderInfo _i = $pb.BuilderInfo(_omitMessageNames ? '' : 'TransferStableResponse', package: const $pb.PackageName(_omitMessageNames ? '' : 'connectrpc.app.v1'), createEmptyInstance: create)
    ..hasRequiredFields = false
  ;

  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  TransferStableResponse clone() => TransferStableResponse()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  TransferStableResponse copyWith(void Function(TransferStableResponse) updates) => super.copyWith((message) => updates(message as TransferStableResponse)) as TransferStableResponse;

  $pb.BuilderInfo get info_ => _i;

  @$core.pragma('dart2js:noInline')
  static TransferStableResponse create() => TransferStableResponse._();
  TransferStableResponse createEmptyInstance() => create();
  static $pb.PbList<TransferStableResponse> createRepeated() => $pb.PbList<TransferStableResponse>();
  @$core.pragma('dart2js:noInline')
  static TransferStableResponse getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<TransferStableResponse>(create);
  static TransferStableResponse? _defaultInstance;
}

class DeleteHorseRequest extends $pb.GeneratedMessage {
  factory DeleteHorseRequest({
    $fixnum.Int64? horseId,
  }) {
    final $result = create();
    if (horseId != null) {
      $result.horseId = horseId;
    }
    return $result;
  }
  DeleteHorseRequest._() : super();
  factory DeleteHorseRequest.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory DeleteHorseRequest.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);

  static final $pb.BuilderInfo _i = $pb.BuilderInfo(_omitMessageNames ? '' : 'DeleteHorseRequest', package: const $pb.PackageName(_omitMessageNames ? '' : 'connectrpc.app.v1'), createEmptyInstance: create)
    ..aInt64(1, _omitFieldNames ? '' : 'horseId')
    ..hasRequiredFields = false
  ;

  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  DeleteHorseRequest clone() => DeleteHorseRequest()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  DeleteHorseRequest copyWith(void Function(DeleteHorseRequest) updates) => super.copyWith((message) => updates(message as DeleteHorseRequest)) as DeleteHorseRequest;

  $pb.BuilderInfo get info_ => _i;

  @$core.pragma('dart2js:noInline')
  static DeleteHorseRequest create() => DeleteHorseRequest._();
  DeleteHorseRequest createEmptyInstance() => create();
  static $pb.PbList<DeleteHorseRequest> createRepeated() => $pb.PbList<DeleteHorseRequest>();
  @$core.pragma('dart2js:noInline')
  static DeleteHorseRequest getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<DeleteHorseRequest>(create);
  static DeleteHorseRequest? _defaultInstance;

  @$pb.TagNumber(1)
  $fixnum.Int64 get horseId => $_getI64(0);
  @$pb.TagNumber(1)
  set horseId($fixnum.Int64 v) { $_setInt64(0, v); }
  @$pb.TagNumber(1)
  $core.bool hasHorseId() => $_has(0);
  @$pb.TagNumber(1)
  void clearHorseId() => clearField(1);
}

class DeleteHorseResponse extends $pb.GeneratedMessage {
  factory DeleteHorseResponse() => create();
  DeleteHorseResponse._() : super();
  factory DeleteHorseResponse.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory DeleteHorseResponse.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);

  static final $pb.BuilderInfo _i = $pb.BuilderInfo(_omitMessageNames ? '' : 'DeleteHorseResponse', package: const $pb.PackageName(_omitMessageNames ? '' : 'connectrpc.app.v1'), createEmptyInstance: create)
    ..hasRequiredFields = false
  ;

  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  DeleteHorseResponse clone() => DeleteHorseResponse()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  DeleteHorseResponse copyWith(void Function(DeleteHorseResponse) updates) => super.copyWith((message) => updates(message as DeleteHorseResponse)) as DeleteHorseResponse;

  $pb.BuilderInfo get info_ => _i;

  @$core.pragma('dart2js:noInline')
  static DeleteHorseResponse create() => DeleteHorseResponse._();
  DeleteHorseResponse createEmptyInstance() => create();
  static $pb.PbList<DeleteHorseResponse> createRepeated() => $pb.PbList<DeleteHorseResponse>();
  @$core.pragma('dart2js:noInline')
  static DeleteHorseResponse getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<DeleteHorseResponse>(create);
  static DeleteHorseResponse? _defaultInstance;
}

class GetHorseTrainingScoresRequest extends $pb.GeneratedMessage {
  factory GetHorseTrainingScoresRequest({
    $core.int? horseId,
  }) {
    final $result = create();
    if (horseId != null) {
      $result.horseId = horseId;
    }
    return $result;
  }
  GetHorseTrainingScoresRequest._() : super();
  factory GetHorseTrainingScoresRequest.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory GetHorseTrainingScoresRequest.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);

  static final $pb.BuilderInfo _i = $pb.BuilderInfo(_omitMessageNames ? '' : 'GetHorseTrainingScoresRequest', package: const $pb.PackageName(_omitMessageNames ? '' : 'connectrpc.app.v1'), createEmptyInstance: create)
    ..a<$core.int>(1, _omitFieldNames ? '' : 'horseId', $pb.PbFieldType.O3)
    ..hasRequiredFields = false
  ;

  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  GetHorseTrainingScoresRequest clone() => GetHorseTrainingScoresRequest()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  GetHorseTrainingScoresRequest copyWith(void Function(GetHorseTrainingScoresRequest) updates) => super.copyWith((message) => updates(message as GetHorseTrainingScoresRequest)) as GetHorseTrainingScoresRequest;

  $pb.BuilderInfo get info_ => _i;

  @$core.pragma('dart2js:noInline')
  static GetHorseTrainingScoresRequest create() => GetHorseTrainingScoresRequest._();
  GetHorseTrainingScoresRequest createEmptyInstance() => create();
  static $pb.PbList<GetHorseTrainingScoresRequest> createRepeated() => $pb.PbList<GetHorseTrainingScoresRequest>();
  @$core.pragma('dart2js:noInline')
  static GetHorseTrainingScoresRequest getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<GetHorseTrainingScoresRequest>(create);
  static GetHorseTrainingScoresRequest? _defaultInstance;

  @$pb.TagNumber(1)
  $core.int get horseId => $_getIZ(0);
  @$pb.TagNumber(1)
  set horseId($core.int v) { $_setSignedInt32(0, v); }
  @$pb.TagNumber(1)
  $core.bool hasHorseId() => $_has(0);
  @$pb.TagNumber(1)
  void clearHorseId() => clearField(1);
}

class GetHorseTrainingScoresResponse extends $pb.GeneratedMessage {
  factory GetHorseTrainingScoresResponse({
    PitchScore? pitchScore,
  }) {
    final $result = create();
    if (pitchScore != null) {
      $result.pitchScore = pitchScore;
    }
    return $result;
  }
  GetHorseTrainingScoresResponse._() : super();
  factory GetHorseTrainingScoresResponse.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory GetHorseTrainingScoresResponse.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);

  static final $pb.BuilderInfo _i = $pb.BuilderInfo(_omitMessageNames ? '' : 'GetHorseTrainingScoresResponse', package: const $pb.PackageName(_omitMessageNames ? '' : 'connectrpc.app.v1'), createEmptyInstance: create)
    ..e<PitchScore>(1, _omitFieldNames ? '' : 'pitchScore', $pb.PbFieldType.OE, defaultOrMaker: PitchScore.PITCH_SCORE_UNSPECIFIED, valueOf: PitchScore.valueOf, enumValues: PitchScore.values)
    ..hasRequiredFields = false
  ;

  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  GetHorseTrainingScoresResponse clone() => GetHorseTrainingScoresResponse()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  GetHorseTrainingScoresResponse copyWith(void Function(GetHorseTrainingScoresResponse) updates) => super.copyWith((message) => updates(message as GetHorseTrainingScoresResponse)) as GetHorseTrainingScoresResponse;

  $pb.BuilderInfo get info_ => _i;

  @$core.pragma('dart2js:noInline')
  static GetHorseTrainingScoresResponse create() => GetHorseTrainingScoresResponse._();
  GetHorseTrainingScoresResponse createEmptyInstance() => create();
  static $pb.PbList<GetHorseTrainingScoresResponse> createRepeated() => $pb.PbList<GetHorseTrainingScoresResponse>();
  @$core.pragma('dart2js:noInline')
  static GetHorseTrainingScoresResponse getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<GetHorseTrainingScoresResponse>(create);
  static GetHorseTrainingScoresResponse? _defaultInstance;

  @$pb.TagNumber(1)
  PitchScore get pitchScore => $_getN(0);
  @$pb.TagNumber(1)
  set pitchScore(PitchScore v) { setField(1, v); }
  @$pb.TagNumber(1)
  $core.bool hasPitchScore() => $_has(0);
  @$pb.TagNumber(1)
  void clearPitchScore() => clearField(1);
}


const _omitFieldNames = $core.bool.fromEnvironment('protobuf.omit_field_names');
const _omitMessageNames = $core.bool.fromEnvironment('protobuf.omit_message_names');
