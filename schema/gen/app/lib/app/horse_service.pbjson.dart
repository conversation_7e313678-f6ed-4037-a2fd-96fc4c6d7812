//
//  Generated code. Do not modify.
//  source: app/horse_service.proto
//
// @dart = 2.12

// ignore_for_file: annotate_overrides, camel_case_types, comment_references
// ignore_for_file: constant_identifier_names, library_prefixes
// ignore_for_file: non_constant_identifier_names, prefer_final_fields
// ignore_for_file: unnecessary_import, unnecessary_this, unused_import

import 'dart:convert' as $convert;
import 'dart:core' as $core;
import 'dart:typed_data' as $typed_data;

@$core.Deprecated('Use sortByDescriptor instead')
const SortBy$json = {
  '1': 'SortBy',
  '2': [
    {'1': 'SORT_BY_UNSPECIFIED', '2': 0},
    {'1': 'SORT_BY_NAME', '2': 1},
    {'1': 'SORT_BY_TRAINING_DATE', '2': 2},
  ],
};

/// Descriptor for `SortBy`. Decode as a `google.protobuf.EnumDescriptorProto`.
final $typed_data.Uint8List sortByDescriptor = $convert.base64Decode(
    'CgZTb3J0QnkSFwoTU09SVF9CWV9VTlNQRUNJRklFRBAAEhAKDFNPUlRfQllfTkFNRRABEhkKFV'
    'NPUlRfQllfVFJBSU5JTkdfREFURRAC');

@$core.Deprecated('Use manageStatusDescriptor instead')
const ManageStatus$json = {
  '1': 'ManageStatus',
  '2': [
    {'1': 'MANAGE_STATUS_UNSPECIFIED', '2': 0},
    {'1': 'MANAGE_STATUS_MANAGED', '2': 1},
    {'1': 'MANAGE_STATUS_UNMANAGED', '2': 2},
  ],
};

/// Descriptor for `ManageStatus`. Decode as a `google.protobuf.EnumDescriptorProto`.
final $typed_data.Uint8List manageStatusDescriptor = $convert.base64Decode(
    'CgxNYW5hZ2VTdGF0dXMSHQoZTUFOQUdFX1NUQVRVU19VTlNQRUNJRklFRBAAEhkKFU1BTkFHRV'
    '9TVEFUVVNfTUFOQUdFRBABEhsKF01BTkFHRV9TVEFUVVNfVU5NQU5BR0VEEAI=');

@$core.Deprecated('Use pitchScoreDescriptor instead')
const PitchScore$json = {
  '1': 'PitchScore',
  '2': [
    {'1': 'PITCH_SCORE_UNSPECIFIED', '2': 0},
    {'1': 'PITCH_SCORE_PITCH_LOW', '2': 1},
    {'1': 'PITCH_SCORE_PITCH_SLIGHTLY_LOW', '2': 2},
    {'1': 'PITCH_SCORE_PITCH_MEDIUM', '2': 3},
    {'1': 'PITCH_SCORE_PITCH_SLIGHTLY_HIGH', '2': 4},
    {'1': 'PITCH_SCORE_PITCH_HIGH', '2': 5},
  ],
};

/// Descriptor for `PitchScore`. Decode as a `google.protobuf.EnumDescriptorProto`.
final $typed_data.Uint8List pitchScoreDescriptor = $convert.base64Decode(
    'CgpQaXRjaFNjb3JlEhsKF1BJVENIX1NDT1JFX1VOU1BFQ0lGSUVEEAASGQoVUElUQ0hfU0NPUk'
    'VfUElUQ0hfTE9XEAESIgoeUElUQ0hfU0NPUkVfUElUQ0hfU0xJR0hUTFlfTE9XEAISHAoYUElU'
    'Q0hfU0NPUkVfUElUQ0hfTUVESVVNEAMSIwofUElUQ0hfU0NPUkVfUElUQ0hfU0xJR0hUTFlfSE'
    'lHSBAEEhoKFlBJVENIX1NDT1JFX1BJVENIX0hJR0gQBQ==');

@$core.Deprecated('Use listHorsesRequestDescriptor instead')
const ListHorsesRequest$json = {
  '1': 'ListHorsesRequest',
  '2': [
    {'1': 'sort_by', '3': 1, '4': 1, '5': 14, '6': '.connectrpc.app.v1.SortBy', '10': 'sortBy'},
  ],
};

/// Descriptor for `ListHorsesRequest`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List listHorsesRequestDescriptor = $convert.base64Decode(
    'ChFMaXN0SG9yc2VzUmVxdWVzdBIyCgdzb3J0X2J5GAEgASgOMhkuY29ubmVjdHJwYy5hcHAudj'
    'EuU29ydEJ5UgZzb3J0Qnk=');

@$core.Deprecated('Use listHorsesResponseDescriptor instead')
const ListHorsesResponse$json = {
  '1': 'ListHorsesResponse',
  '2': [
    {'1': 'horses', '3': 1, '4': 3, '5': 11, '6': '.connectrpc.app.v1.HorseDto', '10': 'horses'},
  ],
};

/// Descriptor for `ListHorsesResponse`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List listHorsesResponseDescriptor = $convert.base64Decode(
    'ChJMaXN0SG9yc2VzUmVzcG9uc2USMwoGaG9yc2VzGAEgAygLMhsuY29ubmVjdHJwYy5hcHAudj'
    'EuSG9yc2VEdG9SBmhvcnNlcw==');

@$core.Deprecated('Use getHorseRequestDescriptor instead')
const GetHorseRequest$json = {
  '1': 'GetHorseRequest',
  '2': [
    {'1': 'horse_id', '3': 1, '4': 1, '5': 3, '10': 'horseId'},
  ],
};

/// Descriptor for `GetHorseRequest`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List getHorseRequestDescriptor = $convert.base64Decode(
    'Cg9HZXRIb3JzZVJlcXVlc3QSGQoIaG9yc2VfaWQYASABKANSB2hvcnNlSWQ=');

@$core.Deprecated('Use getHorseResponseDescriptor instead')
const GetHorseResponse$json = {
  '1': 'GetHorseResponse',
  '2': [
    {'1': 'horse', '3': 1, '4': 1, '5': 11, '6': '.connectrpc.app.v1.HorseDto', '10': 'horse'},
  ],
};

/// Descriptor for `GetHorseResponse`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List getHorseResponseDescriptor = $convert.base64Decode(
    'ChBHZXRIb3JzZVJlc3BvbnNlEjEKBWhvcnNlGAEgASgLMhsuY29ubmVjdHJwYy5hcHAudjEuSG'
    '9yc2VEdG9SBWhvcnNl');

@$core.Deprecated('Use createHorseFromMasterHorseRequestDescriptor instead')
const CreateHorseFromMasterHorseRequest$json = {
  '1': 'CreateHorseFromMasterHorseRequest',
  '2': [
    {'1': 'stable_uuid', '3': 1, '4': 1, '5': 9, '10': 'stableUuid'},
    {'1': 'in_stable', '3': 2, '4': 1, '5': 8, '10': 'inStable'},
    {'1': 'master_horse_id', '3': 3, '4': 1, '5': 9, '10': 'masterHorseId'},
    {'1': 'profile_pic_path', '3': 4, '4': 1, '5': 9, '9': 0, '10': 'profilePicPath', '17': true},
  ],
  '8': [
    {'1': '_profile_pic_path'},
  ],
};

/// Descriptor for `CreateHorseFromMasterHorseRequest`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List createHorseFromMasterHorseRequestDescriptor = $convert.base64Decode(
    'CiFDcmVhdGVIb3JzZUZyb21NYXN0ZXJIb3JzZVJlcXVlc3QSHwoLc3RhYmxlX3V1aWQYASABKA'
    'lSCnN0YWJsZVV1aWQSGwoJaW5fc3RhYmxlGAIgASgIUghpblN0YWJsZRImCg9tYXN0ZXJfaG9y'
    'c2VfaWQYAyABKAlSDW1hc3RlckhvcnNlSWQSLQoQcHJvZmlsZV9waWNfcGF0aBgEIAEoCUgAUg'
    '5wcm9maWxlUGljUGF0aIgBAUITChFfcHJvZmlsZV9waWNfcGF0aA==');

@$core.Deprecated('Use createHorseFromMasterHorseResponseDescriptor instead')
const CreateHorseFromMasterHorseResponse$json = {
  '1': 'CreateHorseFromMasterHorseResponse',
  '2': [
    {'1': 'horse', '3': 1, '4': 1, '5': 11, '6': '.connectrpc.app.v1.HorseDto', '10': 'horse'},
  ],
};

/// Descriptor for `CreateHorseFromMasterHorseResponse`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List createHorseFromMasterHorseResponseDescriptor = $convert.base64Decode(
    'CiJDcmVhdGVIb3JzZUZyb21NYXN0ZXJIb3JzZVJlc3BvbnNlEjEKBWhvcnNlGAEgASgLMhsuY2'
    '9ubmVjdHJwYy5hcHAudjEuSG9yc2VEdG9SBWhvcnNl');

@$core.Deprecated('Use createHorseRequestDescriptor instead')
const CreateHorseRequest$json = {
  '1': 'CreateHorseRequest',
  '2': [
    {'1': 'stable_uuid', '3': 1, '4': 1, '5': 9, '10': 'stableUuid'},
    {'1': 'in_stable', '3': 2, '4': 1, '5': 8, '10': 'inStable'},
    {'1': 'name', '3': 3, '4': 1, '5': 9, '10': 'name'},
    {'1': 'gender', '3': 4, '4': 1, '5': 9, '10': 'gender'},
    {'1': 'birth_year', '3': 5, '4': 1, '5': 5, '10': 'birthYear'},
    {'1': 'profile_pic_path', '3': 6, '4': 1, '5': 9, '9': 0, '10': 'profilePicPath', '17': true},
  ],
  '8': [
    {'1': '_profile_pic_path'},
  ],
};

/// Descriptor for `CreateHorseRequest`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List createHorseRequestDescriptor = $convert.base64Decode(
    'ChJDcmVhdGVIb3JzZVJlcXVlc3QSHwoLc3RhYmxlX3V1aWQYASABKAlSCnN0YWJsZVV1aWQSGw'
    'oJaW5fc3RhYmxlGAIgASgIUghpblN0YWJsZRISCgRuYW1lGAMgASgJUgRuYW1lEhYKBmdlbmRl'
    'chgEIAEoCVIGZ2VuZGVyEh0KCmJpcnRoX3llYXIYBSABKAVSCWJpcnRoWWVhchItChBwcm9maW'
    'xlX3BpY19wYXRoGAYgASgJSABSDnByb2ZpbGVQaWNQYXRoiAEBQhMKEV9wcm9maWxlX3BpY19w'
    'YXRo');

@$core.Deprecated('Use createHorseResponseDescriptor instead')
const CreateHorseResponse$json = {
  '1': 'CreateHorseResponse',
  '2': [
    {'1': 'horse', '3': 1, '4': 1, '5': 11, '6': '.connectrpc.app.v1.HorseDto', '10': 'horse'},
  ],
};

/// Descriptor for `CreateHorseResponse`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List createHorseResponseDescriptor = $convert.base64Decode(
    'ChNDcmVhdGVIb3JzZVJlc3BvbnNlEjEKBWhvcnNlGAEgASgLMhsuY29ubmVjdHJwYy5hcHAudj'
    'EuSG9yc2VEdG9SBWhvcnNl');

@$core.Deprecated('Use patchHorseRequestDescriptor instead')
const PatchHorseRequest$json = {
  '1': 'PatchHorseRequest',
  '2': [
    {'1': 'horse_id', '3': 1, '4': 1, '5': 3, '10': 'horseId'},
    {'1': 'in_stable', '3': 2, '4': 1, '5': 8, '10': 'inStable'},
    {'1': 'profile_pic_path', '3': 3, '4': 1, '5': 9, '9': 0, '10': 'profilePicPath', '17': true},
  ],
  '8': [
    {'1': '_profile_pic_path'},
  ],
};

/// Descriptor for `PatchHorseRequest`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List patchHorseRequestDescriptor = $convert.base64Decode(
    'ChFQYXRjaEhvcnNlUmVxdWVzdBIZCghob3JzZV9pZBgBIAEoA1IHaG9yc2VJZBIbCglpbl9zdG'
    'FibGUYAiABKAhSCGluU3RhYmxlEi0KEHByb2ZpbGVfcGljX3BhdGgYAyABKAlIAFIOcHJvZmls'
    'ZVBpY1BhdGiIAQFCEwoRX3Byb2ZpbGVfcGljX3BhdGg=');

@$core.Deprecated('Use patchHorseResponseDescriptor instead')
const PatchHorseResponse$json = {
  '1': 'PatchHorseResponse',
};

/// Descriptor for `PatchHorseResponse`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List patchHorseResponseDescriptor = $convert.base64Decode(
    'ChJQYXRjaEhvcnNlUmVzcG9uc2U=');

@$core.Deprecated('Use patchHorseManageStatusRequestDescriptor instead')
const PatchHorseManageStatusRequest$json = {
  '1': 'PatchHorseManageStatusRequest',
  '2': [
    {'1': 'horse_id', '3': 1, '4': 1, '5': 3, '10': 'horseId'},
    {'1': 'manage_status', '3': 2, '4': 1, '5': 14, '6': '.connectrpc.app.v1.ManageStatus', '10': 'manageStatus'},
  ],
};

/// Descriptor for `PatchHorseManageStatusRequest`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List patchHorseManageStatusRequestDescriptor = $convert.base64Decode(
    'Ch1QYXRjaEhvcnNlTWFuYWdlU3RhdHVzUmVxdWVzdBIZCghob3JzZV9pZBgBIAEoA1IHaG9yc2'
    'VJZBJECg1tYW5hZ2Vfc3RhdHVzGAIgASgOMh8uY29ubmVjdHJwYy5hcHAudjEuTWFuYWdlU3Rh'
    'dHVzUgxtYW5hZ2VTdGF0dXM=');

@$core.Deprecated('Use patchHorseManageStatusResponseDescriptor instead')
const PatchHorseManageStatusResponse$json = {
  '1': 'PatchHorseManageStatusResponse',
};

/// Descriptor for `PatchHorseManageStatusResponse`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List patchHorseManageStatusResponseDescriptor = $convert.base64Decode(
    'Ch5QYXRjaEhvcnNlTWFuYWdlU3RhdHVzUmVzcG9uc2U=');

@$core.Deprecated('Use transferStableRequestDescriptor instead')
const TransferStableRequest$json = {
  '1': 'TransferStableRequest',
  '2': [
    {'1': 'horse_id', '3': 1, '4': 1, '5': 3, '10': 'horseId'},
    {'1': 'stable_uuid', '3': 2, '4': 1, '5': 9, '10': 'stableUuid'},
  ],
};

/// Descriptor for `TransferStableRequest`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List transferStableRequestDescriptor = $convert.base64Decode(
    'ChVUcmFuc2ZlclN0YWJsZVJlcXVlc3QSGQoIaG9yc2VfaWQYASABKANSB2hvcnNlSWQSHwoLc3'
    'RhYmxlX3V1aWQYAiABKAlSCnN0YWJsZVV1aWQ=');

@$core.Deprecated('Use transferStableResponseDescriptor instead')
const TransferStableResponse$json = {
  '1': 'TransferStableResponse',
};

/// Descriptor for `TransferStableResponse`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List transferStableResponseDescriptor = $convert.base64Decode(
    'ChZUcmFuc2ZlclN0YWJsZVJlc3BvbnNl');

@$core.Deprecated('Use deleteHorseRequestDescriptor instead')
const DeleteHorseRequest$json = {
  '1': 'DeleteHorseRequest',
  '2': [
    {'1': 'horse_id', '3': 1, '4': 1, '5': 3, '10': 'horseId'},
  ],
};

/// Descriptor for `DeleteHorseRequest`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List deleteHorseRequestDescriptor = $convert.base64Decode(
    'ChJEZWxldGVIb3JzZVJlcXVlc3QSGQoIaG9yc2VfaWQYASABKANSB2hvcnNlSWQ=');

@$core.Deprecated('Use deleteHorseResponseDescriptor instead')
const DeleteHorseResponse$json = {
  '1': 'DeleteHorseResponse',
};

/// Descriptor for `DeleteHorseResponse`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List deleteHorseResponseDescriptor = $convert.base64Decode(
    'ChNEZWxldGVIb3JzZVJlc3BvbnNl');

@$core.Deprecated('Use getHorseTrainingScoresRequestDescriptor instead')
const GetHorseTrainingScoresRequest$json = {
  '1': 'GetHorseTrainingScoresRequest',
  '2': [
    {'1': 'horse_id', '3': 1, '4': 1, '5': 5, '10': 'horseId'},
  ],
};

/// Descriptor for `GetHorseTrainingScoresRequest`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List getHorseTrainingScoresRequestDescriptor = $convert.base64Decode(
    'Ch1HZXRIb3JzZVRyYWluaW5nU2NvcmVzUmVxdWVzdBIZCghob3JzZV9pZBgBIAEoBVIHaG9yc2'
    'VJZA==');

@$core.Deprecated('Use getHorseTrainingScoresResponseDescriptor instead')
const GetHorseTrainingScoresResponse$json = {
  '1': 'GetHorseTrainingScoresResponse',
  '2': [
    {'1': 'pitch_score', '3': 1, '4': 1, '5': 14, '6': '.connectrpc.app.v1.PitchScore', '10': 'pitchScore'},
  ],
};

/// Descriptor for `GetHorseTrainingScoresResponse`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List getHorseTrainingScoresResponseDescriptor = $convert.base64Decode(
    'Ch5HZXRIb3JzZVRyYWluaW5nU2NvcmVzUmVzcG9uc2USPgoLcGl0Y2hfc2NvcmUYASABKA4yHS'
    '5jb25uZWN0cnBjLmFwcC52MS5QaXRjaFNjb3JlUgpwaXRjaFNjb3Jl');

