//
//  Generated code. Do not modify.
//  source: app/training_indicator_service.proto
//
// @dart = 2.12

// ignore_for_file: annotate_overrides, camel_case_types, comment_references
// ignore_for_file: constant_identifier_names, library_prefixes
// ignore_for_file: non_constant_identifier_names, prefer_final_fields
// ignore_for_file: unnecessary_import, unnecessary_this, unused_import

import 'dart:async' as $async;
import 'dart:core' as $core;

import 'package:grpc/service_api.dart' as $grpc;
import 'package:protobuf/protobuf.dart' as $pb;

import 'training_indicator_service.pb.dart' as $3;

export 'training_indicator_service.pb.dart';

@$pb.GrpcServiceName('connectrpc.app.v1.TrainingIndicatorService')
class TrainingIndicatorServiceClient extends $grpc.Client {
  static final _$getTrainingIndicator = $grpc.ClientMethod<$3.GetTrainingIndicatorRequest, $3.GetTrainingIndicatorResponse>(
      '/connectrpc.app.v1.TrainingIndicatorService/GetTrainingIndicator',
      ($3.GetTrainingIndicatorRequest value) => value.writeToBuffer(),
      ($core.List<$core.int> value) => $3.GetTrainingIndicatorResponse.fromBuffer(value));
  static final _$listTrainingIndicatorLabels = $grpc.ClientMethod<$3.ListTrainingIndicatorLabelsRequest, $3.ListTrainingIndicatorLabelsResponse>(
      '/connectrpc.app.v1.TrainingIndicatorService/ListTrainingIndicatorLabels',
      ($3.ListTrainingIndicatorLabelsRequest value) => value.writeToBuffer(),
      ($core.List<$core.int> value) => $3.ListTrainingIndicatorLabelsResponse.fromBuffer(value));

  TrainingIndicatorServiceClient($grpc.ClientChannel channel,
      {$grpc.CallOptions? options,
      $core.Iterable<$grpc.ClientInterceptor>? interceptors})
      : super(channel, options: options,
        interceptors: interceptors);

  $grpc.ResponseFuture<$3.GetTrainingIndicatorResponse> getTrainingIndicator($3.GetTrainingIndicatorRequest request, {$grpc.CallOptions? options}) {
    return $createUnaryCall(_$getTrainingIndicator, request, options: options);
  }

  $grpc.ResponseFuture<$3.ListTrainingIndicatorLabelsResponse> listTrainingIndicatorLabels($3.ListTrainingIndicatorLabelsRequest request, {$grpc.CallOptions? options}) {
    return $createUnaryCall(_$listTrainingIndicatorLabels, request, options: options);
  }
}

@$pb.GrpcServiceName('connectrpc.app.v1.TrainingIndicatorService')
abstract class TrainingIndicatorServiceBase extends $grpc.Service {
  $core.String get $name => 'connectrpc.app.v1.TrainingIndicatorService';

  TrainingIndicatorServiceBase() {
    $addMethod($grpc.ServiceMethod<$3.GetTrainingIndicatorRequest, $3.GetTrainingIndicatorResponse>(
        'GetTrainingIndicator',
        getTrainingIndicator_Pre,
        false,
        false,
        ($core.List<$core.int> value) => $3.GetTrainingIndicatorRequest.fromBuffer(value),
        ($3.GetTrainingIndicatorResponse value) => value.writeToBuffer()));
    $addMethod($grpc.ServiceMethod<$3.ListTrainingIndicatorLabelsRequest, $3.ListTrainingIndicatorLabelsResponse>(
        'ListTrainingIndicatorLabels',
        listTrainingIndicatorLabels_Pre,
        false,
        false,
        ($core.List<$core.int> value) => $3.ListTrainingIndicatorLabelsRequest.fromBuffer(value),
        ($3.ListTrainingIndicatorLabelsResponse value) => value.writeToBuffer()));
  }

  $async.Future<$3.GetTrainingIndicatorResponse> getTrainingIndicator_Pre($grpc.ServiceCall call, $async.Future<$3.GetTrainingIndicatorRequest> request) async {
    return getTrainingIndicator(call, await request);
  }

  $async.Future<$3.ListTrainingIndicatorLabelsResponse> listTrainingIndicatorLabels_Pre($grpc.ServiceCall call, $async.Future<$3.ListTrainingIndicatorLabelsRequest> request) async {
    return listTrainingIndicatorLabels(call, await request);
  }

  $async.Future<$3.GetTrainingIndicatorResponse> getTrainingIndicator($grpc.ServiceCall call, $3.GetTrainingIndicatorRequest request);
  $async.Future<$3.ListTrainingIndicatorLabelsResponse> listTrainingIndicatorLabels($grpc.ServiceCall call, $3.ListTrainingIndicatorLabelsRequest request);
}
