// @generated by protoc-gen-es v2.2.3 with parameter "target=ts"
// @generated from file app/horse_service.proto (package connectrpc.app.v1, syntax proto3)
/* eslint-disable */

import type { GenEnum, GenFile, GenMessage, GenService } from "@bufbuild/protobuf/codegenv1";
import { enumDesc, fileDesc, messageDesc, serviceDesc } from "@bufbuild/protobuf/codegenv1";
import type { HorseDto } from "./models/horse_dto_pb";
import { file_app_models_horse_dto } from "./models/horse_dto_pb";
import type { Message } from "@bufbuild/protobuf";

/**
 * Describes the file app/horse_service.proto.
 */
export const file_app_horse_service: GenFile = /*@__PURE__*/
  fileDesc("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", [file_app_models_horse_dto]);

/**
 * @generated from message connectrpc.app.v1.ListHorsesRequest
 */
export type ListHorsesRequest = Message<"connectrpc.app.v1.ListHorsesRequest"> & {
  /**
   * @generated from field: connectrpc.app.v1.SortBy sort_by = 1;
   */
  sortBy: SortBy;
};

/**
 * Describes the message connectrpc.app.v1.ListHorsesRequest.
 * Use `create(ListHorsesRequestSchema)` to create a new message.
 */
export const ListHorsesRequestSchema: GenMessage<ListHorsesRequest> = /*@__PURE__*/
  messageDesc(file_app_horse_service, 0);

/**
 * @generated from message connectrpc.app.v1.ListHorsesResponse
 */
export type ListHorsesResponse = Message<"connectrpc.app.v1.ListHorsesResponse"> & {
  /**
   * @generated from field: repeated connectrpc.app.v1.HorseDto horses = 1;
   */
  horses: HorseDto[];
};

/**
 * Describes the message connectrpc.app.v1.ListHorsesResponse.
 * Use `create(ListHorsesResponseSchema)` to create a new message.
 */
export const ListHorsesResponseSchema: GenMessage<ListHorsesResponse> = /*@__PURE__*/
  messageDesc(file_app_horse_service, 1);

/**
 * @generated from message connectrpc.app.v1.GetHorseRequest
 */
export type GetHorseRequest = Message<"connectrpc.app.v1.GetHorseRequest"> & {
  /**
   * @generated from field: int64 horse_id = 1;
   */
  horseId: bigint;
};

/**
 * Describes the message connectrpc.app.v1.GetHorseRequest.
 * Use `create(GetHorseRequestSchema)` to create a new message.
 */
export const GetHorseRequestSchema: GenMessage<GetHorseRequest> = /*@__PURE__*/
  messageDesc(file_app_horse_service, 2);

/**
 * @generated from message connectrpc.app.v1.GetHorseResponse
 */
export type GetHorseResponse = Message<"connectrpc.app.v1.GetHorseResponse"> & {
  /**
   * @generated from field: connectrpc.app.v1.HorseDto horse = 1;
   */
  horse?: HorseDto;
};

/**
 * Describes the message connectrpc.app.v1.GetHorseResponse.
 * Use `create(GetHorseResponseSchema)` to create a new message.
 */
export const GetHorseResponseSchema: GenMessage<GetHorseResponse> = /*@__PURE__*/
  messageDesc(file_app_horse_service, 3);

/**
 * @generated from message connectrpc.app.v1.CreateHorseFromMasterHorseRequest
 */
export type CreateHorseFromMasterHorseRequest = Message<"connectrpc.app.v1.CreateHorseFromMasterHorseRequest"> & {
  /**
   * @generated from field: string stable_uuid = 1;
   */
  stableUuid: string;

  /**
   * @generated from field: bool in_stable = 2;
   */
  inStable: boolean;

  /**
   * @generated from field: string master_horse_id = 3;
   */
  masterHorseId: string;

  /**
   * @generated from field: optional string profile_pic_path = 4;
   */
  profilePicPath?: string;
};

/**
 * Describes the message connectrpc.app.v1.CreateHorseFromMasterHorseRequest.
 * Use `create(CreateHorseFromMasterHorseRequestSchema)` to create a new message.
 */
export const CreateHorseFromMasterHorseRequestSchema: GenMessage<CreateHorseFromMasterHorseRequest> = /*@__PURE__*/
  messageDesc(file_app_horse_service, 4);

/**
 * @generated from message connectrpc.app.v1.CreateHorseFromMasterHorseResponse
 */
export type CreateHorseFromMasterHorseResponse = Message<"connectrpc.app.v1.CreateHorseFromMasterHorseResponse"> & {
  /**
   * @generated from field: connectrpc.app.v1.HorseDto horse = 1;
   */
  horse?: HorseDto;
};

/**
 * Describes the message connectrpc.app.v1.CreateHorseFromMasterHorseResponse.
 * Use `create(CreateHorseFromMasterHorseResponseSchema)` to create a new message.
 */
export const CreateHorseFromMasterHorseResponseSchema: GenMessage<CreateHorseFromMasterHorseResponse> = /*@__PURE__*/
  messageDesc(file_app_horse_service, 5);

/**
 * @generated from message connectrpc.app.v1.CreateHorseRequest
 */
export type CreateHorseRequest = Message<"connectrpc.app.v1.CreateHorseRequest"> & {
  /**
   * @generated from field: string stable_uuid = 1;
   */
  stableUuid: string;

  /**
   * @generated from field: bool in_stable = 2;
   */
  inStable: boolean;

  /**
   * @generated from field: string name = 3;
   */
  name: string;

  /**
   * @generated from field: string gender = 4;
   */
  gender: string;

  /**
   * @generated from field: int32 birth_year = 5;
   */
  birthYear: number;

  /**
   * @generated from field: optional string profile_pic_path = 6;
   */
  profilePicPath?: string;
};

/**
 * Describes the message connectrpc.app.v1.CreateHorseRequest.
 * Use `create(CreateHorseRequestSchema)` to create a new message.
 */
export const CreateHorseRequestSchema: GenMessage<CreateHorseRequest> = /*@__PURE__*/
  messageDesc(file_app_horse_service, 6);

/**
 * @generated from message connectrpc.app.v1.CreateHorseResponse
 */
export type CreateHorseResponse = Message<"connectrpc.app.v1.CreateHorseResponse"> & {
  /**
   * @generated from field: connectrpc.app.v1.HorseDto horse = 1;
   */
  horse?: HorseDto;
};

/**
 * Describes the message connectrpc.app.v1.CreateHorseResponse.
 * Use `create(CreateHorseResponseSchema)` to create a new message.
 */
export const CreateHorseResponseSchema: GenMessage<CreateHorseResponse> = /*@__PURE__*/
  messageDesc(file_app_horse_service, 7);

/**
 * @generated from message connectrpc.app.v1.PatchHorseRequest
 */
export type PatchHorseRequest = Message<"connectrpc.app.v1.PatchHorseRequest"> & {
  /**
   * @generated from field: int64 horse_id = 1;
   */
  horseId: bigint;

  /**
   * @generated from field: bool in_stable = 2;
   */
  inStable: boolean;

  /**
   * @generated from field: optional string profile_pic_path = 3;
   */
  profilePicPath?: string;
};

/**
 * Describes the message connectrpc.app.v1.PatchHorseRequest.
 * Use `create(PatchHorseRequestSchema)` to create a new message.
 */
export const PatchHorseRequestSchema: GenMessage<PatchHorseRequest> = /*@__PURE__*/
  messageDesc(file_app_horse_service, 8);

/**
 * @generated from message connectrpc.app.v1.PatchHorseResponse
 */
export type PatchHorseResponse = Message<"connectrpc.app.v1.PatchHorseResponse"> & {
};

/**
 * Describes the message connectrpc.app.v1.PatchHorseResponse.
 * Use `create(PatchHorseResponseSchema)` to create a new message.
 */
export const PatchHorseResponseSchema: GenMessage<PatchHorseResponse> = /*@__PURE__*/
  messageDesc(file_app_horse_service, 9);

/**
 * @generated from message connectrpc.app.v1.PatchHorseManageStatusRequest
 */
export type PatchHorseManageStatusRequest = Message<"connectrpc.app.v1.PatchHorseManageStatusRequest"> & {
  /**
   * @generated from field: int64 horse_id = 1;
   */
  horseId: bigint;

  /**
   * @generated from field: connectrpc.app.v1.ManageStatus manage_status = 2;
   */
  manageStatus: ManageStatus;
};

/**
 * Describes the message connectrpc.app.v1.PatchHorseManageStatusRequest.
 * Use `create(PatchHorseManageStatusRequestSchema)` to create a new message.
 */
export const PatchHorseManageStatusRequestSchema: GenMessage<PatchHorseManageStatusRequest> = /*@__PURE__*/
  messageDesc(file_app_horse_service, 10);

/**
 * @generated from message connectrpc.app.v1.PatchHorseManageStatusResponse
 */
export type PatchHorseManageStatusResponse = Message<"connectrpc.app.v1.PatchHorseManageStatusResponse"> & {
};

/**
 * Describes the message connectrpc.app.v1.PatchHorseManageStatusResponse.
 * Use `create(PatchHorseManageStatusResponseSchema)` to create a new message.
 */
export const PatchHorseManageStatusResponseSchema: GenMessage<PatchHorseManageStatusResponse> = /*@__PURE__*/
  messageDesc(file_app_horse_service, 11);

/**
 * @generated from message connectrpc.app.v1.TransferStableRequest
 */
export type TransferStableRequest = Message<"connectrpc.app.v1.TransferStableRequest"> & {
  /**
   * @generated from field: int64 horse_id = 1;
   */
  horseId: bigint;

  /**
   * @generated from field: string stable_uuid = 2;
   */
  stableUuid: string;
};

/**
 * Describes the message connectrpc.app.v1.TransferStableRequest.
 * Use `create(TransferStableRequestSchema)` to create a new message.
 */
export const TransferStableRequestSchema: GenMessage<TransferStableRequest> = /*@__PURE__*/
  messageDesc(file_app_horse_service, 12);

/**
 * @generated from message connectrpc.app.v1.TransferStableResponse
 */
export type TransferStableResponse = Message<"connectrpc.app.v1.TransferStableResponse"> & {
};

/**
 * Describes the message connectrpc.app.v1.TransferStableResponse.
 * Use `create(TransferStableResponseSchema)` to create a new message.
 */
export const TransferStableResponseSchema: GenMessage<TransferStableResponse> = /*@__PURE__*/
  messageDesc(file_app_horse_service, 13);

/**
 * @generated from message connectrpc.app.v1.DeleteHorseRequest
 */
export type DeleteHorseRequest = Message<"connectrpc.app.v1.DeleteHorseRequest"> & {
  /**
   * @generated from field: int64 horse_id = 1;
   */
  horseId: bigint;
};

/**
 * Describes the message connectrpc.app.v1.DeleteHorseRequest.
 * Use `create(DeleteHorseRequestSchema)` to create a new message.
 */
export const DeleteHorseRequestSchema: GenMessage<DeleteHorseRequest> = /*@__PURE__*/
  messageDesc(file_app_horse_service, 14);

/**
 * @generated from message connectrpc.app.v1.DeleteHorseResponse
 */
export type DeleteHorseResponse = Message<"connectrpc.app.v1.DeleteHorseResponse"> & {
};

/**
 * Describes the message connectrpc.app.v1.DeleteHorseResponse.
 * Use `create(DeleteHorseResponseSchema)` to create a new message.
 */
export const DeleteHorseResponseSchema: GenMessage<DeleteHorseResponse> = /*@__PURE__*/
  messageDesc(file_app_horse_service, 15);

/**
 * @generated from message connectrpc.app.v1.GetHorseTrainingScoresRequest
 */
export type GetHorseTrainingScoresRequest = Message<"connectrpc.app.v1.GetHorseTrainingScoresRequest"> & {
  /**
   * @generated from field: int32 horse_id = 1;
   */
  horseId: number;
};

/**
 * Describes the message connectrpc.app.v1.GetHorseTrainingScoresRequest.
 * Use `create(GetHorseTrainingScoresRequestSchema)` to create a new message.
 */
export const GetHorseTrainingScoresRequestSchema: GenMessage<GetHorseTrainingScoresRequest> = /*@__PURE__*/
  messageDesc(file_app_horse_service, 16);

/**
 * @generated from message connectrpc.app.v1.GetHorseTrainingScoresResponse
 */
export type GetHorseTrainingScoresResponse = Message<"connectrpc.app.v1.GetHorseTrainingScoresResponse"> & {
  /**
   * @generated from field: connectrpc.app.v1.PitchScore pitch_score = 1;
   */
  pitchScore: PitchScore;
};

/**
 * Describes the message connectrpc.app.v1.GetHorseTrainingScoresResponse.
 * Use `create(GetHorseTrainingScoresResponseSchema)` to create a new message.
 */
export const GetHorseTrainingScoresResponseSchema: GenMessage<GetHorseTrainingScoresResponse> = /*@__PURE__*/
  messageDesc(file_app_horse_service, 17);

/**
 * @generated from enum connectrpc.app.v1.SortBy
 */
export enum SortBy {
  /**
   * @generated from enum value: SORT_BY_UNSPECIFIED = 0;
   */
  UNSPECIFIED = 0,

  /**
   * @generated from enum value: SORT_BY_NAME = 1;
   */
  NAME = 1,

  /**
   * @generated from enum value: SORT_BY_TRAINING_DATE = 2;
   */
  TRAINING_DATE = 2,
}

/**
 * Describes the enum connectrpc.app.v1.SortBy.
 */
export const SortBySchema: GenEnum<SortBy> = /*@__PURE__*/
  enumDesc(file_app_horse_service, 0);

/**
 * @generated from enum connectrpc.app.v1.ManageStatus
 */
export enum ManageStatus {
  /**
   * @generated from enum value: MANAGE_STATUS_UNSPECIFIED = 0;
   */
  UNSPECIFIED = 0,

  /**
   * @generated from enum value: MANAGE_STATUS_MANAGED = 1;
   */
  MANAGED = 1,

  /**
   * @generated from enum value: MANAGE_STATUS_UNMANAGED = 2;
   */
  UNMANAGED = 2,
}

/**
 * Describes the enum connectrpc.app.v1.ManageStatus.
 */
export const ManageStatusSchema: GenEnum<ManageStatus> = /*@__PURE__*/
  enumDesc(file_app_horse_service, 1);

/**
 * @generated from enum connectrpc.app.v1.PitchScore
 */
export enum PitchScore {
  /**
   * @generated from enum value: PITCH_SCORE_UNSPECIFIED = 0;
   */
  UNSPECIFIED = 0,

  /**
   * @generated from enum value: PITCH_SCORE_PITCH_LOW = 1;
   */
  PITCH_LOW = 1,

  /**
   * @generated from enum value: PITCH_SCORE_PITCH_SLIGHTLY_LOW = 2;
   */
  PITCH_SLIGHTLY_LOW = 2,

  /**
   * @generated from enum value: PITCH_SCORE_PITCH_MEDIUM = 3;
   */
  PITCH_MEDIUM = 3,

  /**
   * @generated from enum value: PITCH_SCORE_PITCH_SLIGHTLY_HIGH = 4;
   */
  PITCH_SLIGHTLY_HIGH = 4,

  /**
   * @generated from enum value: PITCH_SCORE_PITCH_HIGH = 5;
   */
  PITCH_HIGH = 5,
}

/**
 * Describes the enum connectrpc.app.v1.PitchScore.
 */
export const PitchScoreSchema: GenEnum<PitchScore> = /*@__PURE__*/
  enumDesc(file_app_horse_service, 2);

/**
 * @generated from service connectrpc.app.v1.HorseService
 */
export const HorseService: GenService<{
  /**
   * @generated from rpc connectrpc.app.v1.HorseService.CreateHorseFromMasterHorse
   */
  createHorseFromMasterHorse: {
    methodKind: "unary";
    input: typeof CreateHorseFromMasterHorseRequestSchema;
    output: typeof CreateHorseFromMasterHorseResponseSchema;
  },
  /**
   * @generated from rpc connectrpc.app.v1.HorseService.CreateHorse
   */
  createHorse: {
    methodKind: "unary";
    input: typeof CreateHorseRequestSchema;
    output: typeof CreateHorseResponseSchema;
  },
  /**
   * @generated from rpc connectrpc.app.v1.HorseService.ListHorses
   */
  listHorses: {
    methodKind: "unary";
    input: typeof ListHorsesRequestSchema;
    output: typeof ListHorsesResponseSchema;
  },
  /**
   * @generated from rpc connectrpc.app.v1.HorseService.GetHorse
   */
  getHorse: {
    methodKind: "unary";
    input: typeof GetHorseRequestSchema;
    output: typeof GetHorseResponseSchema;
  },
  /**
   * @generated from rpc connectrpc.app.v1.HorseService.PatchHorseManageStatus
   */
  patchHorseManageStatus: {
    methodKind: "unary";
    input: typeof PatchHorseManageStatusRequestSchema;
    output: typeof PatchHorseManageStatusResponseSchema;
  },
  /**
   * @generated from rpc connectrpc.app.v1.HorseService.PatchHorse
   */
  patchHorse: {
    methodKind: "unary";
    input: typeof PatchHorseRequestSchema;
    output: typeof PatchHorseResponseSchema;
  },
  /**
   * @generated from rpc connectrpc.app.v1.HorseService.TransferStable
   */
  transferStable: {
    methodKind: "unary";
    input: typeof TransferStableRequestSchema;
    output: typeof TransferStableResponseSchema;
  },
  /**
   * @generated from rpc connectrpc.app.v1.HorseService.DeleteHorse
   */
  deleteHorse: {
    methodKind: "unary";
    input: typeof DeleteHorseRequestSchema;
    output: typeof DeleteHorseResponseSchema;
  },
  /**
   * @generated from rpc connectrpc.app.v1.HorseService.GetHorseTrainingScores
   */
  getHorseTrainingScores: {
    methodKind: "unary";
    input: typeof GetHorseTrainingScoresRequestSchema;
    output: typeof GetHorseTrainingScoresResponseSchema;
  },
}> = /*@__PURE__*/
  serviceDesc(file_app_horse_service, 0);

