// @generated by protoc-gen-es v2.2.3 with parameter "target=ts"
// @generated from file app/models/training_indicator_label_dto.proto (package connectrpc.app.v1, syntax proto3)
/* eslint-disable */

import type { GenFile, GenMessage } from "@bufbuild/protobuf/codegenv1";
import { fileDesc, messageDesc } from "@bufbuild/protobuf/codegenv1";
import type { Message } from "@bufbuild/protobuf";

/**
 * Describes the file app/models/training_indicator_label_dto.proto.
 */
export const file_app_models_training_indicator_label_dto: GenFile = /*@__PURE__*/
  fileDesc("Ci1hcHAvbW9kZWxzL3RyYWluaW5nX2luZGljYXRvcl9sYWJlbF9kdG8ucHJvdG8SEWNvbm5lY3RycGMuYXBwLnYxIm8KGVRyYWluaW5nSW5kaWNhdG9yTGFiZWxEdG8SIwobdHJhaW5pbmdfaW5kaWNhdG9yX2xhYmVsX2lkGAEgASgFEg0KBWxhYmVsGAIgASgJEgwKBHRpbWUYAyABKAUSEAoIZGlzdGFuY2UYBCABKAViBnByb3RvMw");

/**
 * @generated from message connectrpc.app.v1.TrainingIndicatorLabelDto
 */
export type TrainingIndicatorLabelDto = Message<"connectrpc.app.v1.TrainingIndicatorLabelDto"> & {
  /**
   * @generated from field: int32 training_indicator_label_id = 1;
   */
  trainingIndicatorLabelId: number;

  /**
   * @generated from field: string label = 2;
   */
  label: string;

  /**
   * @generated from field: int32 time = 3;
   */
  time: number;

  /**
   * @generated from field: int32 distance = 4;
   */
  distance: number;
};

/**
 * Describes the message connectrpc.app.v1.TrainingIndicatorLabelDto.
 * Use `create(TrainingIndicatorLabelDtoSchema)` to create a new message.
 */
export const TrainingIndicatorLabelDtoSchema: GenMessage<TrainingIndicatorLabelDto> = /*@__PURE__*/
  messageDesc(file_app_models_training_indicator_label_dto, 0);

