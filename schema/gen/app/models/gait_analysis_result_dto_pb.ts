// @generated by protoc-gen-es v2.2.3 with parameter "target=ts"
// @generated from file app/models/gait_analysis_result_dto.proto (package connectrpc.app.v1, syntax proto3)
/* eslint-disable */

import type { GenFile, GenMessage } from "@bufbuild/protobuf/codegenv1";
import { fileDesc, messageDesc } from "@bufbuild/protobuf/codegenv1";
import type { LocationDto } from "./location_dto_pb";
import { file_app_models_location_dto } from "./location_dto_pb";
import type { Message } from "@bufbuild/protobuf";

/**
 * Describes the file app/models/gait_analysis_result_dto.proto.
 */
export const file_app_models_gait_analysis_result_dto: GenFile = /*@__PURE__*/
  fileDesc("CilhcHAvbW9kZWxzL2dhaXRfYW5hbHlzaXNfcmVzdWx0X2R0by5wcm90bxIRY29ubmVjdHJwYy5hcHAudjEi2gkKFUdhaXRBbmFseXNpc1Jlc3VsdER0bxIhChlnYWl0X2FuYWx5c2lzX3Jlc3VsdF91dWlkGAEgASgJEhMKC3RyYWluaW5nX2lkGAIgASgFEhAKCHN0YXJ0X2F0GAMgASgJEg4KBmVuZF9hdBgEIAEoCRIMCgRnYWl0GAUgASgJEh4KEWltcGFjdF9sZWZ0X2Zyb250GAYgASgCSACIAQESHwoSaW1wYWN0X3JpZ2h0X2Zyb250GAcgASgCSAGIAQESHQoQaW1wYWN0X2xlZnRfYmFjaxgIIAEoAkgCiAEBEh4KEWltcGFjdF9yaWdodF9iYWNrGAkgASgCSAOIAQESKAobc3dpbmdfdGltZV9yYXRpb19sZWZ0X2Zyb250GAogASgCSASIAQESKQocc3dpbmdfdGltZV9yYXRpb19yaWdodF9mcm9udBgLIAEoAkgFiAEBEicKGnN3aW5nX3RpbWVfcmF0aW9fbGVmdF9iYWNrGAwgASgCSAaIAQESKAobc3dpbmdfdGltZV9yYXRpb19yaWdodF9iYWNrGA0gASgCSAeIAQESJQoYZm9vdF9vbl9hbmdsZV9sZWZ0X2Zyb250GA4gASgCSAiIAQESJgoZZm9vdF9vbl9hbmdsZV9yaWdodF9mcm9udBgPIAEoAkgJiAEBEiQKF2Zvb3Rfb25fYW5nbGVfbGVmdF9iYWNrGBAgASgCSAqIAQESJQoYZm9vdF9vbl9hbmdsZV9yaWdodF9iYWNrGBEgASgCSAuIAQESJgoZZm9vdF9vZmZfYW5nbGVfbGVmdF9mcm9udBgSIAEoAkgMiAEBEicKGmZvb3Rfb2ZmX2FuZ2xlX3JpZ2h0X2Zyb250GBMgASgCSA2IAQESJQoYZm9vdF9vZmZfYW5nbGVfbGVmdF9iYWNrGBQgASgCSA6IAQESJgoZZm9vdF9vZmZfYW5nbGVfcmlnaHRfYmFjaxgVIAEoAkgPiAEBEjEKCWxvY2F0aW9ucxgWIAMoCzIeLmNvbm5lY3RycGMuYXBwLnYxLkxvY2F0aW9uRHRvQhQKEl9pbXBhY3RfbGVmdF9mcm9udEIVChNfaW1wYWN0X3JpZ2h0X2Zyb250QhMKEV9pbXBhY3RfbGVmdF9iYWNrQhQKEl9pbXBhY3RfcmlnaHRfYmFja0IeChxfc3dpbmdfdGltZV9yYXRpb19sZWZ0X2Zyb250Qh8KHV9zd2luZ190aW1lX3JhdGlvX3JpZ2h0X2Zyb250Qh0KG19zd2luZ190aW1lX3JhdGlvX2xlZnRfYmFja0IeChxfc3dpbmdfdGltZV9yYXRpb19yaWdodF9iYWNrQhsKGV9mb290X29uX2FuZ2xlX2xlZnRfZnJvbnRCHAoaX2Zvb3Rfb25fYW5nbGVfcmlnaHRfZnJvbnRCGgoYX2Zvb3Rfb25fYW5nbGVfbGVmdF9iYWNrQhsKGV9mb290X29uX2FuZ2xlX3JpZ2h0X2JhY2tCHAoaX2Zvb3Rfb2ZmX2FuZ2xlX2xlZnRfZnJvbnRCHQobX2Zvb3Rfb2ZmX2FuZ2xlX3JpZ2h0X2Zyb250QhsKGV9mb290X29mZl9hbmdsZV9sZWZ0X2JhY2tCHAoaX2Zvb3Rfb2ZmX2FuZ2xlX3JpZ2h0X2JhY2tiBnByb3RvMw", [file_app_models_location_dto]);

/**
 * @generated from message connectrpc.app.v1.GaitAnalysisResultDto
 */
export type GaitAnalysisResultDto = Message<"connectrpc.app.v1.GaitAnalysisResultDto"> & {
  /**
   * @generated from field: string gait_analysis_result_uuid = 1;
   */
  gaitAnalysisResultUuid: string;

  /**
   * @generated from field: int32 training_id = 2;
   */
  trainingId: number;

  /**
   * @generated from field: string start_at = 3;
   */
  startAt: string;

  /**
   * @generated from field: string end_at = 4;
   */
  endAt: string;

  /**
   * @generated from field: string gait = 5;
   */
  gait: string;

  /**
   * @generated from field: optional float impact_left_front = 6;
   */
  impactLeftFront?: number;

  /**
   * @generated from field: optional float impact_right_front = 7;
   */
  impactRightFront?: number;

  /**
   * @generated from field: optional float impact_left_back = 8;
   */
  impactLeftBack?: number;

  /**
   * @generated from field: optional float impact_right_back = 9;
   */
  impactRightBack?: number;

  /**
   * @generated from field: optional float swing_time_ratio_left_front = 10;
   */
  swingTimeRatioLeftFront?: number;

  /**
   * @generated from field: optional float swing_time_ratio_right_front = 11;
   */
  swingTimeRatioRightFront?: number;

  /**
   * @generated from field: optional float swing_time_ratio_left_back = 12;
   */
  swingTimeRatioLeftBack?: number;

  /**
   * @generated from field: optional float swing_time_ratio_right_back = 13;
   */
  swingTimeRatioRightBack?: number;

  /**
   * @generated from field: optional float foot_on_angle_left_front = 14;
   */
  footOnAngleLeftFront?: number;

  /**
   * @generated from field: optional float foot_on_angle_right_front = 15;
   */
  footOnAngleRightFront?: number;

  /**
   * @generated from field: optional float foot_on_angle_left_back = 16;
   */
  footOnAngleLeftBack?: number;

  /**
   * @generated from field: optional float foot_on_angle_right_back = 17;
   */
  footOnAngleRightBack?: number;

  /**
   * @generated from field: optional float foot_off_angle_left_front = 18;
   */
  footOffAngleLeftFront?: number;

  /**
   * @generated from field: optional float foot_off_angle_right_front = 19;
   */
  footOffAngleRightFront?: number;

  /**
   * @generated from field: optional float foot_off_angle_left_back = 20;
   */
  footOffAngleLeftBack?: number;

  /**
   * @generated from field: optional float foot_off_angle_right_back = 21;
   */
  footOffAngleRightBack?: number;

  /**
   * @generated from field: repeated connectrpc.app.v1.LocationDto locations = 22;
   */
  locations: LocationDto[];
};

/**
 * Describes the message connectrpc.app.v1.GaitAnalysisResultDto.
 * Use `create(GaitAnalysisResultDtoSchema)` to create a new message.
 */
export const GaitAnalysisResultDtoSchema: GenMessage<GaitAnalysisResultDto> = /*@__PURE__*/
  messageDesc(file_app_models_gait_analysis_result_dto, 0);

