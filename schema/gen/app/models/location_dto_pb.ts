// @generated by protoc-gen-es v2.2.3 with parameter "target=ts"
// @generated from file app/models/location_dto.proto (package connectrpc.app.v1, syntax proto3)
/* eslint-disable */

import type { GenFile, GenMessage } from "@bufbuild/protobuf/codegenv1";
import { fileDesc, messageDesc } from "@bufbuild/protobuf/codegenv1";
import type { Message } from "@bufbuild/protobuf";

/**
 * Describes the file app/models/location_dto.proto.
 */
export const file_app_models_location_dto: GenFile = /*@__PURE__*/
  fileDesc("Ch1hcHAvbW9kZWxzL2xvY2F0aW9uX2R0by5wcm90bxIRY29ubmVjdHJwYy5hcHAudjEiMgoLTG9jYXRpb25EdG8SEAoIbGF0aXR1ZGUYASABKAISEQoJbG9uZ2l0dWRlGAIgASgCYgZwcm90bzM");

/**
 * @generated from message connectrpc.app.v1.LocationDto
 */
export type LocationDto = Message<"connectrpc.app.v1.LocationDto"> & {
  /**
   * @generated from field: float latitude = 1;
   */
  latitude: number;

  /**
   * @generated from field: float longitude = 2;
   */
  longitude: number;
};

/**
 * Describes the message connectrpc.app.v1.LocationDto.
 * Use `create(LocationDtoSchema)` to create a new message.
 */
export const LocationDtoSchema: GenMessage<LocationDto> = /*@__PURE__*/
  messageDesc(file_app_models_location_dto, 0);

