// @generated by protoc-gen-es v2.2.3 with parameter "target=ts"
// @generated from file app/models/training_indicator_dto.proto (package connectrpc.app.v1, syntax proto3)
/* eslint-disable */

import type { GenFile, GenMessage } from "@bufbuild/protobuf/codegenv1";
import { fileDesc, messageDesc } from "@bufbuild/protobuf/codegenv1";
import type { Message } from "@bufbuild/protobuf";

/**
 * Describes the file app/models/training_indicator_dto.proto.
 */
export const file_app_models_training_indicator_dto: GenFile = /*@__PURE__*/
  fileDesc("CidhcHAvbW9kZWxzL3RyYWluaW5nX2luZGljYXRvcl9kdG8ucHJvdG8SEWNvbm5lY3RycGMuYXBwLnYxIu0FChRUcmFpbmluZ0luZGljYXRvckR0bxIdChV0cmFpbmluZ19pbmRpY2F0b3JfaWQYASABKAUSGgoNZmFjaWxpdHlfbmFtZRgCIAEoCUgAiAEBEhsKDm1heF9oZWFydF9yYXRlGAMgASgFSAGIAQESEwoGdGhyMTAwGAQgASgFSAKIAQESEQoEdjIwMBgFIAEoAUgDiAEBEiIKFW9uZV9taW51dGVfaGVhcnRfcmF0ZRgGIAEoBUgEiAEBEjEKJHRoaXJ0eV9zZWNvbmRzX2FmdGVyX2dvYWxfaGVhcnRfcmF0ZRgHIAEoBUgFiAEBEi0KIG9uZV9taW51dGVfYWZ0ZXJfZ29hbF9oZWFydF9yYXRlGAggASgFSAaIAQESLgohdHdvX21pbnV0ZXNfYWZ0ZXJfZ29hbF9oZWFydF9yYXRlGAkgASgFSAeIAQESMgoldHdvX21pbnV0ZXNfYWZ0ZXJfZ29hbF9taW5faGVhcnRfcmF0ZRgKIAEoBUgIiAEBEikKHHRocmVlX21pbnV0ZXNfbWluX2hlYXJ0X3JhdGUYCyABKAVICYgBARIbCg5oZWFydF9yYXRlX2dhcBgMIAEoBUgKiAEBQhAKDl9mYWNpbGl0eV9uYW1lQhEKD19tYXhfaGVhcnRfcmF0ZUIJCgdfdGhyMTAwQgcKBV92MjAwQhgKFl9vbmVfbWludXRlX2hlYXJ0X3JhdGVCJwolX3RoaXJ0eV9zZWNvbmRzX2FmdGVyX2dvYWxfaGVhcnRfcmF0ZUIjCiFfb25lX21pbnV0ZV9hZnRlcl9nb2FsX2hlYXJ0X3JhdGVCJAoiX3R3b19taW51dGVzX2FmdGVyX2dvYWxfaGVhcnRfcmF0ZUIoCiZfdHdvX21pbnV0ZXNfYWZ0ZXJfZ29hbF9taW5faGVhcnRfcmF0ZUIfCh1fdGhyZWVfbWludXRlc19taW5faGVhcnRfcmF0ZUIRCg9faGVhcnRfcmF0ZV9nYXBiBnByb3RvMw");

/**
 * @generated from message connectrpc.app.v1.TrainingIndicatorDto
 */
export type TrainingIndicatorDto = Message<"connectrpc.app.v1.TrainingIndicatorDto"> & {
  /**
   * @generated from field: int32 training_indicator_id = 1;
   */
  trainingIndicatorId: number;

  /**
   * @generated from field: optional string facility_name = 2;
   */
  facilityName?: string;

  /**
   * @generated from field: optional int32 max_heart_rate = 3;
   */
  maxHeartRate?: number;

  /**
   * @generated from field: optional int32 thr100 = 4;
   */
  thr100?: number;

  /**
   * @generated from field: optional double v200 = 5;
   */
  v200?: number;

  /**
   * @generated from field: optional int32 one_minute_heart_rate = 6;
   */
  oneMinuteHeartRate?: number;

  /**
   * @generated from field: optional int32 thirty_seconds_after_goal_heart_rate = 7;
   */
  thirtySecondsAfterGoalHeartRate?: number;

  /**
   * @generated from field: optional int32 one_minute_after_goal_heart_rate = 8;
   */
  oneMinuteAfterGoalHeartRate?: number;

  /**
   * @generated from field: optional int32 two_minutes_after_goal_heart_rate = 9;
   */
  twoMinutesAfterGoalHeartRate?: number;

  /**
   * @generated from field: optional int32 two_minutes_after_goal_min_heart_rate = 10;
   */
  twoMinutesAfterGoalMinHeartRate?: number;

  /**
   * @generated from field: optional int32 three_minutes_min_heart_rate = 11;
   */
  threeMinutesMinHeartRate?: number;

  /**
   * @generated from field: optional int32 heart_rate_gap = 12;
   */
  heartRateGap?: number;
};

/**
 * Describes the message connectrpc.app.v1.TrainingIndicatorDto.
 * Use `create(TrainingIndicatorDtoSchema)` to create a new message.
 */
export const TrainingIndicatorDtoSchema: GenMessage<TrainingIndicatorDto> = /*@__PURE__*/
  messageDesc(file_app_models_training_indicator_dto, 0);

