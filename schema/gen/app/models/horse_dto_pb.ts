// @generated by protoc-gen-es v2.2.3 with parameter "target=ts"
// @generated from file app/models/horse_dto.proto (package connectrpc.app.v1, syntax proto3)
/* eslint-disable */

import type { GenEnum, GenFile, GenMessage } from "@bufbuild/protobuf/codegenv1";
import { enumDesc, fileDesc, messageDesc } from "@bufbuild/protobuf/codegenv1";
import type { Message } from "@bufbuild/protobuf";

/**
 * Describes the file app/models/horse_dto.proto.
 */
export const file_app_models_horse_dto: GenFile = /*@__PURE__*/
  fileDesc("ChphcHAvbW9kZWxzL2hvcnNlX2R0by5wcm90bxIRY29ubmVjdHJwYy5hcHAudjEiiQMKCEhvcnNlRHRvEhAKCGhvcnNlX2lkGAEgASgFEhcKD21hc3Rlcl9ob3JzZV9pZBgCIAEoCRIZChFvcmdhbml6YXRpb25fdXVpZBgDIAEoCRITCgtzdGFibGVfdXVpZBgEIAEoCRIMCgRuYW1lGAUgASgJEg4KBmdlbmRlchgGIAEoCRILCgNhZ2UYByABKAUSEgoKYmlydGhfeWVhchgIIAEoBRIRCglpbl9zdGFibGUYCSABKAgSGAoQcHJvZmlsZV9waWNfcGF0aBgKIAEoCRIVCg1tYW5hZ2Vfc3RhdHVzGAsgASgJEhkKEWhhc190cmFpbmVkX3RvZGF5GAwgASgIElIKHHRvZGF5c19ob3JzZV90cmFpbmluZ19zdGF0dXMYDSABKA4yLC5jb25uZWN0cnBjLmFwcC52MS5Ub2RheXNIb3JzZVRyYWluaW5nU3RhdHVzEhwKD2xhc3RfdHJhaW5lZF9hdBgOIAEoBUgAiAEBQhIKEF9sYXN0X3RyYWluZWRfYXQqxgIKGVRvZGF5c0hvcnNlVHJhaW5pbmdTdGF0dXMSLAooVE9EQVlTX0hPUlNFX1RSQUlOSU5HX1NUQVRVU19VTlNQRUNJRklFRBAAEjEKLVRPREFZU19IT1JTRV9UUkFJTklOR19TVEFUVVNfQkVGT1JFX01FQVNVUklORxABEi0KKVRPREFZU19IT1JTRV9UUkFJTklOR19TVEFUVVNfSU5fTUVBU1VSSU5HEAISNAowVE9EQVlTX0hPUlNFX1RSQUlOSU5HX1NUQVRVU19NRUFTVVJJTkdfQ09NUExFVEVEEAMSLQopVE9EQVlTX0hPUlNFX1RSQUlOSU5HX1NUQVRVU19JTl9BTkFMWVpJTkcQBBI0CjBUT0RBWVNfSE9SU0VfVFJBSU5JTkdfU1RBVFVTX0FOQUxZWklOR19DT01QTEVURUQQBWIGcHJvdG8z");

/**
 * @generated from message connectrpc.app.v1.HorseDto
 */
export type HorseDto = Message<"connectrpc.app.v1.HorseDto"> & {
  /**
   * @generated from field: int32 horse_id = 1;
   */
  horseId: number;

  /**
   * @generated from field: string master_horse_id = 2;
   */
  masterHorseId: string;

  /**
   * @generated from field: string organization_uuid = 3;
   */
  organizationUuid: string;

  /**
   * @generated from field: string stable_uuid = 4;
   */
  stableUuid: string;

  /**
   * @generated from field: string name = 5;
   */
  name: string;

  /**
   * @generated from field: string gender = 6;
   */
  gender: string;

  /**
   * @generated from field: int32 age = 7;
   */
  age: number;

  /**
   * @generated from field: int32 birth_year = 8;
   */
  birthYear: number;

  /**
   * @generated from field: bool in_stable = 9;
   */
  inStable: boolean;

  /**
   * @generated from field: string profile_pic_path = 10;
   */
  profilePicPath: string;

  /**
   * @generated from field: string manage_status = 11;
   */
  manageStatus: string;

  /**
   * @generated from field: bool has_trained_today = 12;
   */
  hasTrainedToday: boolean;

  /**
   * @generated from field: connectrpc.app.v1.TodaysHorseTrainingStatus todays_horse_training_status = 13;
   */
  todaysHorseTrainingStatus: TodaysHorseTrainingStatus;

  /**
   * @generated from field: optional int32 last_trained_at = 14;
   */
  lastTrainedAt?: number;
};

/**
 * Describes the message connectrpc.app.v1.HorseDto.
 * Use `create(HorseDtoSchema)` to create a new message.
 */
export const HorseDtoSchema: GenMessage<HorseDto> = /*@__PURE__*/
  messageDesc(file_app_models_horse_dto, 0);

/**
 * @generated from enum connectrpc.app.v1.TodaysHorseTrainingStatus
 */
export enum TodaysHorseTrainingStatus {
  /**
   * @generated from enum value: TODAYS_HORSE_TRAINING_STATUS_UNSPECIFIED = 0;
   */
  UNSPECIFIED = 0,

  /**
   * @generated from enum value: TODAYS_HORSE_TRAINING_STATUS_BEFORE_MEASURING = 1;
   */
  BEFORE_MEASURING = 1,

  /**
   * @generated from enum value: TODAYS_HORSE_TRAINING_STATUS_IN_MEASURING = 2;
   */
  IN_MEASURING = 2,

  /**
   * @generated from enum value: TODAYS_HORSE_TRAINING_STATUS_MEASURING_COMPLETED = 3;
   */
  MEASURING_COMPLETED = 3,

  /**
   * @generated from enum value: TODAYS_HORSE_TRAINING_STATUS_IN_ANALYZING = 4;
   */
  IN_ANALYZING = 4,

  /**
   * @generated from enum value: TODAYS_HORSE_TRAINING_STATUS_ANALYZING_COMPLETED = 5;
   */
  ANALYZING_COMPLETED = 5,
}

/**
 * Describes the enum connectrpc.app.v1.TodaysHorseTrainingStatus.
 */
export const TodaysHorseTrainingStatusSchema: GenEnum<TodaysHorseTrainingStatus> = /*@__PURE__*/
  enumDesc(file_app_models_horse_dto, 0);

