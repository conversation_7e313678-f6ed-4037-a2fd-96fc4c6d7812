// @generated by protoc-gen-es v2.2.3 with parameter "target=ts"
// @generated from file app/models/time_series_heart_beat_result_dto.proto (package connectrpc.app.v1, syntax proto3)
/* eslint-disable */

import type { GenFile, GenMessage } from "@bufbuild/protobuf/codegenv1";
import { fileDesc, messageDesc } from "@bufbuild/protobuf/codegenv1";
import type { Message } from "@bufbuild/protobuf";

/**
 * Describes the file app/models/time_series_heart_beat_result_dto.proto.
 */
export const file_app_models_time_series_heart_beat_result_dto: GenFile = /*@__PURE__*/
  fileDesc("CjJhcHAvbW9kZWxzL3RpbWVfc2VyaWVzX2hlYXJ0X2JlYXRfcmVzdWx0X2R0by5wcm90bxIRY29ubmVjdHJwYy5hcHAudjEiyAEKHFRpbWVTZXJpZXNIZWFydEJlYXRSZXN1bHREdG8SDAoEdGltZRgBIAEoBRIXCgpoZWFydF9yYXRlGAIgASgFSACIAQESHgoRc3ltcGF0aGV0aWNfbmVydmUYAyABKAFIAYgBARIiChVwYXJhc3ltcGF0aGV0aWNfbmVydmUYBCABKAFIAogBAUINCgtfaGVhcnRfcmF0ZUIUChJfc3ltcGF0aGV0aWNfbmVydmVCGAoWX3BhcmFzeW1wYXRoZXRpY19uZXJ2ZWIGcHJvdG8z");

/**
 * @generated from message connectrpc.app.v1.TimeSeriesHeartBeatResultDto
 */
export type TimeSeriesHeartBeatResultDto = Message<"connectrpc.app.v1.TimeSeriesHeartBeatResultDto"> & {
  /**
   * @generated from field: int32 time = 1;
   */
  time: number;

  /**
   * @generated from field: optional int32 heart_rate = 2;
   */
  heartRate?: number;

  /**
   * @generated from field: optional double sympathetic_nerve = 3;
   */
  sympatheticNerve?: number;

  /**
   * @generated from field: optional double parasympathetic_nerve = 4;
   */
  parasympatheticNerve?: number;
};

/**
 * Describes the message connectrpc.app.v1.TimeSeriesHeartBeatResultDto.
 * Use `create(TimeSeriesHeartBeatResultDtoSchema)` to create a new message.
 */
export const TimeSeriesHeartBeatResultDtoSchema: GenMessage<TimeSeriesHeartBeatResultDto> = /*@__PURE__*/
  messageDesc(file_app_models_time_series_heart_beat_result_dto, 0);

