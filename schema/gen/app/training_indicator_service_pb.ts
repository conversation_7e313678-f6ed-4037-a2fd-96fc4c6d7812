// @generated by protoc-gen-es v2.2.3 with parameter "target=ts"
// @generated from file app/training_indicator_service.proto (package connectrpc.app.v1, syntax proto3)
/* eslint-disable */

import type { GenFile, GenMessage, GenService } from "@bufbuild/protobuf/codegenv1";
import { fileDesc, messageDesc, serviceDesc } from "@bufbuild/protobuf/codegenv1";
import type { TrainingIndicatorDto } from "./models/training_indicator_dto_pb";
import { file_app_models_training_indicator_dto } from "./models/training_indicator_dto_pb";
import type { TrainingIndicatorLabelDto } from "./models/training_indicator_label_dto_pb";
import { file_app_models_training_indicator_label_dto } from "./models/training_indicator_label_dto_pb";
import type { Message } from "@bufbuild/protobuf";

/**
 * Describes the file app/training_indicator_service.proto.
 */
export const file_app_training_indicator_service: GenFile = /*@__PURE__*/
  fileDesc("CiRhcHAvdHJhaW5pbmdfaW5kaWNhdG9yX3NlcnZpY2UucHJvdG8SEWNvbm5lY3RycGMuYXBwLnYxIjIKG0dldFRyYWluaW5nSW5kaWNhdG9yUmVxdWVzdBITCgt0cmFpbmluZ19pZBgBIAEoBSI5CiJMaXN0VHJhaW5pbmdJbmRpY2F0b3JMYWJlbHNSZXF1ZXN0EhMKC3RyYWluaW5nX2lkGAEgASgFIn8KHEdldFRyYWluaW5nSW5kaWNhdG9yUmVzcG9uc2USSAoSdHJhaW5pbmdfaW5kaWNhdG9yGAEgASgLMicuY29ubmVjdHJwYy5hcHAudjEuVHJhaW5pbmdJbmRpY2F0b3JEdG9IAIgBAUIVChNfdHJhaW5pbmdfaW5kaWNhdG9yInYKI0xpc3RUcmFpbmluZ0luZGljYXRvckxhYmVsc1Jlc3BvbnNlEk8KGXRyYWluaW5nX2luZGljYXRvcl9sYWJlbHMYASADKAsyLC5jb25uZWN0cnBjLmFwcC52MS5UcmFpbmluZ0luZGljYXRvckxhYmVsRHRvMqICChhUcmFpbmluZ0luZGljYXRvclNlcnZpY2USdwoUR2V0VHJhaW5pbmdJbmRpY2F0b3ISLi5jb25uZWN0cnBjLmFwcC52MS5HZXRUcmFpbmluZ0luZGljYXRvclJlcXVlc3QaLy5jb25uZWN0cnBjLmFwcC52MS5HZXRUcmFpbmluZ0luZGljYXRvclJlc3BvbnNlEowBChtMaXN0VHJhaW5pbmdJbmRpY2F0b3JMYWJlbHMSNS5jb25uZWN0cnBjLmFwcC52MS5MaXN0VHJhaW5pbmdJbmRpY2F0b3JMYWJlbHNSZXF1ZXN0GjYuY29ubmVjdHJwYy5hcHAudjEuTGlzdFRyYWluaW5nSW5kaWNhdG9yTGFiZWxzUmVzcG9uc2ViBnByb3RvMw", [file_app_models_training_indicator_dto, file_app_models_training_indicator_label_dto]);

/**
 * @generated from message connectrpc.app.v1.GetTrainingIndicatorRequest
 */
export type GetTrainingIndicatorRequest = Message<"connectrpc.app.v1.GetTrainingIndicatorRequest"> & {
  /**
   * @generated from field: int32 training_id = 1;
   */
  trainingId: number;
};

/**
 * Describes the message connectrpc.app.v1.GetTrainingIndicatorRequest.
 * Use `create(GetTrainingIndicatorRequestSchema)` to create a new message.
 */
export const GetTrainingIndicatorRequestSchema: GenMessage<GetTrainingIndicatorRequest> = /*@__PURE__*/
  messageDesc(file_app_training_indicator_service, 0);

/**
 * @generated from message connectrpc.app.v1.ListTrainingIndicatorLabelsRequest
 */
export type ListTrainingIndicatorLabelsRequest = Message<"connectrpc.app.v1.ListTrainingIndicatorLabelsRequest"> & {
  /**
   * @generated from field: int32 training_id = 1;
   */
  trainingId: number;
};

/**
 * Describes the message connectrpc.app.v1.ListTrainingIndicatorLabelsRequest.
 * Use `create(ListTrainingIndicatorLabelsRequestSchema)` to create a new message.
 */
export const ListTrainingIndicatorLabelsRequestSchema: GenMessage<ListTrainingIndicatorLabelsRequest> = /*@__PURE__*/
  messageDesc(file_app_training_indicator_service, 1);

/**
 * @generated from message connectrpc.app.v1.GetTrainingIndicatorResponse
 */
export type GetTrainingIndicatorResponse = Message<"connectrpc.app.v1.GetTrainingIndicatorResponse"> & {
  /**
   * @generated from field: optional connectrpc.app.v1.TrainingIndicatorDto training_indicator = 1;
   */
  trainingIndicator?: TrainingIndicatorDto;
};

/**
 * Describes the message connectrpc.app.v1.GetTrainingIndicatorResponse.
 * Use `create(GetTrainingIndicatorResponseSchema)` to create a new message.
 */
export const GetTrainingIndicatorResponseSchema: GenMessage<GetTrainingIndicatorResponse> = /*@__PURE__*/
  messageDesc(file_app_training_indicator_service, 2);

/**
 * @generated from message connectrpc.app.v1.ListTrainingIndicatorLabelsResponse
 */
export type ListTrainingIndicatorLabelsResponse = Message<"connectrpc.app.v1.ListTrainingIndicatorLabelsResponse"> & {
  /**
   * @generated from field: repeated connectrpc.app.v1.TrainingIndicatorLabelDto training_indicator_labels = 1;
   */
  trainingIndicatorLabels: TrainingIndicatorLabelDto[];
};

/**
 * Describes the message connectrpc.app.v1.ListTrainingIndicatorLabelsResponse.
 * Use `create(ListTrainingIndicatorLabelsResponseSchema)` to create a new message.
 */
export const ListTrainingIndicatorLabelsResponseSchema: GenMessage<ListTrainingIndicatorLabelsResponse> = /*@__PURE__*/
  messageDesc(file_app_training_indicator_service, 3);

/**
 * @generated from service connectrpc.app.v1.TrainingIndicatorService
 */
export const TrainingIndicatorService: GenService<{
  /**
   * @generated from rpc connectrpc.app.v1.TrainingIndicatorService.GetTrainingIndicator
   */
  getTrainingIndicator: {
    methodKind: "unary";
    input: typeof GetTrainingIndicatorRequestSchema;
    output: typeof GetTrainingIndicatorResponseSchema;
  },
  /**
   * @generated from rpc connectrpc.app.v1.TrainingIndicatorService.ListTrainingIndicatorLabels
   */
  listTrainingIndicatorLabels: {
    methodKind: "unary";
    input: typeof ListTrainingIndicatorLabelsRequestSchema;
    output: typeof ListTrainingIndicatorLabelsResponseSchema;
  },
}> = /*@__PURE__*/
  serviceDesc(file_app_training_indicator_service, 0);

