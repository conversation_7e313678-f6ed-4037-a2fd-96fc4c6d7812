// @generated by protoc-gen-es v2.2.3 with parameter "target=ts"
// @generated from file horse_managements/user_service.proto (package connectrpc.management.horse_managements.v1, syntax proto3)
/* eslint-disable */

import type { GenFile, GenMessage, GenService } from "@bufbuild/protobuf/codegenv1";
import { fileDesc, messageDesc, serviceDesc } from "@bufbuild/protobuf/codegenv1";
import type { FeatureFlags } from "./models/feature_flags_pb";
import { file_horse_managements_models_feature_flags } from "./models/feature_flags_pb";
import type { User } from "./models/user_pb";
import { file_horse_managements_models_user } from "./models/user_pb";
import type { Message } from "@bufbuild/protobuf";

/**
 * Describes the file horse_managements/user_service.proto.
 */
export const file_horse_managements_user_service: GenFile = /*@__PURE__*/
  fileDesc("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", [file_horse_managements_models_feature_flags, file_horse_managements_models_user]);

/**
 * @generated from message connectrpc.management.horse_managements.v1.GetMeRequest
 */
export type GetMeRequest = Message<"connectrpc.management.horse_managements.v1.GetMeRequest"> & {
};

/**
 * Describes the message connectrpc.management.horse_managements.v1.GetMeRequest.
 * Use `create(GetMeRequestSchema)` to create a new message.
 */
export const GetMeRequestSchema: GenMessage<GetMeRequest> = /*@__PURE__*/
  messageDesc(file_horse_managements_user_service, 0);

/**
 * @generated from message connectrpc.management.horse_managements.v1.GetMeResponse
 */
export type GetMeResponse = Message<"connectrpc.management.horse_managements.v1.GetMeResponse"> & {
  /**
   * @generated from field: connectrpc.management.horse_managements.v1.User user = 1;
   */
  user?: User;

  /**
   * @generated from field: connectrpc.management.horse_managements.v1.FeatureFlags feature_flags = 2;
   */
  featureFlags?: FeatureFlags;

  /**
   * @generated from field: string language = 3;
   */
  language: string;
};

/**
 * Describes the message connectrpc.management.horse_managements.v1.GetMeResponse.
 * Use `create(GetMeResponseSchema)` to create a new message.
 */
export const GetMeResponseSchema: GenMessage<GetMeResponse> = /*@__PURE__*/
  messageDesc(file_horse_managements_user_service, 1);

/**
 * @generated from message connectrpc.management.horse_managements.v1.ResetPasswordRequest
 */
export type ResetPasswordRequest = Message<"connectrpc.management.horse_managements.v1.ResetPasswordRequest"> & {
  /**
   * @generated from field: string email = 1;
   */
  email: string;
};

/**
 * Describes the message connectrpc.management.horse_managements.v1.ResetPasswordRequest.
 * Use `create(ResetPasswordRequestSchema)` to create a new message.
 */
export const ResetPasswordRequestSchema: GenMessage<ResetPasswordRequest> = /*@__PURE__*/
  messageDesc(file_horse_managements_user_service, 2);

/**
 * @generated from message connectrpc.management.horse_managements.v1.ResetPasswordResponse
 */
export type ResetPasswordResponse = Message<"connectrpc.management.horse_managements.v1.ResetPasswordResponse"> & {
};

/**
 * Describes the message connectrpc.management.horse_managements.v1.ResetPasswordResponse.
 * Use `create(ResetPasswordResponseSchema)` to create a new message.
 */
export const ResetPasswordResponseSchema: GenMessage<ResetPasswordResponse> = /*@__PURE__*/
  messageDesc(file_horse_managements_user_service, 3);

/**
 * @generated from message connectrpc.management.horse_managements.v1.PostDeviceRequest
 */
export type PostDeviceRequest = Message<"connectrpc.management.horse_managements.v1.PostDeviceRequest"> & {
  /**
   * @generated from field: optional string os_name = 1;
   */
  osName?: string;

  /**
   * @generated from field: optional string os_version = 2;
   */
  osVersion?: string;

  /**
   * @generated from field: optional string model_name = 3;
   */
  modelName?: string;

  /**
   * @generated from field: optional string app_runtime_version = 4;
   */
  appRuntimeVersion?: string;

  /**
   * @generated from field: optional string app_update_created_at = 5;
   */
  appUpdateCreatedAt?: string;
};

/**
 * Describes the message connectrpc.management.horse_managements.v1.PostDeviceRequest.
 * Use `create(PostDeviceRequestSchema)` to create a new message.
 */
export const PostDeviceRequestSchema: GenMessage<PostDeviceRequest> = /*@__PURE__*/
  messageDesc(file_horse_managements_user_service, 4);

/**
 * @generated from message connectrpc.management.horse_managements.v1.PostDeviceResponse
 */
export type PostDeviceResponse = Message<"connectrpc.management.horse_managements.v1.PostDeviceResponse"> & {
  /**
   * @generated from field: string device_id = 1;
   */
  deviceId: string;
};

/**
 * Describes the message connectrpc.management.horse_managements.v1.PostDeviceResponse.
 * Use `create(PostDeviceResponseSchema)` to create a new message.
 */
export const PostDeviceResponseSchema: GenMessage<PostDeviceResponse> = /*@__PURE__*/
  messageDesc(file_horse_managements_user_service, 5);

/**
 * @generated from message connectrpc.management.horse_managements.v1.UpdateDeviceRequest
 */
export type UpdateDeviceRequest = Message<"connectrpc.management.horse_managements.v1.UpdateDeviceRequest"> & {
  /**
   * @generated from field: string device_id = 1;
   */
  deviceId: string;

  /**
   * @generated from field: optional string app_runtime_version = 2;
   */
  appRuntimeVersion?: string;

  /**
   * @generated from field: optional string app_update_created_at = 3;
   */
  appUpdateCreatedAt?: string;
};

/**
 * Describes the message connectrpc.management.horse_managements.v1.UpdateDeviceRequest.
 * Use `create(UpdateDeviceRequestSchema)` to create a new message.
 */
export const UpdateDeviceRequestSchema: GenMessage<UpdateDeviceRequest> = /*@__PURE__*/
  messageDesc(file_horse_managements_user_service, 6);

/**
 * @generated from message connectrpc.management.horse_managements.v1.UpdateDeviceResponse
 */
export type UpdateDeviceResponse = Message<"connectrpc.management.horse_managements.v1.UpdateDeviceResponse"> & {
};

/**
 * Describes the message connectrpc.management.horse_managements.v1.UpdateDeviceResponse.
 * Use `create(UpdateDeviceResponseSchema)` to create a new message.
 */
export const UpdateDeviceResponseSchema: GenMessage<UpdateDeviceResponse> = /*@__PURE__*/
  messageDesc(file_horse_managements_user_service, 7);

/**
 * @generated from message connectrpc.management.horse_managements.v1.GetUserRequest
 */
export type GetUserRequest = Message<"connectrpc.management.horse_managements.v1.GetUserRequest"> & {
  /**
   * @generated from field: string user_uuid = 1;
   */
  userUuid: string;
};

/**
 * Describes the message connectrpc.management.horse_managements.v1.GetUserRequest.
 * Use `create(GetUserRequestSchema)` to create a new message.
 */
export const GetUserRequestSchema: GenMessage<GetUserRequest> = /*@__PURE__*/
  messageDesc(file_horse_managements_user_service, 8);

/**
 * @generated from message connectrpc.management.horse_managements.v1.GetUserResponse
 */
export type GetUserResponse = Message<"connectrpc.management.horse_managements.v1.GetUserResponse"> & {
  /**
   * @generated from field: connectrpc.management.horse_managements.v1.User user = 1;
   */
  user?: User;
};

/**
 * Describes the message connectrpc.management.horse_managements.v1.GetUserResponse.
 * Use `create(GetUserResponseSchema)` to create a new message.
 */
export const GetUserResponseSchema: GenMessage<GetUserResponse> = /*@__PURE__*/
  messageDesc(file_horse_managements_user_service, 9);

/**
 * @generated from message connectrpc.management.horse_managements.v1.ListUsersRequest
 */
export type ListUsersRequest = Message<"connectrpc.management.horse_managements.v1.ListUsersRequest"> & {
  /**
   * @generated from field: string stable_uuid = 1;
   */
  stableUuid: string;
};

/**
 * Describes the message connectrpc.management.horse_managements.v1.ListUsersRequest.
 * Use `create(ListUsersRequestSchema)` to create a new message.
 */
export const ListUsersRequestSchema: GenMessage<ListUsersRequest> = /*@__PURE__*/
  messageDesc(file_horse_managements_user_service, 10);

/**
 * @generated from message connectrpc.management.horse_managements.v1.ListUsersResponse
 */
export type ListUsersResponse = Message<"connectrpc.management.horse_managements.v1.ListUsersResponse"> & {
  /**
   * @generated from field: repeated connectrpc.management.horse_managements.v1.User users = 1;
   */
  users: User[];
};

/**
 * Describes the message connectrpc.management.horse_managements.v1.ListUsersResponse.
 * Use `create(ListUsersResponseSchema)` to create a new message.
 */
export const ListUsersResponseSchema: GenMessage<ListUsersResponse> = /*@__PURE__*/
  messageDesc(file_horse_managements_user_service, 11);

/**
 * @generated from message connectrpc.management.horse_managements.v1.PostUserRequest
 */
export type PostUserRequest = Message<"connectrpc.management.horse_managements.v1.PostUserRequest"> & {
  /**
   * @generated from field: string first_name = 1;
   */
  firstName: string;

  /**
   * @generated from field: string last_name = 2;
   */
  lastName: string;

  /**
   * @generated from field: string email = 3;
   */
  email: string;

  /**
   * @generated from field: optional bool has_organization_permission = 4;
   */
  hasOrganizationPermission?: boolean;

  /**
   * @generated from field: repeated string stable_uuids = 5;
   */
  stableUuids: string[];
};

/**
 * Describes the message connectrpc.management.horse_managements.v1.PostUserRequest.
 * Use `create(PostUserRequestSchema)` to create a new message.
 */
export const PostUserRequestSchema: GenMessage<PostUserRequest> = /*@__PURE__*/
  messageDesc(file_horse_managements_user_service, 12);

/**
 * @generated from message connectrpc.management.horse_managements.v1.PostUserResponse
 */
export type PostUserResponse = Message<"connectrpc.management.horse_managements.v1.PostUserResponse"> & {
  /**
   * @generated from field: connectrpc.management.horse_managements.v1.User user = 1;
   */
  user?: User;
};

/**
 * Describes the message connectrpc.management.horse_managements.v1.PostUserResponse.
 * Use `create(PostUserResponseSchema)` to create a new message.
 */
export const PostUserResponseSchema: GenMessage<PostUserResponse> = /*@__PURE__*/
  messageDesc(file_horse_managements_user_service, 13);

/**
 * @generated from message connectrpc.management.horse_managements.v1.RemoveStablePermissionRequest
 */
export type RemoveStablePermissionRequest = Message<"connectrpc.management.horse_managements.v1.RemoveStablePermissionRequest"> & {
  /**
   * @generated from field: string user_uuid = 1;
   */
  userUuid: string;

  /**
   * @generated from field: string stable_uuid = 2;
   */
  stableUuid: string;
};

/**
 * Describes the message connectrpc.management.horse_managements.v1.RemoveStablePermissionRequest.
 * Use `create(RemoveStablePermissionRequestSchema)` to create a new message.
 */
export const RemoveStablePermissionRequestSchema: GenMessage<RemoveStablePermissionRequest> = /*@__PURE__*/
  messageDesc(file_horse_managements_user_service, 14);

/**
 * @generated from message connectrpc.management.horse_managements.v1.RemoveStablePermissionResponse
 */
export type RemoveStablePermissionResponse = Message<"connectrpc.management.horse_managements.v1.RemoveStablePermissionResponse"> & {
};

/**
 * Describes the message connectrpc.management.horse_managements.v1.RemoveStablePermissionResponse.
 * Use `create(RemoveStablePermissionResponseSchema)` to create a new message.
 */
export const RemoveStablePermissionResponseSchema: GenMessage<RemoveStablePermissionResponse> = /*@__PURE__*/
  messageDesc(file_horse_managements_user_service, 15);

/**
 * @generated from message connectrpc.management.horse_managements.v1.AddStablePermissionRequest
 */
export type AddStablePermissionRequest = Message<"connectrpc.management.horse_managements.v1.AddStablePermissionRequest"> & {
  /**
   * @generated from field: string user_uuid = 1;
   */
  userUuid: string;

  /**
   * @generated from field: string stable_uuid = 2;
   */
  stableUuid: string;
};

/**
 * Describes the message connectrpc.management.horse_managements.v1.AddStablePermissionRequest.
 * Use `create(AddStablePermissionRequestSchema)` to create a new message.
 */
export const AddStablePermissionRequestSchema: GenMessage<AddStablePermissionRequest> = /*@__PURE__*/
  messageDesc(file_horse_managements_user_service, 16);

/**
 * @generated from message connectrpc.management.horse_managements.v1.AddStablePermissionResponse
 */
export type AddStablePermissionResponse = Message<"connectrpc.management.horse_managements.v1.AddStablePermissionResponse"> & {
};

/**
 * Describes the message connectrpc.management.horse_managements.v1.AddStablePermissionResponse.
 * Use `create(AddStablePermissionResponseSchema)` to create a new message.
 */
export const AddStablePermissionResponseSchema: GenMessage<AddStablePermissionResponse> = /*@__PURE__*/
  messageDesc(file_horse_managements_user_service, 17);

/**
 * @generated from message connectrpc.management.horse_managements.v1.AddOrganizationPermissionRequest
 */
export type AddOrganizationPermissionRequest = Message<"connectrpc.management.horse_managements.v1.AddOrganizationPermissionRequest"> & {
  /**
   * @generated from field: string user_uuid = 1;
   */
  userUuid: string;
};

/**
 * Describes the message connectrpc.management.horse_managements.v1.AddOrganizationPermissionRequest.
 * Use `create(AddOrganizationPermissionRequestSchema)` to create a new message.
 */
export const AddOrganizationPermissionRequestSchema: GenMessage<AddOrganizationPermissionRequest> = /*@__PURE__*/
  messageDesc(file_horse_managements_user_service, 18);

/**
 * @generated from message connectrpc.management.horse_managements.v1.AddOrganizationPermissionResponse
 */
export type AddOrganizationPermissionResponse = Message<"connectrpc.management.horse_managements.v1.AddOrganizationPermissionResponse"> & {
};

/**
 * Describes the message connectrpc.management.horse_managements.v1.AddOrganizationPermissionResponse.
 * Use `create(AddOrganizationPermissionResponseSchema)` to create a new message.
 */
export const AddOrganizationPermissionResponseSchema: GenMessage<AddOrganizationPermissionResponse> = /*@__PURE__*/
  messageDesc(file_horse_managements_user_service, 19);

/**
 * @generated from message connectrpc.management.horse_managements.v1.RemoveOrganizationPermissionRequest
 */
export type RemoveOrganizationPermissionRequest = Message<"connectrpc.management.horse_managements.v1.RemoveOrganizationPermissionRequest"> & {
  /**
   * @generated from field: string user_uuid = 1;
   */
  userUuid: string;

  /**
   * @generated from field: repeated string stable_uuids = 2;
   */
  stableUuids: string[];
};

/**
 * Describes the message connectrpc.management.horse_managements.v1.RemoveOrganizationPermissionRequest.
 * Use `create(RemoveOrganizationPermissionRequestSchema)` to create a new message.
 */
export const RemoveOrganizationPermissionRequestSchema: GenMessage<RemoveOrganizationPermissionRequest> = /*@__PURE__*/
  messageDesc(file_horse_managements_user_service, 20);

/**
 * @generated from message connectrpc.management.horse_managements.v1.RemoveOrganizationPermissionResponse
 */
export type RemoveOrganizationPermissionResponse = Message<"connectrpc.management.horse_managements.v1.RemoveOrganizationPermissionResponse"> & {
};

/**
 * Describes the message connectrpc.management.horse_managements.v1.RemoveOrganizationPermissionResponse.
 * Use `create(RemoveOrganizationPermissionResponseSchema)` to create a new message.
 */
export const RemoveOrganizationPermissionResponseSchema: GenMessage<RemoveOrganizationPermissionResponse> = /*@__PURE__*/
  messageDesc(file_horse_managements_user_service, 21);

/**
 * @generated from message connectrpc.management.horse_managements.v1.PatchUserRequest
 */
export type PatchUserRequest = Message<"connectrpc.management.horse_managements.v1.PatchUserRequest"> & {
  /**
   * @generated from field: string user_uuid = 1;
   */
  userUuid: string;

  /**
   * @generated from field: optional string first_name = 2;
   */
  firstName?: string;

  /**
   * @generated from field: optional string last_name = 3;
   */
  lastName?: string;
};

/**
 * Describes the message connectrpc.management.horse_managements.v1.PatchUserRequest.
 * Use `create(PatchUserRequestSchema)` to create a new message.
 */
export const PatchUserRequestSchema: GenMessage<PatchUserRequest> = /*@__PURE__*/
  messageDesc(file_horse_managements_user_service, 22);

/**
 * @generated from message connectrpc.management.horse_managements.v1.PatchUserResponse
 */
export type PatchUserResponse = Message<"connectrpc.management.horse_managements.v1.PatchUserResponse"> & {
  /**
   * @generated from field: connectrpc.management.horse_managements.v1.User user = 1;
   */
  user?: User;
};

/**
 * Describes the message connectrpc.management.horse_managements.v1.PatchUserResponse.
 * Use `create(PatchUserResponseSchema)` to create a new message.
 */
export const PatchUserResponseSchema: GenMessage<PatchUserResponse> = /*@__PURE__*/
  messageDesc(file_horse_managements_user_service, 23);

/**
 * @generated from message connectrpc.management.horse_managements.v1.DeleteUserRequest
 */
export type DeleteUserRequest = Message<"connectrpc.management.horse_managements.v1.DeleteUserRequest"> & {
  /**
   * @generated from field: string user_uuid = 1;
   */
  userUuid: string;
};

/**
 * Describes the message connectrpc.management.horse_managements.v1.DeleteUserRequest.
 * Use `create(DeleteUserRequestSchema)` to create a new message.
 */
export const DeleteUserRequestSchema: GenMessage<DeleteUserRequest> = /*@__PURE__*/
  messageDesc(file_horse_managements_user_service, 24);

/**
 * @generated from message connectrpc.management.horse_managements.v1.DeleteUserResponse
 */
export type DeleteUserResponse = Message<"connectrpc.management.horse_managements.v1.DeleteUserResponse"> & {
};

/**
 * Describes the message connectrpc.management.horse_managements.v1.DeleteUserResponse.
 * Use `create(DeleteUserResponseSchema)` to create a new message.
 */
export const DeleteUserResponseSchema: GenMessage<DeleteUserResponse> = /*@__PURE__*/
  messageDesc(file_horse_managements_user_service, 25);

/**
 * @generated from message connectrpc.management.horse_managements.v1.UpsertUserLangSettingsRequest
 */
export type UpsertUserLangSettingsRequest = Message<"connectrpc.management.horse_managements.v1.UpsertUserLangSettingsRequest"> & {
  /**
   * @generated from field: string language = 1;
   */
  language: string;
};

/**
 * Describes the message connectrpc.management.horse_managements.v1.UpsertUserLangSettingsRequest.
 * Use `create(UpsertUserLangSettingsRequestSchema)` to create a new message.
 */
export const UpsertUserLangSettingsRequestSchema: GenMessage<UpsertUserLangSettingsRequest> = /*@__PURE__*/
  messageDesc(file_horse_managements_user_service, 26);

/**
 * @generated from message connectrpc.management.horse_managements.v1.UpsertUserLangSettingsResponse
 */
export type UpsertUserLangSettingsResponse = Message<"connectrpc.management.horse_managements.v1.UpsertUserLangSettingsResponse"> & {
};

/**
 * Describes the message connectrpc.management.horse_managements.v1.UpsertUserLangSettingsResponse.
 * Use `create(UpsertUserLangSettingsResponseSchema)` to create a new message.
 */
export const UpsertUserLangSettingsResponseSchema: GenMessage<UpsertUserLangSettingsResponse> = /*@__PURE__*/
  messageDesc(file_horse_managements_user_service, 27);

/**
 * @generated from service connectrpc.management.horse_managements.v1.UserService
 */
export const UserService: GenService<{
  /**
   * @generated from rpc connectrpc.management.horse_managements.v1.UserService.GetMe
   */
  getMe: {
    methodKind: "unary";
    input: typeof GetMeRequestSchema;
    output: typeof GetMeResponseSchema;
  },
  /**
   * @generated from rpc connectrpc.management.horse_managements.v1.UserService.ResetPassword
   */
  resetPassword: {
    methodKind: "unary";
    input: typeof ResetPasswordRequestSchema;
    output: typeof ResetPasswordResponseSchema;
  },
  /**
   * @generated from rpc connectrpc.management.horse_managements.v1.UserService.PostDevice
   */
  postDevice: {
    methodKind: "unary";
    input: typeof PostDeviceRequestSchema;
    output: typeof PostDeviceResponseSchema;
  },
  /**
   * @generated from rpc connectrpc.management.horse_managements.v1.UserService.UpdateDevice
   */
  updateDevice: {
    methodKind: "unary";
    input: typeof UpdateDeviceRequestSchema;
    output: typeof UpdateDeviceResponseSchema;
  },
  /**
   * @generated from rpc connectrpc.management.horse_managements.v1.UserService.GetUser
   */
  getUser: {
    methodKind: "unary";
    input: typeof GetUserRequestSchema;
    output: typeof GetUserResponseSchema;
  },
  /**
   * @generated from rpc connectrpc.management.horse_managements.v1.UserService.ListUsers
   */
  listUsers: {
    methodKind: "unary";
    input: typeof ListUsersRequestSchema;
    output: typeof ListUsersResponseSchema;
  },
  /**
   * @generated from rpc connectrpc.management.horse_managements.v1.UserService.PatchUser
   */
  patchUser: {
    methodKind: "unary";
    input: typeof PatchUserRequestSchema;
    output: typeof PatchUserResponseSchema;
  },
  /**
   * @generated from rpc connectrpc.management.horse_managements.v1.UserService.DeleteUser
   */
  deleteUser: {
    methodKind: "unary";
    input: typeof DeleteUserRequestSchema;
    output: typeof DeleteUserResponseSchema;
  },
  /**
   * @generated from rpc connectrpc.management.horse_managements.v1.UserService.PostUser
   */
  postUser: {
    methodKind: "unary";
    input: typeof PostUserRequestSchema;
    output: typeof PostUserResponseSchema;
  },
  /**
   * @generated from rpc connectrpc.management.horse_managements.v1.UserService.RemoveStablePermission
   */
  removeStablePermission: {
    methodKind: "unary";
    input: typeof RemoveStablePermissionRequestSchema;
    output: typeof RemoveStablePermissionResponseSchema;
  },
  /**
   * @generated from rpc connectrpc.management.horse_managements.v1.UserService.AddStablePermission
   */
  addStablePermission: {
    methodKind: "unary";
    input: typeof AddStablePermissionRequestSchema;
    output: typeof AddStablePermissionResponseSchema;
  },
  /**
   * @generated from rpc connectrpc.management.horse_managements.v1.UserService.AddOrganizationPermission
   */
  addOrganizationPermission: {
    methodKind: "unary";
    input: typeof AddOrganizationPermissionRequestSchema;
    output: typeof AddOrganizationPermissionResponseSchema;
  },
  /**
   * @generated from rpc connectrpc.management.horse_managements.v1.UserService.RemoveOrganizationPermission
   */
  removeOrganizationPermission: {
    methodKind: "unary";
    input: typeof RemoveOrganizationPermissionRequestSchema;
    output: typeof RemoveOrganizationPermissionResponseSchema;
  },
  /**
   * @generated from rpc connectrpc.management.horse_managements.v1.UserService.UpsertUserLangSettings
   */
  upsertUserLangSettings: {
    methodKind: "unary";
    input: typeof UpsertUserLangSettingsRequestSchema;
    output: typeof UpsertUserLangSettingsResponseSchema;
  },
}> = /*@__PURE__*/
  serviceDesc(file_horse_managements_user_service, 0);

