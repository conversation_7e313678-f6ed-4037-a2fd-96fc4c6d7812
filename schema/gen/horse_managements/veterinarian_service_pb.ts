// @generated by protoc-gen-es v2.2.3 with parameter "target=ts"
// @generated from file horse_managements/veterinarian_service.proto (package connectrpc.management.horse_managements.v1, syntax proto3)
/* eslint-disable */

import type { GenFile, GenMessage, GenService } from "@bufbuild/protobuf/codegenv1";
import { fileDesc, messageDesc, serviceDesc } from "@bufbuild/protobuf/codegenv1";
import type { Veterinarian } from "./models/veterinarian_pb";
import { file_horse_managements_models_veterinarian } from "./models/veterinarian_pb";
import type { Message } from "@bufbuild/protobuf";

/**
 * Describes the file horse_managements/veterinarian_service.proto.
 */
export const file_horse_managements_veterinarian_service: GenFile = /*@__PURE__*/
  fileDesc("Cixob3JzZV9tYW5hZ2VtZW50cy92ZXRlcmluYXJpYW5fc2VydmljZS5wcm90bxIqY29ubmVjdHJwYy5tYW5hZ2VtZW50LmhvcnNlX21hbmFnZW1lbnRzLnYxIhoKGExpc3RWZXRlcmluYXJpYW5zUmVxdWVzdCJsChlMaXN0VmV0ZXJpbmFyaWFuc1Jlc3BvbnNlEk8KDXZldGVyaW5hcmlhbnMYASADKAsyOC5jb25uZWN0cnBjLm1hbmFnZW1lbnQuaG9yc2VfbWFuYWdlbWVudHMudjEuVmV0ZXJpbmFyaWFuIjQKF1Bvc3RWZXRlcmluYXJpYW5SZXF1ZXN0EhkKEXZldGVyaW5hcmlhbl9uYW1lGAEgASgJIjMKGFBvc3RWZXRlcmluYXJpYW5SZXNwb25zZRIXCg92ZXRlcmluYXJpYW5faWQYASABKAkiNAoZRGVsZXRlVmV0ZXJpbmFyaWFuUmVxdWVzdBIXCg92ZXRlcmluYXJpYW5faWQYASABKAkiHAoaRGVsZXRlVmV0ZXJpbmFyaWFuUmVzcG9uc2Uy/gMKE1ZldGVyaW5hcmlhblNlcnZpY2USoAEKEUxpc3RWZXRlcmluYXJpYW5zEkQuY29ubmVjdHJwYy5tYW5hZ2VtZW50LmhvcnNlX21hbmFnZW1lbnRzLnYxLkxpc3RWZXRlcmluYXJpYW5zUmVxdWVzdBpFLmNvbm5lY3RycGMubWFuYWdlbWVudC5ob3JzZV9tYW5hZ2VtZW50cy52MS5MaXN0VmV0ZXJpbmFyaWFuc1Jlc3BvbnNlEp0BChBQb3N0VmV0ZXJpbmFyaWFuEkMuY29ubmVjdHJwYy5tYW5hZ2VtZW50LmhvcnNlX21hbmFnZW1lbnRzLnYxLlBvc3RWZXRlcmluYXJpYW5SZXF1ZXN0GkQuY29ubmVjdHJwYy5tYW5hZ2VtZW50LmhvcnNlX21hbmFnZW1lbnRzLnYxLlBvc3RWZXRlcmluYXJpYW5SZXNwb25zZRKjAQoSRGVsZXRlVmV0ZXJpbmFyaWFuEkUuY29ubmVjdHJwYy5tYW5hZ2VtZW50LmhvcnNlX21hbmFnZW1lbnRzLnYxLkRlbGV0ZVZldGVyaW5hcmlhblJlcXVlc3QaRi5jb25uZWN0cnBjLm1hbmFnZW1lbnQuaG9yc2VfbWFuYWdlbWVudHMudjEuRGVsZXRlVmV0ZXJpbmFyaWFuUmVzcG9uc2ViBnByb3RvMw", [file_horse_managements_models_veterinarian]);

/**
 * @generated from message connectrpc.management.horse_managements.v1.ListVeterinariansRequest
 */
export type ListVeterinariansRequest = Message<"connectrpc.management.horse_managements.v1.ListVeterinariansRequest"> & {
};

/**
 * Describes the message connectrpc.management.horse_managements.v1.ListVeterinariansRequest.
 * Use `create(ListVeterinariansRequestSchema)` to create a new message.
 */
export const ListVeterinariansRequestSchema: GenMessage<ListVeterinariansRequest> = /*@__PURE__*/
  messageDesc(file_horse_managements_veterinarian_service, 0);

/**
 * @generated from message connectrpc.management.horse_managements.v1.ListVeterinariansResponse
 */
export type ListVeterinariansResponse = Message<"connectrpc.management.horse_managements.v1.ListVeterinariansResponse"> & {
  /**
   * @generated from field: repeated connectrpc.management.horse_managements.v1.Veterinarian veterinarians = 1;
   */
  veterinarians: Veterinarian[];
};

/**
 * Describes the message connectrpc.management.horse_managements.v1.ListVeterinariansResponse.
 * Use `create(ListVeterinariansResponseSchema)` to create a new message.
 */
export const ListVeterinariansResponseSchema: GenMessage<ListVeterinariansResponse> = /*@__PURE__*/
  messageDesc(file_horse_managements_veterinarian_service, 1);

/**
 * @generated from message connectrpc.management.horse_managements.v1.PostVeterinarianRequest
 */
export type PostVeterinarianRequest = Message<"connectrpc.management.horse_managements.v1.PostVeterinarianRequest"> & {
  /**
   * @generated from field: string veterinarian_name = 1;
   */
  veterinarianName: string;
};

/**
 * Describes the message connectrpc.management.horse_managements.v1.PostVeterinarianRequest.
 * Use `create(PostVeterinarianRequestSchema)` to create a new message.
 */
export const PostVeterinarianRequestSchema: GenMessage<PostVeterinarianRequest> = /*@__PURE__*/
  messageDesc(file_horse_managements_veterinarian_service, 2);

/**
 * @generated from message connectrpc.management.horse_managements.v1.PostVeterinarianResponse
 */
export type PostVeterinarianResponse = Message<"connectrpc.management.horse_managements.v1.PostVeterinarianResponse"> & {
  /**
   * @generated from field: string veterinarian_id = 1;
   */
  veterinarianId: string;
};

/**
 * Describes the message connectrpc.management.horse_managements.v1.PostVeterinarianResponse.
 * Use `create(PostVeterinarianResponseSchema)` to create a new message.
 */
export const PostVeterinarianResponseSchema: GenMessage<PostVeterinarianResponse> = /*@__PURE__*/
  messageDesc(file_horse_managements_veterinarian_service, 3);

/**
 * @generated from message connectrpc.management.horse_managements.v1.DeleteVeterinarianRequest
 */
export type DeleteVeterinarianRequest = Message<"connectrpc.management.horse_managements.v1.DeleteVeterinarianRequest"> & {
  /**
   * @generated from field: string veterinarian_id = 1;
   */
  veterinarianId: string;
};

/**
 * Describes the message connectrpc.management.horse_managements.v1.DeleteVeterinarianRequest.
 * Use `create(DeleteVeterinarianRequestSchema)` to create a new message.
 */
export const DeleteVeterinarianRequestSchema: GenMessage<DeleteVeterinarianRequest> = /*@__PURE__*/
  messageDesc(file_horse_managements_veterinarian_service, 4);

/**
 * @generated from message connectrpc.management.horse_managements.v1.DeleteVeterinarianResponse
 */
export type DeleteVeterinarianResponse = Message<"connectrpc.management.horse_managements.v1.DeleteVeterinarianResponse"> & {
};

/**
 * Describes the message connectrpc.management.horse_managements.v1.DeleteVeterinarianResponse.
 * Use `create(DeleteVeterinarianResponseSchema)` to create a new message.
 */
export const DeleteVeterinarianResponseSchema: GenMessage<DeleteVeterinarianResponse> = /*@__PURE__*/
  messageDesc(file_horse_managements_veterinarian_service, 5);

/**
 * @generated from service connectrpc.management.horse_managements.v1.VeterinarianService
 */
export const VeterinarianService: GenService<{
  /**
   * @generated from rpc connectrpc.management.horse_managements.v1.VeterinarianService.ListVeterinarians
   */
  listVeterinarians: {
    methodKind: "unary";
    input: typeof ListVeterinariansRequestSchema;
    output: typeof ListVeterinariansResponseSchema;
  },
  /**
   * @generated from rpc connectrpc.management.horse_managements.v1.VeterinarianService.PostVeterinarian
   */
  postVeterinarian: {
    methodKind: "unary";
    input: typeof PostVeterinarianRequestSchema;
    output: typeof PostVeterinarianResponseSchema;
  },
  /**
   * @generated from rpc connectrpc.management.horse_managements.v1.VeterinarianService.DeleteVeterinarian
   */
  deleteVeterinarian: {
    methodKind: "unary";
    input: typeof DeleteVeterinarianRequestSchema;
    output: typeof DeleteVeterinarianResponseSchema;
  },
}> = /*@__PURE__*/
  serviceDesc(file_horse_managements_veterinarian_service, 0);

