// @generated by protoc-gen-es v2.2.3 with parameter "target=ts"
// @generated from file horse_managements/race_place_service.proto (package connectrpc.management.horse_managements.v1, syntax proto3)
/* eslint-disable */

import type { GenFile, GenMessage, GenService } from "@bufbuild/protobuf/codegenv1";
import { fileDesc, messageDesc, serviceDesc } from "@bufbuild/protobuf/codegenv1";
import type { RacePlace } from "./models/race_place_pb";
import { file_horse_managements_models_race_place } from "./models/race_place_pb";
import type { Message } from "@bufbuild/protobuf";

/**
 * Describes the file horse_managements/race_place_service.proto.
 */
export const file_horse_managements_race_place_service: GenFile = /*@__PURE__*/
  fileDesc("Cipob3JzZV9tYW5hZ2VtZW50cy9yYWNlX3BsYWNlX3NlcnZpY2UucHJvdG8SKmNvbm5lY3RycGMubWFuYWdlbWVudC5ob3JzZV9tYW5hZ2VtZW50cy52MSIXChVMaXN0UmFjZVBsYWNlc1JlcXVlc3QiZAoWTGlzdFJhY2VQbGFjZXNSZXNwb25zZRJKCgtyYWNlX3BsYWNlcxgBIAMoCzI1LmNvbm5lY3RycGMubWFuYWdlbWVudC5ob3JzZV9tYW5hZ2VtZW50cy52MS5SYWNlUGxhY2UyrAEKEFJhY2VQbGFjZVNlcnZpY2USlwEKDkxpc3RSYWNlUGxhY2VzEkEuY29ubmVjdHJwYy5tYW5hZ2VtZW50LmhvcnNlX21hbmFnZW1lbnRzLnYxLkxpc3RSYWNlUGxhY2VzUmVxdWVzdBpCLmNvbm5lY3RycGMubWFuYWdlbWVudC5ob3JzZV9tYW5hZ2VtZW50cy52MS5MaXN0UmFjZVBsYWNlc1Jlc3BvbnNlYgZwcm90bzM", [file_horse_managements_models_race_place]);

/**
 * @generated from message connectrpc.management.horse_managements.v1.ListRacePlacesRequest
 */
export type ListRacePlacesRequest = Message<"connectrpc.management.horse_managements.v1.ListRacePlacesRequest"> & {
};

/**
 * Describes the message connectrpc.management.horse_managements.v1.ListRacePlacesRequest.
 * Use `create(ListRacePlacesRequestSchema)` to create a new message.
 */
export const ListRacePlacesRequestSchema: GenMessage<ListRacePlacesRequest> = /*@__PURE__*/
  messageDesc(file_horse_managements_race_place_service, 0);

/**
 * @generated from message connectrpc.management.horse_managements.v1.ListRacePlacesResponse
 */
export type ListRacePlacesResponse = Message<"connectrpc.management.horse_managements.v1.ListRacePlacesResponse"> & {
  /**
   * @generated from field: repeated connectrpc.management.horse_managements.v1.RacePlace race_places = 1;
   */
  racePlaces: RacePlace[];
};

/**
 * Describes the message connectrpc.management.horse_managements.v1.ListRacePlacesResponse.
 * Use `create(ListRacePlacesResponseSchema)` to create a new message.
 */
export const ListRacePlacesResponseSchema: GenMessage<ListRacePlacesResponse> = /*@__PURE__*/
  messageDesc(file_horse_managements_race_place_service, 1);

/**
 * @generated from service connectrpc.management.horse_managements.v1.RacePlaceService
 */
export const RacePlaceService: GenService<{
  /**
   * @generated from rpc connectrpc.management.horse_managements.v1.RacePlaceService.ListRacePlaces
   */
  listRacePlaces: {
    methodKind: "unary";
    input: typeof ListRacePlacesRequestSchema;
    output: typeof ListRacePlacesResponseSchema;
  },
}> = /*@__PURE__*/
  serviceDesc(file_horse_managements_race_place_service, 0);

