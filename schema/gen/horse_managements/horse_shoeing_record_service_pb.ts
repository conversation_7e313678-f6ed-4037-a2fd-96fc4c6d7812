// @generated by protoc-gen-es v2.2.3 with parameter "target=ts"
// @generated from file horse_managements/horse_shoeing_record_service.proto (package connectrpc.management.horse_managements.v1, syntax proto3)
/* eslint-disable */

import type { GenFile, GenMessage, GenService } from "@bufbuild/protobuf/codegenv1";
import { fileDesc, messageDesc, serviceDesc } from "@bufbuild/protobuf/codegenv1";
import type { HorseShoeingRecord } from "./models/horse_shoeing_record_pb";
import { file_horse_managements_models_horse_shoeing_record } from "./models/horse_shoeing_record_pb";
import type { Message } from "@bufbuild/protobuf";

/**
 * Describes the file horse_managements/horse_shoeing_record_service.proto.
 */
export const file_horse_managements_horse_shoeing_record_service: GenFile = /*@__PURE__*/
  fileDesc("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", [file_horse_managements_models_horse_shoeing_record]);

/**
 * @generated from message connectrpc.management.horse_managements.v1.ListHorseShoeingRecordsRequest
 */
export type ListHorseShoeingRecordsRequest = Message<"connectrpc.management.horse_managements.v1.ListHorseShoeingRecordsRequest"> & {
  /**
   * @generated from field: int64 horse_id = 1 [deprecated = true];
   * @deprecated
   */
  horseId: bigint;

  /**
   * @generated from field: optional int32 skip = 2;
   */
  skip?: number;

  /**
   * @generated from field: optional int64 optional_horse_id = 3;
   */
  optionalHorseId?: bigint;
};

/**
 * Describes the message connectrpc.management.horse_managements.v1.ListHorseShoeingRecordsRequest.
 * Use `create(ListHorseShoeingRecordsRequestSchema)` to create a new message.
 */
export const ListHorseShoeingRecordsRequestSchema: GenMessage<ListHorseShoeingRecordsRequest> = /*@__PURE__*/
  messageDesc(file_horse_managements_horse_shoeing_record_service, 0);

/**
 * @generated from message connectrpc.management.horse_managements.v1.ListHorseShoeingRecordsResponse
 */
export type ListHorseShoeingRecordsResponse = Message<"connectrpc.management.horse_managements.v1.ListHorseShoeingRecordsResponse"> & {
  /**
   * @generated from field: repeated connectrpc.management.horse_managements.v1.HorseShoeingRecord horse_shoeing_records = 1;
   */
  horseShoeingRecords: HorseShoeingRecord[];
};

/**
 * Describes the message connectrpc.management.horse_managements.v1.ListHorseShoeingRecordsResponse.
 * Use `create(ListHorseShoeingRecordsResponseSchema)` to create a new message.
 */
export const ListHorseShoeingRecordsResponseSchema: GenMessage<ListHorseShoeingRecordsResponse> = /*@__PURE__*/
  messageDesc(file_horse_managements_horse_shoeing_record_service, 1);

/**
 * @generated from message connectrpc.management.horse_managements.v1.GetHorseShoeingRecordRequest
 */
export type GetHorseShoeingRecordRequest = Message<"connectrpc.management.horse_managements.v1.GetHorseShoeingRecordRequest"> & {
  /**
   * @generated from field: string horse_shoeing_record_id = 1;
   */
  horseShoeingRecordId: string;
};

/**
 * Describes the message connectrpc.management.horse_managements.v1.GetHorseShoeingRecordRequest.
 * Use `create(GetHorseShoeingRecordRequestSchema)` to create a new message.
 */
export const GetHorseShoeingRecordRequestSchema: GenMessage<GetHorseShoeingRecordRequest> = /*@__PURE__*/
  messageDesc(file_horse_managements_horse_shoeing_record_service, 2);

/**
 * @generated from message connectrpc.management.horse_managements.v1.GetHorseShoeingRecordResponse
 */
export type GetHorseShoeingRecordResponse = Message<"connectrpc.management.horse_managements.v1.GetHorseShoeingRecordResponse"> & {
  /**
   * @generated from field: connectrpc.management.horse_managements.v1.HorseShoeingRecord horse_shoeing_record = 1;
   */
  horseShoeingRecord?: HorseShoeingRecord;
};

/**
 * Describes the message connectrpc.management.horse_managements.v1.GetHorseShoeingRecordResponse.
 * Use `create(GetHorseShoeingRecordResponseSchema)` to create a new message.
 */
export const GetHorseShoeingRecordResponseSchema: GenMessage<GetHorseShoeingRecordResponse> = /*@__PURE__*/
  messageDesc(file_horse_managements_horse_shoeing_record_service, 3);

/**
 * @generated from message connectrpc.management.horse_managements.v1.PostHorseShoeingRecordRequest
 */
export type PostHorseShoeingRecordRequest = Message<"connectrpc.management.horse_managements.v1.PostHorseShoeingRecordRequest"> & {
  /**
   * @generated from field: int64 horse_id = 1;
   */
  horseId: bigint;

  /**
   * @generated from field: int32 year = 2;
   */
  year: number;

  /**
   * @generated from field: int32 month = 3;
   */
  month: number;

  /**
   * @generated from field: int32 day = 4;
   */
  day: number;

  /**
   * @generated from field: repeated string horse_shoeing_invoice_photo_paths = 5;
   */
  horseShoeingInvoicePhotoPaths: string[];

  /**
   * @generated from field: optional string horse_shoeing_treatment_type = 6;
   */
  horseShoeingTreatmentType?: string;

  /**
   * @generated from field: optional string farrier_id = 7;
   */
  farrierId?: string;

  /**
   * @generated from field: optional string comment = 8;
   */
  comment?: string;
};

/**
 * Describes the message connectrpc.management.horse_managements.v1.PostHorseShoeingRecordRequest.
 * Use `create(PostHorseShoeingRecordRequestSchema)` to create a new message.
 */
export const PostHorseShoeingRecordRequestSchema: GenMessage<PostHorseShoeingRecordRequest> = /*@__PURE__*/
  messageDesc(file_horse_managements_horse_shoeing_record_service, 4);

/**
 * @generated from message connectrpc.management.horse_managements.v1.PostHorseShoeingRecordResponse
 */
export type PostHorseShoeingRecordResponse = Message<"connectrpc.management.horse_managements.v1.PostHorseShoeingRecordResponse"> & {
  /**
   * @generated from field: string horse_shoeing_record_id = 1;
   */
  horseShoeingRecordId: string;
};

/**
 * Describes the message connectrpc.management.horse_managements.v1.PostHorseShoeingRecordResponse.
 * Use `create(PostHorseShoeingRecordResponseSchema)` to create a new message.
 */
export const PostHorseShoeingRecordResponseSchema: GenMessage<PostHorseShoeingRecordResponse> = /*@__PURE__*/
  messageDesc(file_horse_managements_horse_shoeing_record_service, 5);

/**
 * @generated from message connectrpc.management.horse_managements.v1.UpdateHorseShoeingRecordRequest
 */
export type UpdateHorseShoeingRecordRequest = Message<"connectrpc.management.horse_managements.v1.UpdateHorseShoeingRecordRequest"> & {
  /**
   * @generated from field: string horse_shoeing_record_id = 1;
   */
  horseShoeingRecordId: string;

  /**
   * @generated from field: int64 horse_id = 2;
   */
  horseId: bigint;

  /**
   * @generated from field: int32 year = 3;
   */
  year: number;

  /**
   * @generated from field: int32 month = 4;
   */
  month: number;

  /**
   * @generated from field: int32 day = 5;
   */
  day: number;

  /**
   * @generated from field: repeated string horse_shoeing_invoice_photo_paths = 6;
   */
  horseShoeingInvoicePhotoPaths: string[];

  /**
   * @generated from field: optional string horse_shoeing_treatment_type = 7;
   */
  horseShoeingTreatmentType?: string;

  /**
   * @generated from field: optional string farrier_id = 8;
   */
  farrierId?: string;

  /**
   * @generated from field: optional string comment = 9;
   */
  comment?: string;
};

/**
 * Describes the message connectrpc.management.horse_managements.v1.UpdateHorseShoeingRecordRequest.
 * Use `create(UpdateHorseShoeingRecordRequestSchema)` to create a new message.
 */
export const UpdateHorseShoeingRecordRequestSchema: GenMessage<UpdateHorseShoeingRecordRequest> = /*@__PURE__*/
  messageDesc(file_horse_managements_horse_shoeing_record_service, 6);

/**
 * @generated from message connectrpc.management.horse_managements.v1.UpdateHorseShoeingRecordResponse
 */
export type UpdateHorseShoeingRecordResponse = Message<"connectrpc.management.horse_managements.v1.UpdateHorseShoeingRecordResponse"> & {
  /**
   * @generated from field: string horse_shoeing_record_id = 1;
   */
  horseShoeingRecordId: string;
};

/**
 * Describes the message connectrpc.management.horse_managements.v1.UpdateHorseShoeingRecordResponse.
 * Use `create(UpdateHorseShoeingRecordResponseSchema)` to create a new message.
 */
export const UpdateHorseShoeingRecordResponseSchema: GenMessage<UpdateHorseShoeingRecordResponse> = /*@__PURE__*/
  messageDesc(file_horse_managements_horse_shoeing_record_service, 7);

/**
 * @generated from message connectrpc.management.horse_managements.v1.DeleteHorseShoeingRecordRequest
 */
export type DeleteHorseShoeingRecordRequest = Message<"connectrpc.management.horse_managements.v1.DeleteHorseShoeingRecordRequest"> & {
  /**
   * @generated from field: string horse_shoeing_record_id = 1;
   */
  horseShoeingRecordId: string;
};

/**
 * Describes the message connectrpc.management.horse_managements.v1.DeleteHorseShoeingRecordRequest.
 * Use `create(DeleteHorseShoeingRecordRequestSchema)` to create a new message.
 */
export const DeleteHorseShoeingRecordRequestSchema: GenMessage<DeleteHorseShoeingRecordRequest> = /*@__PURE__*/
  messageDesc(file_horse_managements_horse_shoeing_record_service, 8);

/**
 * @generated from message connectrpc.management.horse_managements.v1.DeleteHorseShoeingRecordResponse
 */
export type DeleteHorseShoeingRecordResponse = Message<"connectrpc.management.horse_managements.v1.DeleteHorseShoeingRecordResponse"> & {
};

/**
 * Describes the message connectrpc.management.horse_managements.v1.DeleteHorseShoeingRecordResponse.
 * Use `create(DeleteHorseShoeingRecordResponseSchema)` to create a new message.
 */
export const DeleteHorseShoeingRecordResponseSchema: GenMessage<DeleteHorseShoeingRecordResponse> = /*@__PURE__*/
  messageDesc(file_horse_managements_horse_shoeing_record_service, 9);

/**
 * @generated from service connectrpc.management.horse_managements.v1.HorseShoeingRecordService
 */
export const HorseShoeingRecordService: GenService<{
  /**
   * @generated from rpc connectrpc.management.horse_managements.v1.HorseShoeingRecordService.ListHorseShoeingRecords
   */
  listHorseShoeingRecords: {
    methodKind: "unary";
    input: typeof ListHorseShoeingRecordsRequestSchema;
    output: typeof ListHorseShoeingRecordsResponseSchema;
  },
  /**
   * @generated from rpc connectrpc.management.horse_managements.v1.HorseShoeingRecordService.GetHorseShoeingRecord
   */
  getHorseShoeingRecord: {
    methodKind: "unary";
    input: typeof GetHorseShoeingRecordRequestSchema;
    output: typeof GetHorseShoeingRecordResponseSchema;
  },
  /**
   * @generated from rpc connectrpc.management.horse_managements.v1.HorseShoeingRecordService.PostHorseShoeingRecord
   */
  postHorseShoeingRecord: {
    methodKind: "unary";
    input: typeof PostHorseShoeingRecordRequestSchema;
    output: typeof PostHorseShoeingRecordResponseSchema;
  },
  /**
   * @generated from rpc connectrpc.management.horse_managements.v1.HorseShoeingRecordService.UpdateHorseShoeingRecord
   */
  updateHorseShoeingRecord: {
    methodKind: "unary";
    input: typeof UpdateHorseShoeingRecordRequestSchema;
    output: typeof UpdateHorseShoeingRecordResponseSchema;
  },
  /**
   * @generated from rpc connectrpc.management.horse_managements.v1.HorseShoeingRecordService.DeleteHorseShoeingRecord
   */
  deleteHorseShoeingRecord: {
    methodKind: "unary";
    input: typeof DeleteHorseShoeingRecordRequestSchema;
    output: typeof DeleteHorseShoeingRecordResponseSchema;
  },
}> = /*@__PURE__*/
  serviceDesc(file_horse_managements_horse_shoeing_record_service, 0);

