// @generated by protoc-gen-es v2.2.3 with parameter "target=ts"
// @generated from file horse_managements/stable_service.proto (package connectrpc.management.horse_managements.v1, syntax proto3)
/* eslint-disable */

import type { GenFile, GenMessage, GenService } from "@bufbuild/protobuf/codegenv1";
import { fileDesc, messageDesc, serviceDesc } from "@bufbuild/protobuf/codegenv1";
import type { Stable } from "./models/stable_pb";
import { file_horse_managements_models_stable } from "./models/stable_pb";
import type { Message } from "@bufbuild/protobuf";

/**
 * Describes the file horse_managements/stable_service.proto.
 */
export const file_horse_managements_stable_service: GenFile = /*@__PURE__*/
  fileDesc("CiZob3JzZV9tYW5hZ2VtZW50cy9zdGFibGVfc2VydmljZS5wcm90bxIqY29ubmVjdHJwYy5tYW5hZ2VtZW50LmhvcnNlX21hbmFnZW1lbnRzLnYxIhQKEkxpc3RTdGFibGVzUmVxdWVzdCJaChNMaXN0U3RhYmxlc1Jlc3BvbnNlEkMKB3N0YWJsZXMYASADKAsyMi5jb25uZWN0cnBjLm1hbmFnZW1lbnQuaG9yc2VfbWFuYWdlbWVudHMudjEuU3RhYmxlMqABCg1TdGFibGVTZXJ2aWNlEo4BCgtMaXN0U3RhYmxlcxI+LmNvbm5lY3RycGMubWFuYWdlbWVudC5ob3JzZV9tYW5hZ2VtZW50cy52MS5MaXN0U3RhYmxlc1JlcXVlc3QaPy5jb25uZWN0cnBjLm1hbmFnZW1lbnQuaG9yc2VfbWFuYWdlbWVudHMudjEuTGlzdFN0YWJsZXNSZXNwb25zZWIGcHJvdG8z", [file_horse_managements_models_stable]);

/**
 * @generated from message connectrpc.management.horse_managements.v1.ListStablesRequest
 */
export type ListStablesRequest = Message<"connectrpc.management.horse_managements.v1.ListStablesRequest"> & {
};

/**
 * Describes the message connectrpc.management.horse_managements.v1.ListStablesRequest.
 * Use `create(ListStablesRequestSchema)` to create a new message.
 */
export const ListStablesRequestSchema: GenMessage<ListStablesRequest> = /*@__PURE__*/
  messageDesc(file_horse_managements_stable_service, 0);

/**
 * @generated from message connectrpc.management.horse_managements.v1.ListStablesResponse
 */
export type ListStablesResponse = Message<"connectrpc.management.horse_managements.v1.ListStablesResponse"> & {
  /**
   * @generated from field: repeated connectrpc.management.horse_managements.v1.Stable stables = 1;
   */
  stables: Stable[];
};

/**
 * Describes the message connectrpc.management.horse_managements.v1.ListStablesResponse.
 * Use `create(ListStablesResponseSchema)` to create a new message.
 */
export const ListStablesResponseSchema: GenMessage<ListStablesResponse> = /*@__PURE__*/
  messageDesc(file_horse_managements_stable_service, 1);

/**
 * @generated from service connectrpc.management.horse_managements.v1.StableService
 */
export const StableService: GenService<{
  /**
   * @generated from rpc connectrpc.management.horse_managements.v1.StableService.ListStables
   */
  listStables: {
    methodKind: "unary";
    input: typeof ListStablesRequestSchema;
    output: typeof ListStablesResponseSchema;
  },
}> = /*@__PURE__*/
  serviceDesc(file_horse_managements_stable_service, 0);

