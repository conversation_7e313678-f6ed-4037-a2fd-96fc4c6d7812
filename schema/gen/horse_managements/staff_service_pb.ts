// @generated by protoc-gen-es v2.2.3 with parameter "target=ts"
// @generated from file horse_managements/staff_service.proto (package connectrpc.management.horse_managements.v1, syntax proto3)
/* eslint-disable */

import type { GenFile, GenMessage, GenService } from "@bufbuild/protobuf/codegenv1";
import { fileDesc, messageDesc, serviceDesc } from "@bufbuild/protobuf/codegenv1";
import type { Staff } from "./models/staff_pb";
import { file_horse_managements_models_staff } from "./models/staff_pb";
import type { Message } from "@bufbuild/protobuf";

/**
 * Describes the file horse_managements/staff_service.proto.
 */
export const file_horse_managements_staff_service: GenFile = /*@__PURE__*/
  fileDesc("CiVob3JzZV9tYW5hZ2VtZW50cy9zdGFmZl9zZXJ2aWNlLnByb3RvEipjb25uZWN0cnBjLm1hbmFnZW1lbnQuaG9yc2VfbWFuYWdlbWVudHMudjEiKAoRTGlzdFN0YWZmc1JlcXVlc3QSEwoLc3RhYmxlX3V1aWQYASABKAkiVwoSTGlzdFN0YWZmc1Jlc3BvbnNlEkEKBnN0YWZmcxgBIAMoCzIxLmNvbm5lY3RycGMubWFuYWdlbWVudC5ob3JzZV9tYW5hZ2VtZW50cy52MS5TdGFmZiI7ChBQb3N0U3RhZmZSZXF1ZXN0EhMKC3N0YWJsZV91dWlkGAEgASgJEhIKCnN0YWZmX25hbWUYAiABKAkiJwoRUG9zdFN0YWZmUmVzcG9uc2USEgoKc3RhZmZfdXVpZBgBIAEoCSIoChJEZWxldGVTdGFmZlJlcXVlc3QSEgoKc3RhZmZfdXVpZBgBIAEoCSIVChNEZWxldGVTdGFmZlJlc3BvbnNlMrgDCgxTdGFmZlNlcnZpY2USiwEKCkxpc3RTdGFmZnMSPS5jb25uZWN0cnBjLm1hbmFnZW1lbnQuaG9yc2VfbWFuYWdlbWVudHMudjEuTGlzdFN0YWZmc1JlcXVlc3QaPi5jb25uZWN0cnBjLm1hbmFnZW1lbnQuaG9yc2VfbWFuYWdlbWVudHMudjEuTGlzdFN0YWZmc1Jlc3BvbnNlEogBCglQb3N0U3RhZmYSPC5jb25uZWN0cnBjLm1hbmFnZW1lbnQuaG9yc2VfbWFuYWdlbWVudHMudjEuUG9zdFN0YWZmUmVxdWVzdBo9LmNvbm5lY3RycGMubWFuYWdlbWVudC5ob3JzZV9tYW5hZ2VtZW50cy52MS5Qb3N0U3RhZmZSZXNwb25zZRKOAQoLRGVsZXRlU3RhZmYSPi5jb25uZWN0cnBjLm1hbmFnZW1lbnQuaG9yc2VfbWFuYWdlbWVudHMudjEuRGVsZXRlU3RhZmZSZXF1ZXN0Gj8uY29ubmVjdHJwYy5tYW5hZ2VtZW50LmhvcnNlX21hbmFnZW1lbnRzLnYxLkRlbGV0ZVN0YWZmUmVzcG9uc2ViBnByb3RvMw", [file_horse_managements_models_staff]);

/**
 * @generated from message connectrpc.management.horse_managements.v1.ListStaffsRequest
 */
export type ListStaffsRequest = Message<"connectrpc.management.horse_managements.v1.ListStaffsRequest"> & {
  /**
   * @generated from field: string stable_uuid = 1;
   */
  stableUuid: string;
};

/**
 * Describes the message connectrpc.management.horse_managements.v1.ListStaffsRequest.
 * Use `create(ListStaffsRequestSchema)` to create a new message.
 */
export const ListStaffsRequestSchema: GenMessage<ListStaffsRequest> = /*@__PURE__*/
  messageDesc(file_horse_managements_staff_service, 0);

/**
 * @generated from message connectrpc.management.horse_managements.v1.ListStaffsResponse
 */
export type ListStaffsResponse = Message<"connectrpc.management.horse_managements.v1.ListStaffsResponse"> & {
  /**
   * @generated from field: repeated connectrpc.management.horse_managements.v1.Staff staffs = 1;
   */
  staffs: Staff[];
};

/**
 * Describes the message connectrpc.management.horse_managements.v1.ListStaffsResponse.
 * Use `create(ListStaffsResponseSchema)` to create a new message.
 */
export const ListStaffsResponseSchema: GenMessage<ListStaffsResponse> = /*@__PURE__*/
  messageDesc(file_horse_managements_staff_service, 1);

/**
 * @generated from message connectrpc.management.horse_managements.v1.PostStaffRequest
 */
export type PostStaffRequest = Message<"connectrpc.management.horse_managements.v1.PostStaffRequest"> & {
  /**
   * @generated from field: string stable_uuid = 1;
   */
  stableUuid: string;

  /**
   * @generated from field: string staff_name = 2;
   */
  staffName: string;
};

/**
 * Describes the message connectrpc.management.horse_managements.v1.PostStaffRequest.
 * Use `create(PostStaffRequestSchema)` to create a new message.
 */
export const PostStaffRequestSchema: GenMessage<PostStaffRequest> = /*@__PURE__*/
  messageDesc(file_horse_managements_staff_service, 2);

/**
 * @generated from message connectrpc.management.horse_managements.v1.PostStaffResponse
 */
export type PostStaffResponse = Message<"connectrpc.management.horse_managements.v1.PostStaffResponse"> & {
  /**
   * @generated from field: string staff_uuid = 1;
   */
  staffUuid: string;
};

/**
 * Describes the message connectrpc.management.horse_managements.v1.PostStaffResponse.
 * Use `create(PostStaffResponseSchema)` to create a new message.
 */
export const PostStaffResponseSchema: GenMessage<PostStaffResponse> = /*@__PURE__*/
  messageDesc(file_horse_managements_staff_service, 3);

/**
 * @generated from message connectrpc.management.horse_managements.v1.DeleteStaffRequest
 */
export type DeleteStaffRequest = Message<"connectrpc.management.horse_managements.v1.DeleteStaffRequest"> & {
  /**
   * @generated from field: string staff_uuid = 1;
   */
  staffUuid: string;
};

/**
 * Describes the message connectrpc.management.horse_managements.v1.DeleteStaffRequest.
 * Use `create(DeleteStaffRequestSchema)` to create a new message.
 */
export const DeleteStaffRequestSchema: GenMessage<DeleteStaffRequest> = /*@__PURE__*/
  messageDesc(file_horse_managements_staff_service, 4);

/**
 * @generated from message connectrpc.management.horse_managements.v1.DeleteStaffResponse
 */
export type DeleteStaffResponse = Message<"connectrpc.management.horse_managements.v1.DeleteStaffResponse"> & {
};

/**
 * Describes the message connectrpc.management.horse_managements.v1.DeleteStaffResponse.
 * Use `create(DeleteStaffResponseSchema)` to create a new message.
 */
export const DeleteStaffResponseSchema: GenMessage<DeleteStaffResponse> = /*@__PURE__*/
  messageDesc(file_horse_managements_staff_service, 5);

/**
 * @generated from service connectrpc.management.horse_managements.v1.StaffService
 */
export const StaffService: GenService<{
  /**
   * @generated from rpc connectrpc.management.horse_managements.v1.StaffService.ListStaffs
   */
  listStaffs: {
    methodKind: "unary";
    input: typeof ListStaffsRequestSchema;
    output: typeof ListStaffsResponseSchema;
  },
  /**
   * @generated from rpc connectrpc.management.horse_managements.v1.StaffService.PostStaff
   */
  postStaff: {
    methodKind: "unary";
    input: typeof PostStaffRequestSchema;
    output: typeof PostStaffResponseSchema;
  },
  /**
   * @generated from rpc connectrpc.management.horse_managements.v1.StaffService.DeleteStaff
   */
  deleteStaff: {
    methodKind: "unary";
    input: typeof DeleteStaffRequestSchema;
    output: typeof DeleteStaffResponseSchema;
  },
}> = /*@__PURE__*/
  serviceDesc(file_horse_managements_staff_service, 0);

