// @generated by protoc-gen-es v2.2.3 with parameter "target=ts"
// @generated from file horse_managements/training_menu_service.proto (package connectrpc.management.horse_managements.v1, syntax proto3)
/* eslint-disable */

import type { GenFile, GenMessage, GenService } from "@bufbuild/protobuf/codegenv1";
import { fileDesc, messageDesc, serviceDesc } from "@bufbuild/protobuf/codegenv1";
import type { TrainingMenu } from "./models/training_menu_pb";
import { file_horse_managements_models_training_menu } from "./models/training_menu_pb";
import type { Message } from "@bufbuild/protobuf";

/**
 * Describes the file horse_managements/training_menu_service.proto.
 */
export const file_horse_managements_training_menu_service: GenFile = /*@__PURE__*/
  fileDesc("Ci1ob3JzZV9tYW5hZ2VtZW50cy90cmFpbmluZ19tZW51X3NlcnZpY2UucHJvdG8SKmNvbm5lY3RycGMubWFuYWdlbWVudC5ob3JzZV9tYW5hZ2VtZW50cy52MSJGChhMaXN0VHJhaW5pbmdNZW51c1JlcXVlc3QSEwoLc3RhYmxlX3V1aWQYASABKAkSFQoNdHJhaW5pbmdfdHlwZRgCIAEoCSJtChlMaXN0VHJhaW5pbmdNZW51c1Jlc3BvbnNlElAKDnRyYWluaW5nX21lbnVzGAEgAygLMjguY29ubmVjdHJwYy5tYW5hZ2VtZW50LmhvcnNlX21hbmFnZW1lbnRzLnYxLlRyYWluaW5nTWVudSJhChdQb3N0VHJhaW5pbmdNZW51UmVxdWVzdBITCgtzdGFibGVfdXVpZBgBIAEoCRIaChJ0cmFpbmluZ19tZW51X25hbWUYAiABKAkSFQoNdHJhaW5pbmdfdHlwZRgDIAEoCSI2ChhQb3N0VHJhaW5pbmdNZW51UmVzcG9uc2USGgoSdHJhaW5pbmdfbWVudV91dWlkGAEgASgJIjcKGURlbGV0ZVRyYWluaW5nTWVudVJlcXVlc3QSGgoSdHJhaW5pbmdfbWVudV91dWlkGAEgASgJIhwKGkRlbGV0ZVRyYWluaW5nTWVudVJlc3BvbnNlMv4DChNUcmFpbmluZ01lbnVTZXJ2aWNlEqABChFMaXN0VHJhaW5pbmdNZW51cxJELmNvbm5lY3RycGMubWFuYWdlbWVudC5ob3JzZV9tYW5hZ2VtZW50cy52MS5MaXN0VHJhaW5pbmdNZW51c1JlcXVlc3QaRS5jb25uZWN0cnBjLm1hbmFnZW1lbnQuaG9yc2VfbWFuYWdlbWVudHMudjEuTGlzdFRyYWluaW5nTWVudXNSZXNwb25zZRKdAQoQUG9zdFRyYWluaW5nTWVudRJDLmNvbm5lY3RycGMubWFuYWdlbWVudC5ob3JzZV9tYW5hZ2VtZW50cy52MS5Qb3N0VHJhaW5pbmdNZW51UmVxdWVzdBpELmNvbm5lY3RycGMubWFuYWdlbWVudC5ob3JzZV9tYW5hZ2VtZW50cy52MS5Qb3N0VHJhaW5pbmdNZW51UmVzcG9uc2USowEKEkRlbGV0ZVRyYWluaW5nTWVudRJFLmNvbm5lY3RycGMubWFuYWdlbWVudC5ob3JzZV9tYW5hZ2VtZW50cy52MS5EZWxldGVUcmFpbmluZ01lbnVSZXF1ZXN0GkYuY29ubmVjdHJwYy5tYW5hZ2VtZW50LmhvcnNlX21hbmFnZW1lbnRzLnYxLkRlbGV0ZVRyYWluaW5nTWVudVJlc3BvbnNlYgZwcm90bzM", [file_horse_managements_models_training_menu]);

/**
 * @generated from message connectrpc.management.horse_managements.v1.ListTrainingMenusRequest
 */
export type ListTrainingMenusRequest = Message<"connectrpc.management.horse_managements.v1.ListTrainingMenusRequest"> & {
  /**
   * @generated from field: string stable_uuid = 1;
   */
  stableUuid: string;

  /**
   * @generated from field: string training_type = 2;
   */
  trainingType: string;
};

/**
 * Describes the message connectrpc.management.horse_managements.v1.ListTrainingMenusRequest.
 * Use `create(ListTrainingMenusRequestSchema)` to create a new message.
 */
export const ListTrainingMenusRequestSchema: GenMessage<ListTrainingMenusRequest> = /*@__PURE__*/
  messageDesc(file_horse_managements_training_menu_service, 0);

/**
 * @generated from message connectrpc.management.horse_managements.v1.ListTrainingMenusResponse
 */
export type ListTrainingMenusResponse = Message<"connectrpc.management.horse_managements.v1.ListTrainingMenusResponse"> & {
  /**
   * @generated from field: repeated connectrpc.management.horse_managements.v1.TrainingMenu training_menus = 1;
   */
  trainingMenus: TrainingMenu[];
};

/**
 * Describes the message connectrpc.management.horse_managements.v1.ListTrainingMenusResponse.
 * Use `create(ListTrainingMenusResponseSchema)` to create a new message.
 */
export const ListTrainingMenusResponseSchema: GenMessage<ListTrainingMenusResponse> = /*@__PURE__*/
  messageDesc(file_horse_managements_training_menu_service, 1);

/**
 * @generated from message connectrpc.management.horse_managements.v1.PostTrainingMenuRequest
 */
export type PostTrainingMenuRequest = Message<"connectrpc.management.horse_managements.v1.PostTrainingMenuRequest"> & {
  /**
   * @generated from field: string stable_uuid = 1;
   */
  stableUuid: string;

  /**
   * @generated from field: string training_menu_name = 2;
   */
  trainingMenuName: string;

  /**
   * @generated from field: string training_type = 3;
   */
  trainingType: string;
};

/**
 * Describes the message connectrpc.management.horse_managements.v1.PostTrainingMenuRequest.
 * Use `create(PostTrainingMenuRequestSchema)` to create a new message.
 */
export const PostTrainingMenuRequestSchema: GenMessage<PostTrainingMenuRequest> = /*@__PURE__*/
  messageDesc(file_horse_managements_training_menu_service, 2);

/**
 * @generated from message connectrpc.management.horse_managements.v1.PostTrainingMenuResponse
 */
export type PostTrainingMenuResponse = Message<"connectrpc.management.horse_managements.v1.PostTrainingMenuResponse"> & {
  /**
   * @generated from field: string training_menu_uuid = 1;
   */
  trainingMenuUuid: string;
};

/**
 * Describes the message connectrpc.management.horse_managements.v1.PostTrainingMenuResponse.
 * Use `create(PostTrainingMenuResponseSchema)` to create a new message.
 */
export const PostTrainingMenuResponseSchema: GenMessage<PostTrainingMenuResponse> = /*@__PURE__*/
  messageDesc(file_horse_managements_training_menu_service, 3);

/**
 * @generated from message connectrpc.management.horse_managements.v1.DeleteTrainingMenuRequest
 */
export type DeleteTrainingMenuRequest = Message<"connectrpc.management.horse_managements.v1.DeleteTrainingMenuRequest"> & {
  /**
   * @generated from field: string training_menu_uuid = 1;
   */
  trainingMenuUuid: string;
};

/**
 * Describes the message connectrpc.management.horse_managements.v1.DeleteTrainingMenuRequest.
 * Use `create(DeleteTrainingMenuRequestSchema)` to create a new message.
 */
export const DeleteTrainingMenuRequestSchema: GenMessage<DeleteTrainingMenuRequest> = /*@__PURE__*/
  messageDesc(file_horse_managements_training_menu_service, 4);

/**
 * @generated from message connectrpc.management.horse_managements.v1.DeleteTrainingMenuResponse
 */
export type DeleteTrainingMenuResponse = Message<"connectrpc.management.horse_managements.v1.DeleteTrainingMenuResponse"> & {
};

/**
 * Describes the message connectrpc.management.horse_managements.v1.DeleteTrainingMenuResponse.
 * Use `create(DeleteTrainingMenuResponseSchema)` to create a new message.
 */
export const DeleteTrainingMenuResponseSchema: GenMessage<DeleteTrainingMenuResponse> = /*@__PURE__*/
  messageDesc(file_horse_managements_training_menu_service, 5);

/**
 * @generated from service connectrpc.management.horse_managements.v1.TrainingMenuService
 */
export const TrainingMenuService: GenService<{
  /**
   * @generated from rpc connectrpc.management.horse_managements.v1.TrainingMenuService.ListTrainingMenus
   */
  listTrainingMenus: {
    methodKind: "unary";
    input: typeof ListTrainingMenusRequestSchema;
    output: typeof ListTrainingMenusResponseSchema;
  },
  /**
   * @generated from rpc connectrpc.management.horse_managements.v1.TrainingMenuService.PostTrainingMenu
   */
  postTrainingMenu: {
    methodKind: "unary";
    input: typeof PostTrainingMenuRequestSchema;
    output: typeof PostTrainingMenuResponseSchema;
  },
  /**
   * @generated from rpc connectrpc.management.horse_managements.v1.TrainingMenuService.DeleteTrainingMenu
   */
  deleteTrainingMenu: {
    methodKind: "unary";
    input: typeof DeleteTrainingMenuRequestSchema;
    output: typeof DeleteTrainingMenuResponseSchema;
  },
}> = /*@__PURE__*/
  serviceDesc(file_horse_managements_training_menu_service, 0);

