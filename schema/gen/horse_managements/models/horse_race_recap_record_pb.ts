// @generated by protoc-gen-es v2.2.3 with parameter "target=ts"
// @generated from file horse_managements/models/horse_race_recap_record.proto (package connectrpc.management.horse_managements.v1, syntax proto3)
/* eslint-disable */

import type { GenFile, GenMessage } from "@bufbuild/protobuf/codegenv1";
import { fileDesc, messageDesc } from "@bufbuild/protobuf/codegenv1";
import type { Staff } from "./staff_pb";
import { file_horse_managements_models_staff } from "./staff_pb";
import type { User } from "./user_pb";
import { file_horse_managements_models_user } from "./user_pb";
import type { Message } from "@bufbuild/protobuf";

/**
 * Describes the file horse_managements/models/horse_race_recap_record.proto.
 */
export const file_horse_managements_models_horse_race_recap_record: GenFile = /*@__PURE__*/
  fileDesc("CjZob3JzZV9tYW5hZ2VtZW50cy9tb2RlbHMvaG9yc2VfcmFjZV9yZWNhcF9yZWNvcmQucHJvdG8SKmNvbm5lY3RycGMubWFuYWdlbWVudC5ob3JzZV9tYW5hZ2VtZW50cy52MSLjBgoUSG9yc2VSYWNlUmVjYXBSZWNvcmQSIgoaaG9yc2VfcmFjZV9yZWNhcF9yZWNvcmRfaWQYASABKAkSDAoEeWVhchgCIAEoBRINCgVtb250aBgDIAEoBRILCgNkYXkYBCABKAUSFwoKYXR0ZW5kYW5jZRgFIAEoCUgAiAEBEkUKBXN0YWZmGAYgASgLMjEuY29ubmVjdHJwYy5tYW5hZ2VtZW50LmhvcnNlX21hbmFnZW1lbnRzLnYxLlN0YWZmSAGIAQESFgoJZXF1aXBtZW50GAcgASgJSAKIAQESHgoRdHJhbnNwb3J0X2NvbW1lbnQYCCABKAlIA4gBARIaCg1zdGFsbF9jb21tZW50GAkgASgJSASIAQESHAoPcGFkZG9ja19jb21tZW50GAogASgJSAWIAQESHAoPd2FybV91cF9jb21tZW50GAsgASgJSAaIAQESGQoMZ2F0ZV9jb21tZW50GAwgASgJSAeIAQESIgoVcmFjZV9zdHJhdGVneV9jb21tZW50GA0gASgJSAiIAQESHwoSYWZ0ZXJfcmFjZV9jb21tZW50GA4gASgJSAmIAQESGwoOam9ja2V5X2NvbW1lbnQYDyABKAlICogBARIcCg90cmFpbmVyX2NvbW1lbnQYECABKAlIC4gBARIeChFuZXh0X3JhY2VfY29tbWVudBgRIAEoCUgMiAEBEksKDGNyZWF0ZWRfdXNlchgSIAEoCzIwLmNvbm5lY3RycGMubWFuYWdlbWVudC5ob3JzZV9tYW5hZ2VtZW50cy52MS5Vc2VySA2IAQFCDQoLX2F0dGVuZGFuY2VCCAoGX3N0YWZmQgwKCl9lcXVpcG1lbnRCFAoSX3RyYW5zcG9ydF9jb21tZW50QhAKDl9zdGFsbF9jb21tZW50QhIKEF9wYWRkb2NrX2NvbW1lbnRCEgoQX3dhcm1fdXBfY29tbWVudEIPCg1fZ2F0ZV9jb21tZW50QhgKFl9yYWNlX3N0cmF0ZWd5X2NvbW1lbnRCFQoTX2FmdGVyX3JhY2VfY29tbWVudEIRCg9fam9ja2V5X2NvbW1lbnRCEgoQX3RyYWluZXJfY29tbWVudEIUChJfbmV4dF9yYWNlX2NvbW1lbnRCDwoNX2NyZWF0ZWRfdXNlcmIGcHJvdG8z", [file_horse_managements_models_staff, file_horse_managements_models_user]);

/**
 * @generated from message connectrpc.management.horse_managements.v1.HorseRaceRecapRecord
 */
export type HorseRaceRecapRecord = Message<"connectrpc.management.horse_managements.v1.HorseRaceRecapRecord"> & {
  /**
   * @generated from field: string horse_race_recap_record_id = 1;
   */
  horseRaceRecapRecordId: string;

  /**
   * @generated from field: int32 year = 2;
   */
  year: number;

  /**
   * @generated from field: int32 month = 3;
   */
  month: number;

  /**
   * @generated from field: int32 day = 4;
   */
  day: number;

  /**
   * @generated from field: optional string attendance = 5;
   */
  attendance?: string;

  /**
   * @generated from field: optional connectrpc.management.horse_managements.v1.Staff staff = 6;
   */
  staff?: Staff;

  /**
   * @generated from field: optional string equipment = 7;
   */
  equipment?: string;

  /**
   * @generated from field: optional string transport_comment = 8;
   */
  transportComment?: string;

  /**
   * @generated from field: optional string stall_comment = 9;
   */
  stallComment?: string;

  /**
   * @generated from field: optional string paddock_comment = 10;
   */
  paddockComment?: string;

  /**
   * @generated from field: optional string warm_up_comment = 11;
   */
  warmUpComment?: string;

  /**
   * @generated from field: optional string gate_comment = 12;
   */
  gateComment?: string;

  /**
   * @generated from field: optional string race_strategy_comment = 13;
   */
  raceStrategyComment?: string;

  /**
   * @generated from field: optional string after_race_comment = 14;
   */
  afterRaceComment?: string;

  /**
   * @generated from field: optional string jockey_comment = 15;
   */
  jockeyComment?: string;

  /**
   * @generated from field: optional string trainer_comment = 16;
   */
  trainerComment?: string;

  /**
   * @generated from field: optional string next_race_comment = 17;
   */
  nextRaceComment?: string;

  /**
   * @generated from field: optional connectrpc.management.horse_managements.v1.User created_user = 18;
   */
  createdUser?: User;
};

/**
 * Describes the message connectrpc.management.horse_managements.v1.HorseRaceRecapRecord.
 * Use `create(HorseRaceRecapRecordSchema)` to create a new message.
 */
export const HorseRaceRecapRecordSchema: GenMessage<HorseRaceRecapRecord> = /*@__PURE__*/
  messageDesc(file_horse_managements_models_horse_race_recap_record, 0);

