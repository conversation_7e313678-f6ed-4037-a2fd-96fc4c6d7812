// @generated by protoc-gen-es v2.2.3 with parameter "target=ts"
// @generated from file horse_managements/models/farrier.proto (package connectrpc.management.horse_managements.v1, syntax proto3)
/* eslint-disable */

import type { GenFile, GenMessage } from "@bufbuild/protobuf/codegenv1";
import { fileDesc, messageDesc } from "@bufbuild/protobuf/codegenv1";
import type { Message } from "@bufbuild/protobuf";

/**
 * Describes the file horse_managements/models/farrier.proto.
 */
export const file_horse_managements_models_farrier: GenFile = /*@__PURE__*/
  fileDesc("CiZob3JzZV9tYW5hZ2VtZW50cy9tb2RlbHMvZmFycmllci5wcm90bxIqY29ubmVjdHJwYy5tYW5hZ2VtZW50LmhvcnNlX21hbmFnZW1lbnRzLnYxIjMKB0ZhcnJpZXISEgoKZmFycmllcl9pZBgBIAEoCRIUCgxmYXJyaWVyX25hbWUYAiABKAliBnByb3RvMw");

/**
 * @generated from message connectrpc.management.horse_managements.v1.Farrier
 */
export type Farrier = Message<"connectrpc.management.horse_managements.v1.Farrier"> & {
  /**
   * @generated from field: string farrier_id = 1;
   */
  farrierId: string;

  /**
   * @generated from field: string farrier_name = 2;
   */
  farrierName: string;
};

/**
 * Describes the message connectrpc.management.horse_managements.v1.Farrier.
 * Use `create(FarrierSchema)` to create a new message.
 */
export const FarrierSchema: GenMessage<Farrier> = /*@__PURE__*/
  messageDesc(file_horse_managements_models_farrier, 0);

