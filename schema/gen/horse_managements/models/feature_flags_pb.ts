// @generated by protoc-gen-es v2.2.3 with parameter "target=ts"
// @generated from file horse_managements/models/feature_flags.proto (package connectrpc.management.horse_managements.v1, syntax proto3)
/* eslint-disable */

import type { GenFile, GenMessage } from "@bufbuild/protobuf/codegenv1";
import { fileDesc, messageDesc } from "@bufbuild/protobuf/codegenv1";
import type { Message } from "@bufbuild/protobuf";

/**
 * Describes the file horse_managements/models/feature_flags.proto.
 */
export const file_horse_managements_models_feature_flags: GenFile = /*@__PURE__*/
  fileDesc("Cixob3JzZV9tYW5hZ2VtZW50cy9tb2RlbHMvZmVhdHVyZV9mbGFncy5wcm90bxIqY29ubmVjdHJwYy5tYW5hZ2VtZW50LmhvcnNlX21hbmFnZW1lbnRzLnYxIlcKDEZlYXR1cmVGbGFncxIWCg5tb250aGx5X3JlcG9ydBgBIAEoCBIVCg1idXNpbmVzc190cmlwGAIgASgIEhgKEGxhbmd1YWdlX3NldHRpbmcYAyABKAhiBnByb3RvMw");

/**
 * @generated from message connectrpc.management.horse_managements.v1.FeatureFlags
 */
export type FeatureFlags = Message<"connectrpc.management.horse_managements.v1.FeatureFlags"> & {
  /**
   * @generated from field: bool monthly_report = 1;
   */
  monthlyReport: boolean;

  /**
   * @generated from field: bool business_trip = 2;
   */
  businessTrip: boolean;

  /**
   * @generated from field: bool language_setting = 3;
   */
  languageSetting: boolean;
};

/**
 * Describes the message connectrpc.management.horse_managements.v1.FeatureFlags.
 * Use `create(FeatureFlagsSchema)` to create a new message.
 */
export const FeatureFlagsSchema: GenMessage<FeatureFlags> = /*@__PURE__*/
  messageDesc(file_horse_managements_models_feature_flags, 0);

