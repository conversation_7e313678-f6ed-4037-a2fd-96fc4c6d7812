// @generated by protoc-gen-es v2.2.3 with parameter "target=ts"
// @generated from file horse_managements/models/staff.proto (package connectrpc.management.horse_managements.v1, syntax proto3)
/* eslint-disable */

import type { GenFile, GenMessage } from "@bufbuild/protobuf/codegenv1";
import { fileDesc, messageDesc } from "@bufbuild/protobuf/codegenv1";
import type { Message } from "@bufbuild/protobuf";

/**
 * Describes the file horse_managements/models/staff.proto.
 */
export const file_horse_managements_models_staff: GenFile = /*@__PURE__*/
  fileDesc("CiRob3JzZV9tYW5hZ2VtZW50cy9tb2RlbHMvc3RhZmYucHJvdG8SKmNvbm5lY3RycGMubWFuYWdlbWVudC5ob3JzZV9tYW5hZ2VtZW50cy52MSIvCgVTdGFmZhISCgpzdGFmZl91dWlkGAEgASgJEhIKCnN0YWZmX25hbWUYAiABKAliBnByb3RvMw");

/**
 * @generated from message connectrpc.management.horse_managements.v1.Staff
 */
export type Staff = Message<"connectrpc.management.horse_managements.v1.Staff"> & {
  /**
   * @generated from field: string staff_uuid = 1;
   */
  staffUuid: string;

  /**
   * @generated from field: string staff_name = 2;
   */
  staffName: string;
};

/**
 * Describes the message connectrpc.management.horse_managements.v1.Staff.
 * Use `create(StaffSchema)` to create a new message.
 */
export const StaffSchema: GenMessage<Staff> = /*@__PURE__*/
  messageDesc(file_horse_managements_models_staff, 0);

