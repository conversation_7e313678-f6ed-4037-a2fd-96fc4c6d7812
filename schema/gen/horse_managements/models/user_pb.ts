// @generated by protoc-gen-es v2.2.3 with parameter "target=ts"
// @generated from file horse_managements/models/user.proto (package connectrpc.management.horse_managements.v1, syntax proto3)
/* eslint-disable */

import type { GenFile, GenMessage } from "@bufbuild/protobuf/codegenv1";
import { fileDesc, messageDesc } from "@bufbuild/protobuf/codegenv1";
import type { Message } from "@bufbuild/protobuf";

/**
 * Describes the file horse_managements/models/user.proto.
 */
export const file_horse_managements_models_user: GenFile = /*@__PURE__*/
  fileDesc("CiNob3JzZV9tYW5hZ2VtZW50cy9tb2RlbHMvdXNlci5wcm90bxIqY29ubmVjdHJwYy5tYW5hZ2VtZW50LmhvcnNlX21hbmFnZW1lbnRzLnYxInoKBFVzZXISEQoJdXNlcl91dWlkGAEgASgJEhIKCmZpcnN0X25hbWUYAiABKAkSEwoLbWlkZGxlX25hbWUYAyABKAkSEQoJbGFzdF9uYW1lGAQgASgJEiMKG2hhc19vcmdhbml6YXRpb25fcGVybWlzc2lvbhgFIAEoCGIGcHJvdG8z");

/**
 * @generated from message connectrpc.management.horse_managements.v1.User
 */
export type User = Message<"connectrpc.management.horse_managements.v1.User"> & {
  /**
   * @generated from field: string user_uuid = 1;
   */
  userUuid: string;

  /**
   * @generated from field: string first_name = 2;
   */
  firstName: string;

  /**
   * @generated from field: string middle_name = 3;
   */
  middleName: string;

  /**
   * @generated from field: string last_name = 4;
   */
  lastName: string;

  /**
   * @generated from field: bool has_organization_permission = 5;
   */
  hasOrganizationPermission: boolean;
};

/**
 * Describes the message connectrpc.management.horse_managements.v1.User.
 * Use `create(UserSchema)` to create a new message.
 */
export const UserSchema: GenMessage<User> = /*@__PURE__*/
  messageDesc(file_horse_managements_models_user, 0);

