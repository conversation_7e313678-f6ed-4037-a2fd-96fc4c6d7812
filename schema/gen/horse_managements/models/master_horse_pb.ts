// @generated by protoc-gen-es v2.2.3 with parameter "target=ts"
// @generated from file horse_managements/models/master_horse.proto (package connectrpc.management.horse_managements.v1, syntax proto3)
/* eslint-disable */

import type { GenFile, GenMessage } from "@bufbuild/protobuf/codegenv1";
import { fileDesc, messageDesc } from "@bufbuild/protobuf/codegenv1";
import type { Message } from "@bufbuild/protobuf";

/**
 * Describes the file horse_managements/models/master_horse.proto.
 */
export const file_horse_managements_models_master_horse: GenFile = /*@__PURE__*/
  fileDesc("Citob3JzZV9tYW5hZ2VtZW50cy9tb2RlbHMvbWFzdGVyX2hvcnNlLnByb3RvEipjb25uZWN0cnBjLm1hbmFnZW1lbnQuaG9yc2VfbWFuYWdlbWVudHMudjEidgoLTWFzdGVySG9yc2USFwoPbWFzdGVyX2hvcnNlX2lkGAEgASgJEgwKBG5hbWUYAiABKAkSDwoHbmFtZV9lbhgDIAEoCRIOCgZnZW5kZXIYBCABKAkSCwoDYWdlGAUgASgFEhIKCmJpcnRoX3llYXIYBiABKAViBnByb3RvMw");

/**
 * @generated from message connectrpc.management.horse_managements.v1.MasterHorse
 */
export type MasterHorse = Message<"connectrpc.management.horse_managements.v1.MasterHorse"> & {
  /**
   * @generated from field: string master_horse_id = 1;
   */
  masterHorseId: string;

  /**
   * @generated from field: string name = 2;
   */
  name: string;

  /**
   * @generated from field: string name_en = 3;
   */
  nameEn: string;

  /**
   * @generated from field: string gender = 4;
   */
  gender: string;

  /**
   * @generated from field: int32 age = 5;
   */
  age: number;

  /**
   * @generated from field: int32 birth_year = 6;
   */
  birthYear: number;
};

/**
 * Describes the message connectrpc.management.horse_managements.v1.MasterHorse.
 * Use `create(MasterHorseSchema)` to create a new message.
 */
export const MasterHorseSchema: GenMessage<MasterHorse> = /*@__PURE__*/
  messageDesc(file_horse_managements_models_master_horse, 0);

