// @generated by protoc-gen-es v2.2.3 with parameter "target=ts"
// @generated from file horse_managements/models/horse_shoeing_record.proto (package connectrpc.management.horse_managements.v1, syntax proto3)
/* eslint-disable */

import type { GenFile, GenMessage } from "@bufbuild/protobuf/codegenv1";
import { fileDesc, messageDesc } from "@bufbuild/protobuf/codegenv1";
import type { Farrier } from "./farrier_pb";
import { file_horse_managements_models_farrier } from "./farrier_pb";
import type { User } from "./user_pb";
import { file_horse_managements_models_user } from "./user_pb";
import type { Message } from "@bufbuild/protobuf";

/**
 * Describes the file horse_managements/models/horse_shoeing_record.proto.
 */
export const file_horse_managements_models_horse_shoeing_record: GenFile = /*@__PURE__*/
  fileDesc("CjNob3JzZV9tYW5hZ2VtZW50cy9tb2RlbHMvaG9yc2Vfc2hvZWluZ19yZWNvcmQucHJvdG8SKmNvbm5lY3RycGMubWFuYWdlbWVudC5ob3JzZV9tYW5hZ2VtZW50cy52MSLuAwoSSG9yc2VTaG9laW5nUmVjb3JkEh8KF2hvcnNlX3Nob2VpbmdfcmVjb3JkX2lkGAEgASgJEgwKBHllYXIYAiABKAUSDQoFbW9udGgYAyABKAUSCwoDZGF5GAQgASgFEmoKHGhvcnNlX3Nob2VpbmdfaW52b2ljZV9waG90b3MYBSADKAsyRC5jb25uZWN0cnBjLm1hbmFnZW1lbnQuaG9yc2VfbWFuYWdlbWVudHMudjEuSG9yc2VTaG9laW5nSW52b2ljZVBob3RvEikKHGhvcnNlX3Nob2VpbmdfdHJlYXRtZW50X3R5cGUYBiABKAlIAIgBARJJCgdmYXJyaWVyGAcgASgLMjMuY29ubmVjdHJwYy5tYW5hZ2VtZW50LmhvcnNlX21hbmFnZW1lbnRzLnYxLkZhcnJpZXJIAYgBARIUCgdjb21tZW50GAggASgJSAKIAQESSwoMY3JlYXRlZF91c2VyGAkgASgLMjAuY29ubmVjdHJwYy5tYW5hZ2VtZW50LmhvcnNlX21hbmFnZW1lbnRzLnYxLlVzZXJIA4gBAUIfCh1faG9yc2Vfc2hvZWluZ190cmVhdG1lbnRfdHlwZUIKCghfZmFycmllckIKCghfY29tbWVudEIPCg1fY3JlYXRlZF91c2VyIm0KGEhvcnNlU2hvZWluZ0ludm9pY2VQaG90bxIoCiBob3JzZV9zaG9laW5nX2ludm9pY2VfcGhvdG9fcGF0aBgBIAEoCRInCh9ob3JzZV9zaG9laW5nX2ludm9pY2VfcGhvdG9fdXJsGAIgASgJYgZwcm90bzM", [file_horse_managements_models_farrier, file_horse_managements_models_user]);

/**
 * @generated from message connectrpc.management.horse_managements.v1.HorseShoeingRecord
 */
export type HorseShoeingRecord = Message<"connectrpc.management.horse_managements.v1.HorseShoeingRecord"> & {
  /**
   * @generated from field: string horse_shoeing_record_id = 1;
   */
  horseShoeingRecordId: string;

  /**
   * @generated from field: int32 year = 2;
   */
  year: number;

  /**
   * @generated from field: int32 month = 3;
   */
  month: number;

  /**
   * @generated from field: int32 day = 4;
   */
  day: number;

  /**
   * @generated from field: repeated connectrpc.management.horse_managements.v1.HorseShoeingInvoicePhoto horse_shoeing_invoice_photos = 5;
   */
  horseShoeingInvoicePhotos: HorseShoeingInvoicePhoto[];

  /**
   * @generated from field: optional string horse_shoeing_treatment_type = 6;
   */
  horseShoeingTreatmentType?: string;

  /**
   * @generated from field: optional connectrpc.management.horse_managements.v1.Farrier farrier = 7;
   */
  farrier?: Farrier;

  /**
   * @generated from field: optional string comment = 8;
   */
  comment?: string;

  /**
   * @generated from field: optional connectrpc.management.horse_managements.v1.User created_user = 9;
   */
  createdUser?: User;
};

/**
 * Describes the message connectrpc.management.horse_managements.v1.HorseShoeingRecord.
 * Use `create(HorseShoeingRecordSchema)` to create a new message.
 */
export const HorseShoeingRecordSchema: GenMessage<HorseShoeingRecord> = /*@__PURE__*/
  messageDesc(file_horse_managements_models_horse_shoeing_record, 0);

/**
 * @generated from message connectrpc.management.horse_managements.v1.HorseShoeingInvoicePhoto
 */
export type HorseShoeingInvoicePhoto = Message<"connectrpc.management.horse_managements.v1.HorseShoeingInvoicePhoto"> & {
  /**
   * @generated from field: string horse_shoeing_invoice_photo_path = 1;
   */
  horseShoeingInvoicePhotoPath: string;

  /**
   * @generated from field: string horse_shoeing_invoice_photo_url = 2;
   */
  horseShoeingInvoicePhotoUrl: string;
};

/**
 * Describes the message connectrpc.management.horse_managements.v1.HorseShoeingInvoicePhoto.
 * Use `create(HorseShoeingInvoicePhotoSchema)` to create a new message.
 */
export const HorseShoeingInvoicePhotoSchema: GenMessage<HorseShoeingInvoicePhoto> = /*@__PURE__*/
  messageDesc(file_horse_managements_models_horse_shoeing_record, 1);

