// @generated by protoc-gen-es v2.2.3 with parameter "target=ts"
// @generated from file horse_managements/models/horse_training_record.proto (package connectrpc.management.horse_managements.v1, syntax proto3)
/* eslint-disable */

import type { GenEnum, GenFile, GenMessage } from "@bufbuild/protobuf/codegenv1";
import { enumDesc, fileDesc, messageDesc } from "@bufbuild/protobuf/codegenv1";
import type { Course } from "./course_pb";
import { file_horse_managements_models_course } from "./course_pb";
import type { Facility } from "./facility_pb";
import { file_horse_managements_models_facility } from "./facility_pb";
import type { Staff } from "./staff_pb";
import { file_horse_managements_models_staff } from "./staff_pb";
import type { TrainingMenu } from "./training_menu_pb";
import { file_horse_managements_models_training_menu } from "./training_menu_pb";
import type { User } from "./user_pb";
import { file_horse_managements_models_user } from "./user_pb";
import type { Message } from "@bufbuild/protobuf";

/**
 * Describes the file horse_managements/models/horse_training_record.proto.
 */
export const file_horse_managements_models_horse_training_record: GenFile = /*@__PURE__*/
  fileDesc("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", [file_horse_managements_models_course, file_horse_managements_models_facility, file_horse_managements_models_staff, file_horse_managements_models_training_menu, file_horse_managements_models_user]);

/**
 * @generated from message connectrpc.management.horse_managements.v1.HorseTrainingRecord
 */
export type HorseTrainingRecord = Message<"connectrpc.management.horse_managements.v1.HorseTrainingRecord"> & {
  /**
   * @generated from field: string horse_training_record_id = 1;
   */
  horseTrainingRecordId: string;

  /**
   * @generated from field: int32 year = 2;
   */
  year: number;

  /**
   * @generated from field: int32 month = 3;
   */
  month: number;

  /**
   * @generated from field: int32 day = 4;
   */
  day: number;

  /**
   * @generated from field: bool is_gait_abnormal = 5;
   */
  isGaitAbnormal: boolean;

  /**
   * @generated from field: optional string gait_abnormal_description = 6;
   */
  gaitAbnormalDescription?: string;

  /**
   * @generated from field: optional string training_type = 7;
   */
  trainingType?: string;

  /**
   * @generated from field: optional connectrpc.management.horse_managements.v1.Staff rider = 8;
   */
  rider?: Staff;

  /**
   * @generated from field: optional connectrpc.management.horse_managements.v1.TrainingMenu training_menu = 9;
   */
  trainingMenu?: TrainingMenu;

  /**
   * @generated from field: optional connectrpc.management.horse_managements.v1.Facility facility = 10;
   */
  facility?: Facility;

  /**
   * @generated from field: optional string facility_name = 11;
   */
  facilityName?: string;

  /**
   * @generated from field: optional connectrpc.management.horse_managements.v1.Course course = 12;
   */
  course?: Course;

  /**
   * @generated from field: optional string course_name = 13;
   */
  courseName?: string;

  /**
   * @generated from field: optional string course_going = 14;
   */
  courseGoing?: string;

  /**
   * @generated from field: repeated connectrpc.management.horse_managements.v1.FurlongTime furlong_times = 15;
   */
  furlongTimes: FurlongTime[];

  /**
   * @generated from field: optional string furlong_time_position = 16;
   */
  furlongTimePosition?: string;

  /**
   * @generated from field: optional double lactate_level = 17;
   */
  lactateLevel?: number;

  /**
   * @generated from field: optional string training_comment = 18;
   */
  trainingComment?: string;

  /**
   * @generated from field: repeated connectrpc.management.horse_managements.v1.TrainingPartner training_partners = 19;
   */
  trainingPartners: TrainingPartner[];

  /**
   * @generated from field: optional string gate_training_type = 20;
   */
  gateTrainingType?: string;

  /**
   * @generated from field: optional connectrpc.management.horse_managements.v1.PoolTrainingType pool_training_type = 21;
   */
  poolTrainingType?: PoolTrainingType;

  /**
   * @generated from field: optional connectrpc.management.horse_managements.v1.User created_user = 22;
   */
  createdUser?: User;
};

/**
 * Describes the message connectrpc.management.horse_managements.v1.HorseTrainingRecord.
 * Use `create(HorseTrainingRecordSchema)` to create a new message.
 */
export const HorseTrainingRecordSchema: GenMessage<HorseTrainingRecord> = /*@__PURE__*/
  messageDesc(file_horse_managements_models_horse_training_record, 0);

/**
 * @generated from message connectrpc.management.horse_managements.v1.FurlongTime
 */
export type FurlongTime = Message<"connectrpc.management.horse_managements.v1.FurlongTime"> & {
  /**
   * @generated from field: int32 furlong = 1;
   */
  furlong: number;

  /**
   * @generated from field: double time = 2;
   */
  time: number;
};

/**
 * Describes the message connectrpc.management.horse_managements.v1.FurlongTime.
 * Use `create(FurlongTimeSchema)` to create a new message.
 */
export const FurlongTimeSchema: GenMessage<FurlongTime> = /*@__PURE__*/
  messageDesc(file_horse_managements_models_horse_training_record, 1);

/**
 * @generated from message connectrpc.management.horse_managements.v1.TrainingPartner
 */
export type TrainingPartner = Message<"connectrpc.management.horse_managements.v1.TrainingPartner"> & {
  /**
   * @generated from field: optional int64 horse_id = 1;
   */
  horseId?: bigint;

  /**
   * @generated from field: string horse_name = 2;
   */
  horseName: string;

  /**
   * @generated from field: int32 rank = 3;
   */
  rank: number;

  /**
   * @generated from field: optional string track_position = 4;
   */
  trackPosition?: string;

  /**
   * @generated from field: optional int32 starting_order = 5;
   */
  startingOrder?: number;

  /**
   * @generated from field: optional string detail = 6;
   */
  detail?: string;

  /**
   * @generated from field: optional string intensity = 7;
   */
  intensity?: string;

  /**
   * @generated from field: optional string margin = 8;
   */
  margin?: string;
};

/**
 * Describes the message connectrpc.management.horse_managements.v1.TrainingPartner.
 * Use `create(TrainingPartnerSchema)` to create a new message.
 */
export const TrainingPartnerSchema: GenMessage<TrainingPartner> = /*@__PURE__*/
  messageDesc(file_horse_managements_models_horse_training_record, 2);

/**
 * @generated from enum connectrpc.management.horse_managements.v1.PoolTrainingType
 */
export enum PoolTrainingType {
  /**
   * @generated from enum value: POOL_TRAINING_TYPE_UNSPECIFIED = 0;
   */
  UNSPECIFIED = 0,

  /**
   * @generated from enum value: POOL_TRAINING_TYPE_CIRCLE = 1;
   */
  CIRCLE = 1,

  /**
   * @generated from enum value: POOL_TRAINING_TYPE_STRAIGHT = 2;
   */
  STRAIGHT = 2,

  /**
   * @generated from enum value: POOL_TRAINING_TYPE_SHOWER = 3;
   */
  SHOWER = 3,
}

/**
 * Describes the enum connectrpc.management.horse_managements.v1.PoolTrainingType.
 */
export const PoolTrainingTypeSchema: GenEnum<PoolTrainingType> = /*@__PURE__*/
  enumDesc(file_horse_managements_models_horse_training_record, 0);

