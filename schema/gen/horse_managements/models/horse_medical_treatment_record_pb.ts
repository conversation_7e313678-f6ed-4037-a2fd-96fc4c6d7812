// @generated by protoc-gen-es v2.2.3 with parameter "target=ts"
// @generated from file horse_managements/models/horse_medical_treatment_record.proto (package connectrpc.management.horse_managements.v1, syntax proto3)
/* eslint-disable */

import type { GenFile, GenMessage } from "@bufbuild/protobuf/codegenv1";
import { fileDesc, messageDesc } from "@bufbuild/protobuf/codegenv1";
import type { User } from "./user_pb";
import { file_horse_managements_models_user } from "./user_pb";
import type { Veterinarian } from "./veterinarian_pb";
import { file_horse_managements_models_veterinarian } from "./veterinarian_pb";
import type { Message } from "@bufbuild/protobuf";

/**
 * Describes the file horse_managements/models/horse_medical_treatment_record.proto.
 */
export const file_horse_managements_models_horse_medical_treatment_record: GenFile = /*@__PURE__*/
  fileDesc("Cj1ob3JzZV9tYW5hZ2VtZW50cy9tb2RlbHMvaG9yc2VfbWVkaWNhbF90cmVhdG1lbnRfcmVjb3JkLnByb3RvEipjb25uZWN0cnBjLm1hbmFnZW1lbnQuaG9yc2VfbWFuYWdlbWVudHMudjEiiAcKG0hvcnNlTWVkaWNhbFRyZWF0bWVudFJlY29yZBIpCiFob3JzZV9tZWRpY2FsX3RyZWF0bWVudF9yZWNvcmRfaWQYASABKAkSDAoEeWVhchgCIAEoBRINCgVtb250aBgDIAEoBRILCgNkYXkYBCABKAUSfQomaG9yc2VfbWVkaWNhbF90cmVhdG1lbnRfaW52b2ljZV9waG90b3MYBSADKAsyTS5jb25uZWN0cnBjLm1hbmFnZW1lbnQuaG9yc2VfbWFuYWdlbWVudHMudjEuSG9yc2VNZWRpY2FsVHJlYXRtZW50SW52b2ljZVBob3RvEisKHmhvcnNlX21lZGljYWxfdHJlYXRtZW50X3JlYXNvbhgGIAEoCUgAiAEBEi8KImhvcnNlX21lZGljYWxfdHJlYXRtZW50X2luc3BlY3Rpb24YByABKAlIAYgBARIrCh5ob3JzZV9tZWRpY2FsX3RyZWF0bWVudF9yZXN1bHQYCCABKAlIAogBARIrCh5ob3JzZV9tZWRpY2FsX3RyZWF0bWVudF9kZXRhaWwYCSABKAlIA4gBARJTCgx2ZXRlcmluYXJpYW4YCiABKAsyOC5jb25uZWN0cnBjLm1hbmFnZW1lbnQuaG9yc2VfbWFuYWdlbWVudHMudjEuVmV0ZXJpbmFyaWFuSASIAQESiAEKLGhvcnNlX21lZGljYWxfdHJlYXRtZW50X2FmZmVjdGVkX2FyZWFfcGhvdG9zGAsgAygLMlIuY29ubmVjdHJwYy5tYW5hZ2VtZW50LmhvcnNlX21hbmFnZW1lbnRzLnYxLkhvcnNlTWVkaWNhbFRyZWF0bWVudEFmZmVjdGVkQXJlYVBob3RvEksKDGNyZWF0ZWRfdXNlchgMIAEoCzIwLmNvbm5lY3RycGMubWFuYWdlbWVudC5ob3JzZV9tYW5hZ2VtZW50cy52MS5Vc2VySAWIAQFCIQofX2hvcnNlX21lZGljYWxfdHJlYXRtZW50X3JlYXNvbkIlCiNfaG9yc2VfbWVkaWNhbF90cmVhdG1lbnRfaW5zcGVjdGlvbkIhCh9faG9yc2VfbWVkaWNhbF90cmVhdG1lbnRfcmVzdWx0QiEKH19ob3JzZV9tZWRpY2FsX3RyZWF0bWVudF9kZXRhaWxCDwoNX3ZldGVyaW5hcmlhbkIPCg1fY3JlYXRlZF91c2VyIooBCiFIb3JzZU1lZGljYWxUcmVhdG1lbnRJbnZvaWNlUGhvdG8SMgoqaG9yc2VfbWVkaWNhbF90cmVhdG1lbnRfaW52b2ljZV9waG90b19wYXRoGAEgASgJEjEKKWhvcnNlX21lZGljYWxfdHJlYXRtZW50X2ludm9pY2VfcGhvdG9fdXJsGAIgASgJIpsBCiZIb3JzZU1lZGljYWxUcmVhdG1lbnRBZmZlY3RlZEFyZWFQaG90bxI4CjBob3JzZV9tZWRpY2FsX3RyZWF0bWVudF9hZmZlY3RlZF9hcmVhX3Bob3RvX3BhdGgYASABKAkSNwovaG9yc2VfbWVkaWNhbF90cmVhdG1lbnRfYWZmZWN0ZWRfYXJlYV9waG90b191cmwYAiABKAliBnByb3RvMw", [file_horse_managements_models_user, file_horse_managements_models_veterinarian]);

/**
 * @generated from message connectrpc.management.horse_managements.v1.HorseMedicalTreatmentRecord
 */
export type HorseMedicalTreatmentRecord = Message<"connectrpc.management.horse_managements.v1.HorseMedicalTreatmentRecord"> & {
  /**
   * @generated from field: string horse_medical_treatment_record_id = 1;
   */
  horseMedicalTreatmentRecordId: string;

  /**
   * @generated from field: int32 year = 2;
   */
  year: number;

  /**
   * @generated from field: int32 month = 3;
   */
  month: number;

  /**
   * @generated from field: int32 day = 4;
   */
  day: number;

  /**
   * @generated from field: repeated connectrpc.management.horse_managements.v1.HorseMedicalTreatmentInvoicePhoto horse_medical_treatment_invoice_photos = 5;
   */
  horseMedicalTreatmentInvoicePhotos: HorseMedicalTreatmentInvoicePhoto[];

  /**
   * @generated from field: optional string horse_medical_treatment_reason = 6;
   */
  horseMedicalTreatmentReason?: string;

  /**
   * @generated from field: optional string horse_medical_treatment_inspection = 7;
   */
  horseMedicalTreatmentInspection?: string;

  /**
   * @generated from field: optional string horse_medical_treatment_result = 8;
   */
  horseMedicalTreatmentResult?: string;

  /**
   * @generated from field: optional string horse_medical_treatment_detail = 9;
   */
  horseMedicalTreatmentDetail?: string;

  /**
   * @generated from field: optional connectrpc.management.horse_managements.v1.Veterinarian veterinarian = 10;
   */
  veterinarian?: Veterinarian;

  /**
   * @generated from field: repeated connectrpc.management.horse_managements.v1.HorseMedicalTreatmentAffectedAreaPhoto horse_medical_treatment_affected_area_photos = 11;
   */
  horseMedicalTreatmentAffectedAreaPhotos: HorseMedicalTreatmentAffectedAreaPhoto[];

  /**
   * @generated from field: optional connectrpc.management.horse_managements.v1.User created_user = 12;
   */
  createdUser?: User;
};

/**
 * Describes the message connectrpc.management.horse_managements.v1.HorseMedicalTreatmentRecord.
 * Use `create(HorseMedicalTreatmentRecordSchema)` to create a new message.
 */
export const HorseMedicalTreatmentRecordSchema: GenMessage<HorseMedicalTreatmentRecord> = /*@__PURE__*/
  messageDesc(file_horse_managements_models_horse_medical_treatment_record, 0);

/**
 * @generated from message connectrpc.management.horse_managements.v1.HorseMedicalTreatmentInvoicePhoto
 */
export type HorseMedicalTreatmentInvoicePhoto = Message<"connectrpc.management.horse_managements.v1.HorseMedicalTreatmentInvoicePhoto"> & {
  /**
   * @generated from field: string horse_medical_treatment_invoice_photo_path = 1;
   */
  horseMedicalTreatmentInvoicePhotoPath: string;

  /**
   * @generated from field: string horse_medical_treatment_invoice_photo_url = 2;
   */
  horseMedicalTreatmentInvoicePhotoUrl: string;
};

/**
 * Describes the message connectrpc.management.horse_managements.v1.HorseMedicalTreatmentInvoicePhoto.
 * Use `create(HorseMedicalTreatmentInvoicePhotoSchema)` to create a new message.
 */
export const HorseMedicalTreatmentInvoicePhotoSchema: GenMessage<HorseMedicalTreatmentInvoicePhoto> = /*@__PURE__*/
  messageDesc(file_horse_managements_models_horse_medical_treatment_record, 1);

/**
 * @generated from message connectrpc.management.horse_managements.v1.HorseMedicalTreatmentAffectedAreaPhoto
 */
export type HorseMedicalTreatmentAffectedAreaPhoto = Message<"connectrpc.management.horse_managements.v1.HorseMedicalTreatmentAffectedAreaPhoto"> & {
  /**
   * @generated from field: string horse_medical_treatment_affected_area_photo_path = 1;
   */
  horseMedicalTreatmentAffectedAreaPhotoPath: string;

  /**
   * @generated from field: string horse_medical_treatment_affected_area_photo_url = 2;
   */
  horseMedicalTreatmentAffectedAreaPhotoUrl: string;
};

/**
 * Describes the message connectrpc.management.horse_managements.v1.HorseMedicalTreatmentAffectedAreaPhoto.
 * Use `create(HorseMedicalTreatmentAffectedAreaPhotoSchema)` to create a new message.
 */
export const HorseMedicalTreatmentAffectedAreaPhotoSchema: GenMessage<HorseMedicalTreatmentAffectedAreaPhoto> = /*@__PURE__*/
  messageDesc(file_horse_managements_models_horse_medical_treatment_record, 2);

