// @generated by protoc-gen-es v2.2.3 with parameter "target=ts"
// @generated from file horse_managements/models/organization.proto (package connectrpc.management.horse_managements.v1, syntax proto3)
/* eslint-disable */

import type { GenFile, GenMessage } from "@bufbuild/protobuf/codegenv1";
import { fileDesc, messageDesc } from "@bufbuild/protobuf/codegenv1";
import type { Message } from "@bufbuild/protobuf";

/**
 * Describes the file horse_managements/models/organization.proto.
 */
export const file_horse_managements_models_organization: GenFile = /*@__PURE__*/
  fileDesc("Citob3JzZV9tYW5hZ2VtZW50cy9tb2RlbHMvb3JnYW5pemF0aW9uLnByb3RvEipjb25uZWN0cnBjLm1hbmFnZW1lbnQuaG9yc2VfbWFuYWdlbWVudHMudjEiNwoMT3JnYW5pemF0aW9uEhkKEW9yZ2FuaXphdGlvbl91dWlkGAEgASgJEgwKBG5hbWUYAiABKAliBnByb3RvMw");

/**
 * @generated from message connectrpc.management.horse_managements.v1.Organization
 */
export type Organization = Message<"connectrpc.management.horse_managements.v1.Organization"> & {
  /**
   * @generated from field: string organization_uuid = 1;
   */
  organizationUuid: string;

  /**
   * @generated from field: string name = 2;
   */
  name: string;
};

/**
 * Describes the message connectrpc.management.horse_managements.v1.Organization.
 * Use `create(OrganizationSchema)` to create a new message.
 */
export const OrganizationSchema: GenMessage<Organization> = /*@__PURE__*/
  messageDesc(file_horse_managements_models_organization, 0);

