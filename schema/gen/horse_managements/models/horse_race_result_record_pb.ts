// @generated by protoc-gen-es v2.2.3 with parameter "target=ts"
// @generated from file horse_managements/models/horse_race_result_record.proto (package connectrpc.management.horse_managements.v1, syntax proto3)
/* eslint-disable */

import type { GenEnum, GenFile, GenMessage } from "@bufbuild/protobuf/codegenv1";
import { enumDesc, fileDesc, messageDesc } from "@bufbuild/protobuf/codegenv1";
import type { RacePlace } from "./race_place_pb";
import { file_horse_managements_models_race_place } from "./race_place_pb";
import type { User } from "./user_pb";
import { file_horse_managements_models_user } from "./user_pb";
import type { Message } from "@bufbuild/protobuf";

/**
 * Describes the file horse_managements/models/horse_race_result_record.proto.
 */
export const file_horse_managements_models_horse_race_result_record: GenFile = /*@__PURE__*/
  fileDesc("Cjdob3JzZV9tYW5hZ2VtZW50cy9tb2RlbHMvaG9yc2VfcmFjZV9yZXN1bHRfcmVjb3JkLnByb3RvEipjb25uZWN0cnBjLm1hbmFnZW1lbnQuaG9yc2VfbWFuYWdlbWVudHMudjEivAUKFUhvcnNlUmFjZVJlc3VsdFJlY29yZBIjChtob3JzZV9yYWNlX3Jlc3VsdF9yZWNvcmRfaWQYASABKAkSDAoEeWVhchgCIAEoBRINCgVtb250aBgDIAEoBRILCgNkYXkYBCABKAUSTgoKcmFjZV9wbGFjZRgFIAEoCzI1LmNvbm5lY3RycGMubWFuYWdlbWVudC5ob3JzZV9tYW5hZ2VtZW50cy52MS5SYWNlUGxhY2VIAIgBARIYCgtyYWNlX251bWJlchgGIAEoBUgBiAEBEhYKCXJhY2VfbmFtZRgHIAEoCUgCiAEBEhUKCGRpc3RhbmNlGAggASgFSAOIAQESTgoKdHJhY2tfdHlwZRgJIAEoDjI1LmNvbm5lY3RycGMubWFuYWdlbWVudC5ob3JzZV9tYW5hZ2VtZW50cy52MS5UcmFja1R5cGVIBIgBARISCgVnb2luZxgKIAEoCUgFiAEBEhgKC2pvY2tleV9uYW1lGAsgASgJSAaIAQESEwoGd2VpZ2h0GAwgASgFSAeIAQESJAoXYmVmb3JlX3JhY2Vfd2VpZ2h0X2RpZmYYDSABKAVICIgBARIRCgRyYW5rGA4gASgFSAmIAQESSwoMY3JlYXRlZF91c2VyGA8gASgLMjAuY29ubmVjdHJwYy5tYW5hZ2VtZW50LmhvcnNlX21hbmFnZW1lbnRzLnYxLlVzZXJICogBAUINCgtfcmFjZV9wbGFjZUIOCgxfcmFjZV9udW1iZXJCDAoKX3JhY2VfbmFtZUILCglfZGlzdGFuY2VCDQoLX3RyYWNrX3R5cGVCCAoGX2dvaW5nQg4KDF9qb2NrZXlfbmFtZUIJCgdfd2VpZ2h0QhoKGF9iZWZvcmVfcmFjZV93ZWlnaHRfZGlmZkIHCgVfcmFua0IPCg1fY3JlYXRlZF91c2VyKmYKCVRyYWNrVHlwZRIaChZUUkFDS19UWVBFX1VOU1BFQ0lGSUVEEAASEwoPVFJBQ0tfVFlQRV9ESVJUEAESEwoPVFJBQ0tfVFlQRV9UVVJGEAISEwoPVFJBQ0tfVFlQRV9KVU1QEANiBnByb3RvMw", [file_horse_managements_models_race_place, file_horse_managements_models_user]);

/**
 * @generated from message connectrpc.management.horse_managements.v1.HorseRaceResultRecord
 */
export type HorseRaceResultRecord = Message<"connectrpc.management.horse_managements.v1.HorseRaceResultRecord"> & {
  /**
   * @generated from field: string horse_race_result_record_id = 1;
   */
  horseRaceResultRecordId: string;

  /**
   * @generated from field: int32 year = 2;
   */
  year: number;

  /**
   * @generated from field: int32 month = 3;
   */
  month: number;

  /**
   * @generated from field: int32 day = 4;
   */
  day: number;

  /**
   * @generated from field: optional connectrpc.management.horse_managements.v1.RacePlace race_place = 5;
   */
  racePlace?: RacePlace;

  /**
   * @generated from field: optional int32 race_number = 6;
   */
  raceNumber?: number;

  /**
   * @generated from field: optional string race_name = 7;
   */
  raceName?: string;

  /**
   * @generated from field: optional int32 distance = 8;
   */
  distance?: number;

  /**
   * @generated from field: optional connectrpc.management.horse_managements.v1.TrackType track_type = 9;
   */
  trackType?: TrackType;

  /**
   * @generated from field: optional string going = 10;
   */
  going?: string;

  /**
   * @generated from field: optional string jockey_name = 11;
   */
  jockeyName?: string;

  /**
   * @generated from field: optional int32 weight = 12;
   */
  weight?: number;

  /**
   * @generated from field: optional int32 before_race_weight_diff = 13;
   */
  beforeRaceWeightDiff?: number;

  /**
   * @generated from field: optional int32 rank = 14;
   */
  rank?: number;

  /**
   * @generated from field: optional connectrpc.management.horse_managements.v1.User created_user = 15;
   */
  createdUser?: User;
};

/**
 * Describes the message connectrpc.management.horse_managements.v1.HorseRaceResultRecord.
 * Use `create(HorseRaceResultRecordSchema)` to create a new message.
 */
export const HorseRaceResultRecordSchema: GenMessage<HorseRaceResultRecord> = /*@__PURE__*/
  messageDesc(file_horse_managements_models_horse_race_result_record, 0);

/**
 * @generated from enum connectrpc.management.horse_managements.v1.TrackType
 */
export enum TrackType {
  /**
   * @generated from enum value: TRACK_TYPE_UNSPECIFIED = 0;
   */
  UNSPECIFIED = 0,

  /**
   * @generated from enum value: TRACK_TYPE_DIRT = 1;
   */
  DIRT = 1,

  /**
   * @generated from enum value: TRACK_TYPE_TURF = 2;
   */
  TURF = 2,

  /**
   * @generated from enum value: TRACK_TYPE_JUMP = 3;
   */
  JUMP = 3,
}

/**
 * Describes the enum connectrpc.management.horse_managements.v1.TrackType.
 */
export const TrackTypeSchema: GenEnum<TrackType> = /*@__PURE__*/
  enumDesc(file_horse_managements_models_horse_race_result_record, 0);

