// @generated by protoc-gen-es v2.2.3 with parameter "target=ts"
// @generated from file horse_managements/models/horse_daily_record.proto (package connectrpc.management.horse_managements.v1, syntax proto3)
/* eslint-disable */

import type { GenFile, GenMessage } from "@bufbuild/protobuf/codegenv1";
import { fileDesc, messageDesc } from "@bufbuild/protobuf/codegenv1";
import type { Horse } from "./horse_pb";
import { file_horse_managements_models_horse } from "./horse_pb";
import type { HorseBodyRecord } from "./horse_body_record_pb";
import { file_horse_managements_models_horse_body_record } from "./horse_body_record_pb";
import type { HorseMedicalTreatmentRecord } from "./horse_medical_treatment_record_pb";
import { file_horse_managements_models_horse_medical_treatment_record } from "./horse_medical_treatment_record_pb";
import type { HorseRaceRecapRecord } from "./horse_race_recap_record_pb";
import { file_horse_managements_models_horse_race_recap_record } from "./horse_race_recap_record_pb";
import type { HorseRaceResultRecord } from "./horse_race_result_record_pb";
import { file_horse_managements_models_horse_race_result_record } from "./horse_race_result_record_pb";
import type { HorseShoeingRecord } from "./horse_shoeing_record_pb";
import { file_horse_managements_models_horse_shoeing_record } from "./horse_shoeing_record_pb";
import type { HorseTrainingRecord } from "./horse_training_record_pb";
import { file_horse_managements_models_horse_training_record } from "./horse_training_record_pb";
import type { Message } from "@bufbuild/protobuf";

/**
 * Describes the file horse_managements/models/horse_daily_record.proto.
 */
export const file_horse_managements_models_horse_daily_record: GenFile = /*@__PURE__*/
  fileDesc("CjFob3JzZV9tYW5hZ2VtZW50cy9tb2RlbHMvaG9yc2VfZGFpbHlfcmVjb3JkLnByb3RvEipjb25uZWN0cnBjLm1hbmFnZW1lbnQuaG9yc2VfbWFuYWdlbWVudHMudjEilwYKEEhvcnNlRGFpbHlSZWNvcmQSHQoVaG9yc2VfZGFpbHlfcmVjb3JkX2lkGAEgASgJEgwKBHllYXIYAiABKAUSDQoFbW9udGgYAyABKAUSCwoDZGF5GAQgASgFEkAKBWhvcnNlGAUgASgLMjEuY29ubmVjdHJwYy5tYW5hZ2VtZW50LmhvcnNlX21hbmFnZW1lbnRzLnYxLkhvcnNlElUKC2JvZHlfcmVjb3JkGAYgASgLMjsuY29ubmVjdHJwYy5tYW5hZ2VtZW50LmhvcnNlX21hbmFnZW1lbnRzLnYxLkhvcnNlQm9keVJlY29yZEgAiAEBEmIKEnJhY2VfcmVzdWx0X3JlY29yZBgHIAEoCzJBLmNvbm5lY3RycGMubWFuYWdlbWVudC5ob3JzZV9tYW5hZ2VtZW50cy52MS5Ib3JzZVJhY2VSZXN1bHRSZWNvcmRIAYgBARJgChFyYWNlX3JlY2FwX3JlY29yZBgIIAEoCzJALmNvbm5lY3RycGMubWFuYWdlbWVudC5ob3JzZV9tYW5hZ2VtZW50cy52MS5Ib3JzZVJhY2VSZWNhcFJlY29yZEgCiAEBElcKD3Nob2VpbmdfcmVjb3JkcxgJIAMoCzI+LmNvbm5lY3RycGMubWFuYWdlbWVudC5ob3JzZV9tYW5hZ2VtZW50cy52MS5Ib3JzZVNob2VpbmdSZWNvcmQSagoZbWVkaWNhbF90cmVhdG1lbnRfcmVjb3JkcxgKIAMoCzJHLmNvbm5lY3RycGMubWFuYWdlbWVudC5ob3JzZV9tYW5hZ2VtZW50cy52MS5Ib3JzZU1lZGljYWxUcmVhdG1lbnRSZWNvcmQSWQoQdHJhaW5pbmdfcmVjb3JkcxgLIAMoCzI/LmNvbm5lY3RycGMubWFuYWdlbWVudC5ob3JzZV9tYW5hZ2VtZW50cy52MS5Ib3JzZVRyYWluaW5nUmVjb3JkQg4KDF9ib2R5X3JlY29yZEIVChNfcmFjZV9yZXN1bHRfcmVjb3JkQhQKEl9yYWNlX3JlY2FwX3JlY29yZGIGcHJvdG8z", [file_horse_managements_models_horse, file_horse_managements_models_horse_body_record, file_horse_managements_models_horse_medical_treatment_record, file_horse_managements_models_horse_race_recap_record, file_horse_managements_models_horse_race_result_record, file_horse_managements_models_horse_shoeing_record, file_horse_managements_models_horse_training_record]);

/**
 * @generated from message connectrpc.management.horse_managements.v1.HorseDailyRecord
 */
export type HorseDailyRecord = Message<"connectrpc.management.horse_managements.v1.HorseDailyRecord"> & {
  /**
   * @generated from field: string horse_daily_record_id = 1;
   */
  horseDailyRecordId: string;

  /**
   * @generated from field: int32 year = 2;
   */
  year: number;

  /**
   * @generated from field: int32 month = 3;
   */
  month: number;

  /**
   * @generated from field: int32 day = 4;
   */
  day: number;

  /**
   * @generated from field: connectrpc.management.horse_managements.v1.Horse horse = 5;
   */
  horse?: Horse;

  /**
   * @generated from field: optional connectrpc.management.horse_managements.v1.HorseBodyRecord body_record = 6;
   */
  bodyRecord?: HorseBodyRecord;

  /**
   * @generated from field: optional connectrpc.management.horse_managements.v1.HorseRaceResultRecord race_result_record = 7;
   */
  raceResultRecord?: HorseRaceResultRecord;

  /**
   * @generated from field: optional connectrpc.management.horse_managements.v1.HorseRaceRecapRecord race_recap_record = 8;
   */
  raceRecapRecord?: HorseRaceRecapRecord;

  /**
   * @generated from field: repeated connectrpc.management.horse_managements.v1.HorseShoeingRecord shoeing_records = 9;
   */
  shoeingRecords: HorseShoeingRecord[];

  /**
   * @generated from field: repeated connectrpc.management.horse_managements.v1.HorseMedicalTreatmentRecord medical_treatment_records = 10;
   */
  medicalTreatmentRecords: HorseMedicalTreatmentRecord[];

  /**
   * @generated from field: repeated connectrpc.management.horse_managements.v1.HorseTrainingRecord training_records = 11;
   */
  trainingRecords: HorseTrainingRecord[];
};

/**
 * Describes the message connectrpc.management.horse_managements.v1.HorseDailyRecord.
 * Use `create(HorseDailyRecordSchema)` to create a new message.
 */
export const HorseDailyRecordSchema: GenMessage<HorseDailyRecord> = /*@__PURE__*/
  messageDesc(file_horse_managements_models_horse_daily_record, 0);

