// @generated by protoc-gen-es v2.2.3 with parameter "target=ts"
// @generated from file horse_managements/models/race_place.proto (package connectrpc.management.horse_managements.v1, syntax proto3)
/* eslint-disable */

import type { GenFile, GenMessage } from "@bufbuild/protobuf/codegenv1";
import { fileDesc, messageDesc } from "@bufbuild/protobuf/codegenv1";
import type { Message } from "@bufbuild/protobuf";

/**
 * Describes the file horse_managements/models/race_place.proto.
 */
export const file_horse_managements_models_race_place: GenFile = /*@__PURE__*/
  fileDesc("Cilob3JzZV9tYW5hZ2VtZW50cy9tb2RlbHMvcmFjZV9wbGFjZS5wcm90bxIqY29ubmVjdHJwYy5tYW5hZ2VtZW50LmhvcnNlX21hbmFnZW1lbnRzLnYxIkAKCVJhY2VQbGFjZRIVCg1yYWNlX3BsYWNlX2lkGAEgASgFEgwKBG5hbWUYAiABKAkSDgoGcmVnaW9uGAMgASgJYgZwcm90bzM");

/**
 * @generated from message connectrpc.management.horse_managements.v1.RacePlace
 */
export type RacePlace = Message<"connectrpc.management.horse_managements.v1.RacePlace"> & {
  /**
   * @generated from field: int32 race_place_id = 1;
   */
  racePlaceId: number;

  /**
   * @generated from field: string name = 2;
   */
  name: string;

  /**
   * @generated from field: string region = 3;
   */
  region: string;
};

/**
 * Describes the message connectrpc.management.horse_managements.v1.RacePlace.
 * Use `create(RacePlaceSchema)` to create a new message.
 */
export const RacePlaceSchema: GenMessage<RacePlace> = /*@__PURE__*/
  messageDesc(file_horse_managements_models_race_place, 0);

