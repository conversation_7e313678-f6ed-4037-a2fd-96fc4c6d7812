// @generated by protoc-gen-es v2.2.3 with parameter "target=ts"
// @generated from file horse_managements/models/business_trip_history.proto (package connectrpc.management.horse_managements.v1, syntax proto3)
/* eslint-disable */

import type { GenFile, GenMessage } from "@bufbuild/protobuf/codegenv1";
import { fileDesc, messageDesc } from "@bufbuild/protobuf/codegenv1";
import type { Message } from "@bufbuild/protobuf";

/**
 * Describes the file horse_managements/models/business_trip_history.proto.
 */
export const file_horse_managements_models_business_trip_history: GenFile = /*@__PURE__*/
  fileDesc("CjRob3JzZV9tYW5hZ2VtZW50cy9tb2RlbHMvYnVzaW5lc3NfdHJpcF9oaXN0b3J5LnByb3RvEipjb25uZWN0cnBjLm1hbmFnZW1lbnQuaG9yc2VfbWFuYWdlbWVudHMudjEi3QIKE0J1c2luZXNzVHJpcEhpc3RvcnkSIAoYYnVzaW5lc3NfdHJpcF9oaXN0b3J5X2lkGAEgASgJEhkKEW9yZ2FuaXphdGlvbl91dWlkGAIgASgJEhIKCnN0YWZmX25hbWUYAyABKAkSGAoQZGVzdGluYXRpb25fbmFtZRgEIAEoCRISCgpzdGFydF95ZWFyGAUgASgFEhMKC3N0YXJ0X21vbnRoGAYgASgFEhEKCXN0YXJ0X2RheRgHIAEoBRIQCghlbmRfeWVhchgIIAEoBRIRCgllbmRfbW9udGgYCSABKAUSDwoHZW5kX2RheRgKIAEoBRJUCgZob3JzZXMYCyADKAsyRC5jb25uZWN0cnBjLm1hbmFnZW1lbnQuaG9yc2VfbWFuYWdlbWVudHMudjEuQnVzaW5lc3NUcmlwSGlzdG9yeUhvcnNlEhMKC3JlcG9ydGVkX2F0GAwgASgDIkAKGEJ1c2luZXNzVHJpcEhpc3RvcnlIb3JzZRIQCghob3JzZV9pZBgBIAEoAxISCgpob3JzZV9uYW1lGAIgASgJYgZwcm90bzM");

/**
 * @generated from message connectrpc.management.horse_managements.v1.BusinessTripHistory
 */
export type BusinessTripHistory = Message<"connectrpc.management.horse_managements.v1.BusinessTripHistory"> & {
  /**
   * @generated from field: string business_trip_history_id = 1;
   */
  businessTripHistoryId: string;

  /**
   * @generated from field: string organization_uuid = 2;
   */
  organizationUuid: string;

  /**
   * @generated from field: string staff_name = 3;
   */
  staffName: string;

  /**
   * @generated from field: string destination_name = 4;
   */
  destinationName: string;

  /**
   * @generated from field: int32 start_year = 5;
   */
  startYear: number;

  /**
   * @generated from field: int32 start_month = 6;
   */
  startMonth: number;

  /**
   * @generated from field: int32 start_day = 7;
   */
  startDay: number;

  /**
   * @generated from field: int32 end_year = 8;
   */
  endYear: number;

  /**
   * @generated from field: int32 end_month = 9;
   */
  endMonth: number;

  /**
   * @generated from field: int32 end_day = 10;
   */
  endDay: number;

  /**
   * @generated from field: repeated connectrpc.management.horse_managements.v1.BusinessTripHistoryHorse horses = 11;
   */
  horses: BusinessTripHistoryHorse[];

  /**
   * @generated from field: int64 reported_at = 12;
   */
  reportedAt: bigint;
};

/**
 * Describes the message connectrpc.management.horse_managements.v1.BusinessTripHistory.
 * Use `create(BusinessTripHistorySchema)` to create a new message.
 */
export const BusinessTripHistorySchema: GenMessage<BusinessTripHistory> = /*@__PURE__*/
  messageDesc(file_horse_managements_models_business_trip_history, 0);

/**
 * @generated from message connectrpc.management.horse_managements.v1.BusinessTripHistoryHorse
 */
export type BusinessTripHistoryHorse = Message<"connectrpc.management.horse_managements.v1.BusinessTripHistoryHorse"> & {
  /**
   * @generated from field: int64 horse_id = 1;
   */
  horseId: bigint;

  /**
   * @generated from field: string horse_name = 2;
   */
  horseName: string;
};

/**
 * Describes the message connectrpc.management.horse_managements.v1.BusinessTripHistoryHorse.
 * Use `create(BusinessTripHistoryHorseSchema)` to create a new message.
 */
export const BusinessTripHistoryHorseSchema: GenMessage<BusinessTripHistoryHorse> = /*@__PURE__*/
  messageDesc(file_horse_managements_models_business_trip_history, 1);

