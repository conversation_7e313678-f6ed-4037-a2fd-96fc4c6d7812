// @generated by protoc-gen-es v2.2.3 with parameter "target=ts"
// @generated from file horse_managements/models/horse_handover_note.proto (package connectrpc.management.horse_managements.v1, syntax proto3)
/* eslint-disable */

import type { GenFile, GenMessage } from "@bufbuild/protobuf/codegenv1";
import { fileDesc, messageDesc } from "@bufbuild/protobuf/codegenv1";
import type { Message } from "@bufbuild/protobuf";

/**
 * Describes the file horse_managements/models/horse_handover_note.proto.
 */
export const file_horse_managements_models_horse_handover_note: GenFile = /*@__PURE__*/
  fileDesc("CjJob3JzZV9tYW5hZ2VtZW50cy9tb2RlbHMvaG9yc2VfaGFuZG92ZXJfbm90ZS5wcm90bxIqY29ubmVjdHJwYy5tYW5hZ2VtZW50LmhvcnNlX21hbmFnZW1lbnRzLnYxIvkBChFIb3JzZUhhbmRvdmVyTm90ZRIeChZob3JzZV9oYW5kb3Zlcl9ub3RlX2lkGAEgASgJEhAKCGhvcnNlX2lkGAIgASgDEhoKDWhhbmRvdmVyX25vdGUYAyABKAlIAIgBARIlChhuZXh0X3JhY2VfZXF1aXBtZW50X25vdGUYBCABKAlIAYgBARIWCg5sYXN0X2VkaXRlZF9hdBgFIAEoCRIYCgtmb2RkZXJfbm90ZRgGIAEoCUgCiAEBQhAKDl9oYW5kb3Zlcl9ub3RlQhsKGV9uZXh0X3JhY2VfZXF1aXBtZW50X25vdGVCDgoMX2ZvZGRlcl9ub3RlYgZwcm90bzM");

/**
 * @generated from message connectrpc.management.horse_managements.v1.HorseHandoverNote
 */
export type HorseHandoverNote = Message<"connectrpc.management.horse_managements.v1.HorseHandoverNote"> & {
  /**
   * @generated from field: string horse_handover_note_id = 1;
   */
  horseHandoverNoteId: string;

  /**
   * @generated from field: int64 horse_id = 2;
   */
  horseId: bigint;

  /**
   * @generated from field: optional string handover_note = 3;
   */
  handoverNote?: string;

  /**
   * @generated from field: optional string next_race_equipment_note = 4;
   */
  nextRaceEquipmentNote?: string;

  /**
   * @generated from field: string last_edited_at = 5;
   */
  lastEditedAt: string;

  /**
   * @generated from field: optional string fodder_note = 6;
   */
  fodderNote?: string;
};

/**
 * Describes the message connectrpc.management.horse_managements.v1.HorseHandoverNote.
 * Use `create(HorseHandoverNoteSchema)` to create a new message.
 */
export const HorseHandoverNoteSchema: GenMessage<HorseHandoverNote> = /*@__PURE__*/
  messageDesc(file_horse_managements_models_horse_handover_note, 0);

