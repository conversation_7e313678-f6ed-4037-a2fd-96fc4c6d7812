// @generated by protoc-gen-es v2.2.3 with parameter "target=ts"
// @generated from file horse_managements/models/horse_body_record.proto (package connectrpc.management.horse_managements.v1, syntax proto3)
/* eslint-disable */

import type { GenFile, GenMessage } from "@bufbuild/protobuf/codegenv1";
import { fileDesc, messageDesc } from "@bufbuild/protobuf/codegenv1";
import type { User } from "./user_pb";
import { file_horse_managements_models_user } from "./user_pb";
import type { Message } from "@bufbuild/protobuf";

/**
 * Describes the file horse_managements/models/horse_body_record.proto.
 */
export const file_horse_managements_models_horse_body_record: GenFile = /*@__PURE__*/
  fileDesc("CjBob3JzZV9tYW5hZ2VtZW50cy9tb2RlbHMvaG9yc2VfYm9keV9yZWNvcmQucHJvdG8SKmNvbm5lY3RycGMubWFuYWdlbWVudC5ob3JzZV9tYW5hZ2VtZW50cy52MSKiCAoPSG9yc2VCb2R5UmVjb3JkEhwKFGhvcnNlX2JvZHlfcmVjb3JkX2lkGAEgASgJEgwKBHllYXIYAiABKAUSDQoFbW9udGgYAyABKAUSCwoDZGF5GAQgASgFElUKEWhvcnNlX2JvZHlfcGhvdG9zGAUgAygLMjouY29ubmVjdHJwYy5tYW5hZ2VtZW50LmhvcnNlX21hbmFnZW1lbnRzLnYxLkhvcnNlQm9keVBob3RvEhgKC2JvZHlfd2VpZ2h0GAYgASgFSACIAQESIAoTYW1fYm9keV90ZW1wZXJhdHVyZRgHIAEoAUgBiAEBEiAKE3BtX2JvZHlfdGVtcGVyYXR1cmUYCCABKAFIAogBARIiChVhbV9ob3JzZV9ib2R5X2NvbW1lbnQYCSABKAlIA4gBARIiChVwbV9ob3JzZV9ib2R5X2NvbW1lbnQYCiABKAlIBIgBARIfChJhbV9ob3JzZV9ib2R5X2NhcmUYCyABKAlIBYgBARIfChJwbV9ob3JzZV9ib2R5X2NhcmUYDCABKAlIBogBARIZCgxmcmVlX2NvbW1lbnQYDSABKAlIB4gBARJyCiJhbV9ob3JzZV9ib2R5X2FmZmVjdGVkX2FyZWFfcGhvdG9zGA4gAygLMkYuY29ubmVjdHJwYy5tYW5hZ2VtZW50LmhvcnNlX21hbmFnZW1lbnRzLnYxLkhvcnNlQm9keUFmZmVjdGVkQXJlYVBob3RvEnIKInBtX2hvcnNlX2JvZHlfYWZmZWN0ZWRfYXJlYV9waG90b3MYDyADKAsyRi5jb25uZWN0cnBjLm1hbmFnZW1lbnQuaG9yc2VfbWFuYWdlbWVudHMudjEuSG9yc2VCb2R5QWZmZWN0ZWRBcmVhUGhvdG8SHwoSYmVmb3JlX2JvZHlfd2VpZ2h0GBAgASgFSAiIAQESIgoVbGFzdF9yYWNlX2JvZHlfd2VpZ2h0GBEgASgFSAmIAQESSwoMY3JlYXRlZF91c2VyGBIgASgLMjAuY29ubmVjdHJwYy5tYW5hZ2VtZW50LmhvcnNlX21hbmFnZW1lbnRzLnYxLlVzZXJICogBAUIOCgxfYm9keV93ZWlnaHRCFgoUX2FtX2JvZHlfdGVtcGVyYXR1cmVCFgoUX3BtX2JvZHlfdGVtcGVyYXR1cmVCGAoWX2FtX2hvcnNlX2JvZHlfY29tbWVudEIYChZfcG1faG9yc2VfYm9keV9jb21tZW50QhUKE19hbV9ob3JzZV9ib2R5X2NhcmVCFQoTX3BtX2hvcnNlX2JvZHlfY2FyZUIPCg1fZnJlZV9jb21tZW50QhUKE19iZWZvcmVfYm9keV93ZWlnaHRCGAoWX2xhc3RfcmFjZV9ib2R5X3dlaWdodEIPCg1fY3JlYXRlZF91c2VyIk0KDkhvcnNlQm9keVBob3RvEh0KFWhvcnNlX2JvZHlfcGhvdG9fcGF0aBgBIAEoCRIcChRob3JzZV9ib2R5X3Bob3RvX3VybBgCIAEoCSJ1ChpIb3JzZUJvZHlBZmZlY3RlZEFyZWFQaG90bxIrCiNob3JzZV9ib2R5X2FmZmVjdGVkX2FyZWFfcGhvdG9fcGF0aBgBIAEoCRIqCiJob3JzZV9ib2R5X2FmZmVjdGVkX2FyZWFfcGhvdG9fdXJsGAIgASgJYgZwcm90bzM", [file_horse_managements_models_user]);

/**
 * @generated from message connectrpc.management.horse_managements.v1.HorseBodyRecord
 */
export type HorseBodyRecord = Message<"connectrpc.management.horse_managements.v1.HorseBodyRecord"> & {
  /**
   * @generated from field: string horse_body_record_id = 1;
   */
  horseBodyRecordId: string;

  /**
   * @generated from field: int32 year = 2;
   */
  year: number;

  /**
   * @generated from field: int32 month = 3;
   */
  month: number;

  /**
   * @generated from field: int32 day = 4;
   */
  day: number;

  /**
   * @generated from field: repeated connectrpc.management.horse_managements.v1.HorseBodyPhoto horse_body_photos = 5;
   */
  horseBodyPhotos: HorseBodyPhoto[];

  /**
   * @generated from field: optional int32 body_weight = 6;
   */
  bodyWeight?: number;

  /**
   * @generated from field: optional double am_body_temperature = 7;
   */
  amBodyTemperature?: number;

  /**
   * @generated from field: optional double pm_body_temperature = 8;
   */
  pmBodyTemperature?: number;

  /**
   * @generated from field: optional string am_horse_body_comment = 9;
   */
  amHorseBodyComment?: string;

  /**
   * @generated from field: optional string pm_horse_body_comment = 10;
   */
  pmHorseBodyComment?: string;

  /**
   * @generated from field: optional string am_horse_body_care = 11;
   */
  amHorseBodyCare?: string;

  /**
   * @generated from field: optional string pm_horse_body_care = 12;
   */
  pmHorseBodyCare?: string;

  /**
   * @generated from field: optional string free_comment = 13;
   */
  freeComment?: string;

  /**
   * @generated from field: repeated connectrpc.management.horse_managements.v1.HorseBodyAffectedAreaPhoto am_horse_body_affected_area_photos = 14;
   */
  amHorseBodyAffectedAreaPhotos: HorseBodyAffectedAreaPhoto[];

  /**
   * @generated from field: repeated connectrpc.management.horse_managements.v1.HorseBodyAffectedAreaPhoto pm_horse_body_affected_area_photos = 15;
   */
  pmHorseBodyAffectedAreaPhotos: HorseBodyAffectedAreaPhoto[];

  /**
   * @generated from field: optional int32 before_body_weight = 16;
   */
  beforeBodyWeight?: number;

  /**
   * @generated from field: optional int32 last_race_body_weight = 17;
   */
  lastRaceBodyWeight?: number;

  /**
   * @generated from field: optional connectrpc.management.horse_managements.v1.User created_user = 18;
   */
  createdUser?: User;
};

/**
 * Describes the message connectrpc.management.horse_managements.v1.HorseBodyRecord.
 * Use `create(HorseBodyRecordSchema)` to create a new message.
 */
export const HorseBodyRecordSchema: GenMessage<HorseBodyRecord> = /*@__PURE__*/
  messageDesc(file_horse_managements_models_horse_body_record, 0);

/**
 * @generated from message connectrpc.management.horse_managements.v1.HorseBodyPhoto
 */
export type HorseBodyPhoto = Message<"connectrpc.management.horse_managements.v1.HorseBodyPhoto"> & {
  /**
   * @generated from field: string horse_body_photo_path = 1;
   */
  horseBodyPhotoPath: string;

  /**
   * @generated from field: string horse_body_photo_url = 2;
   */
  horseBodyPhotoUrl: string;
};

/**
 * Describes the message connectrpc.management.horse_managements.v1.HorseBodyPhoto.
 * Use `create(HorseBodyPhotoSchema)` to create a new message.
 */
export const HorseBodyPhotoSchema: GenMessage<HorseBodyPhoto> = /*@__PURE__*/
  messageDesc(file_horse_managements_models_horse_body_record, 1);

/**
 * @generated from message connectrpc.management.horse_managements.v1.HorseBodyAffectedAreaPhoto
 */
export type HorseBodyAffectedAreaPhoto = Message<"connectrpc.management.horse_managements.v1.HorseBodyAffectedAreaPhoto"> & {
  /**
   * @generated from field: string horse_body_affected_area_photo_path = 1;
   */
  horseBodyAffectedAreaPhotoPath: string;

  /**
   * @generated from field: string horse_body_affected_area_photo_url = 2;
   */
  horseBodyAffectedAreaPhotoUrl: string;
};

/**
 * Describes the message connectrpc.management.horse_managements.v1.HorseBodyAffectedAreaPhoto.
 * Use `create(HorseBodyAffectedAreaPhotoSchema)` to create a new message.
 */
export const HorseBodyAffectedAreaPhotoSchema: GenMessage<HorseBodyAffectedAreaPhoto> = /*@__PURE__*/
  messageDesc(file_horse_managements_models_horse_body_record, 2);

