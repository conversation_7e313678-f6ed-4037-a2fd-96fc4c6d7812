// @generated by protoc-gen-es v2.2.3 with parameter "target=ts"
// @generated from file horse_managements/models/facility.proto (package connectrpc.management.horse_managements.v1, syntax proto3)
/* eslint-disable */

import type { GenFile, GenMessage } from "@bufbuild/protobuf/codegenv1";
import { fileDesc, messageDesc } from "@bufbuild/protobuf/codegenv1";
import type { Message } from "@bufbuild/protobuf";

/**
 * Describes the file horse_managements/models/facility.proto.
 */
export const file_horse_managements_models_facility: GenFile = /*@__PURE__*/
  fileDesc("Cidob3JzZV9tYW5hZ2VtZW50cy9tb2RlbHMvZmFjaWxpdHkucHJvdG8SKmNvbm5lY3RycGMubWFuYWdlbWVudC5ob3JzZV9tYW5hZ2VtZW50cy52MSItCghGYWNpbGl0eRITCgtmYWNpbGl0eV9pZBgBIAEoCRIMCgRuYW1lGAIgASgJYgZwcm90bzM");

/**
 * @generated from message connectrpc.management.horse_managements.v1.Facility
 */
export type Facility = Message<"connectrpc.management.horse_managements.v1.Facility"> & {
  /**
   * @generated from field: string facility_id = 1;
   */
  facilityId: string;

  /**
   * @generated from field: string name = 2;
   */
  name: string;
};

/**
 * Describes the message connectrpc.management.horse_managements.v1.Facility.
 * Use `create(FacilitySchema)` to create a new message.
 */
export const FacilitySchema: GenMessage<Facility> = /*@__PURE__*/
  messageDesc(file_horse_managements_models_facility, 0);

