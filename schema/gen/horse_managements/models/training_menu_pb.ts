// @generated by protoc-gen-es v2.2.3 with parameter "target=ts"
// @generated from file horse_managements/models/training_menu.proto (package connectrpc.management.horse_managements.v1, syntax proto3)
/* eslint-disable */

import type { GenFile, GenMessage } from "@bufbuild/protobuf/codegenv1";
import { fileDesc, messageDesc } from "@bufbuild/protobuf/codegenv1";
import type { Message } from "@bufbuild/protobuf";

/**
 * Describes the file horse_managements/models/training_menu.proto.
 */
export const file_horse_managements_models_training_menu: GenFile = /*@__PURE__*/
  fileDesc("Cixob3JzZV9tYW5hZ2VtZW50cy9tb2RlbHMvdHJhaW5pbmdfbWVudS5wcm90bxIqY29ubmVjdHJwYy5tYW5hZ2VtZW50LmhvcnNlX21hbmFnZW1lbnRzLnYxIl0KDFRyYWluaW5nTWVudRIaChJ0cmFpbmluZ19tZW51X3V1aWQYASABKAkSGgoSdHJhaW5pbmdfbWVudV9uYW1lGAIgASgJEhUKDXRyYWluaW5nX3R5cGUYAyABKAliBnByb3RvMw");

/**
 * @generated from message connectrpc.management.horse_managements.v1.TrainingMenu
 */
export type TrainingMenu = Message<"connectrpc.management.horse_managements.v1.TrainingMenu"> & {
  /**
   * @generated from field: string training_menu_uuid = 1;
   */
  trainingMenuUuid: string;

  /**
   * @generated from field: string training_menu_name = 2;
   */
  trainingMenuName: string;

  /**
   * @generated from field: string training_type = 3;
   */
  trainingType: string;
};

/**
 * Describes the message connectrpc.management.horse_managements.v1.TrainingMenu.
 * Use `create(TrainingMenuSchema)` to create a new message.
 */
export const TrainingMenuSchema: GenMessage<TrainingMenu> = /*@__PURE__*/
  messageDesc(file_horse_managements_models_training_menu, 0);

