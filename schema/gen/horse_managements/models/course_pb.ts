// @generated by protoc-gen-es v2.2.3 with parameter "target=ts"
// @generated from file horse_managements/models/course.proto (package connectrpc.management.horse_managements.v1, syntax proto3)
/* eslint-disable */

import type { GenFile, GenMessage } from "@bufbuild/protobuf/codegenv1";
import { fileDesc, messageDesc } from "@bufbuild/protobuf/codegenv1";
import type { Message } from "@bufbuild/protobuf";

/**
 * Describes the file horse_managements/models/course.proto.
 */
export const file_horse_managements_models_course: GenFile = /*@__PURE__*/
  fileDesc("CiVob3JzZV9tYW5hZ2VtZW50cy9tb2RlbHMvY291cnNlLnByb3RvEipjb25uZWN0cnBjLm1hbmFnZW1lbnQuaG9yc2VfbWFuYWdlbWVudHMudjEiKQoGQ291cnNlEhEKCWNvdXJzZV9pZBgBIAEoCRIMCgRuYW1lGAIgASgJYgZwcm90bzM");

/**
 * @generated from message connectrpc.management.horse_managements.v1.Course
 */
export type Course = Message<"connectrpc.management.horse_managements.v1.Course"> & {
  /**
   * @generated from field: string course_id = 1;
   */
  courseId: string;

  /**
   * @generated from field: string name = 2;
   */
  name: string;
};

/**
 * Describes the message connectrpc.management.horse_managements.v1.Course.
 * Use `create(CourseSchema)` to create a new message.
 */
export const CourseSchema: GenMessage<Course> = /*@__PURE__*/
  messageDesc(file_horse_managements_models_course, 0);

