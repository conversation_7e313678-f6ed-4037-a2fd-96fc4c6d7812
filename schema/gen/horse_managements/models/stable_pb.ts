// @generated by protoc-gen-es v2.2.3 with parameter "target=ts"
// @generated from file horse_managements/models/stable.proto (package connectrpc.management.horse_managements.v1, syntax proto3)
/* eslint-disable */

import type { GenFile, GenMessage } from "@bufbuild/protobuf/codegenv1";
import { fileDesc, messageDesc } from "@bufbuild/protobuf/codegenv1";
import type { Message } from "@bufbuild/protobuf";

/**
 * Describes the file horse_managements/models/stable.proto.
 */
export const file_horse_managements_models_stable: GenFile = /*@__PURE__*/
  fileDesc("CiVob3JzZV9tYW5hZ2VtZW50cy9tb2RlbHMvc3RhYmxlLnByb3RvEipjb25uZWN0cnBjLm1hbmFnZW1lbnQuaG9yc2VfbWFuYWdlbWVudHMudjEiKwoGU3RhYmxlEhMKC3N0YWJsZV91dWlkGAEgASgJEgwKBG5hbWUYAiABKAliBnByb3RvMw");

/**
 * @generated from message connectrpc.management.horse_managements.v1.Stable
 */
export type Stable = Message<"connectrpc.management.horse_managements.v1.Stable"> & {
  /**
   * @generated from field: string stable_uuid = 1;
   */
  stableUuid: string;

  /**
   * @generated from field: string name = 2;
   */
  name: string;
};

/**
 * Describes the message connectrpc.management.horse_managements.v1.Stable.
 * Use `create(StableSchema)` to create a new message.
 */
export const StableSchema: GenMessage<Stable> = /*@__PURE__*/
  messageDesc(file_horse_managements_models_stable, 0);

