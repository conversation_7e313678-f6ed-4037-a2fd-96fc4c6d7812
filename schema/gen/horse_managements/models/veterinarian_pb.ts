// @generated by protoc-gen-es v2.2.3 with parameter "target=ts"
// @generated from file horse_managements/models/veterinarian.proto (package connectrpc.management.horse_managements.v1, syntax proto3)
/* eslint-disable */

import type { GenFile, GenMessage } from "@bufbuild/protobuf/codegenv1";
import { fileDesc, messageDesc } from "@bufbuild/protobuf/codegenv1";
import type { Message } from "@bufbuild/protobuf";

/**
 * Describes the file horse_managements/models/veterinarian.proto.
 */
export const file_horse_managements_models_veterinarian: GenFile = /*@__PURE__*/
  fileDesc("Citob3JzZV9tYW5hZ2VtZW50cy9tb2RlbHMvdmV0ZXJpbmFyaWFuLnByb3RvEipjb25uZWN0cnBjLm1hbmFnZW1lbnQuaG9yc2VfbWFuYWdlbWVudHMudjEiQgoMVmV0ZXJpbmFyaWFuEhcKD3ZldGVyaW5hcmlhbl9pZBgBIAEoCRIZChF2ZXRlcmluYXJpYW5fbmFtZRgCIAEoCWIGcHJvdG8z");

/**
 * @generated from message connectrpc.management.horse_managements.v1.Veterinarian
 */
export type Veterinarian = Message<"connectrpc.management.horse_managements.v1.Veterinarian"> & {
  /**
   * @generated from field: string veterinarian_id = 1;
   */
  veterinarianId: string;

  /**
   * @generated from field: string veterinarian_name = 2;
   */
  veterinarianName: string;
};

/**
 * Describes the message connectrpc.management.horse_managements.v1.Veterinarian.
 * Use `create(VeterinarianSchema)` to create a new message.
 */
export const VeterinarianSchema: GenMessage<Veterinarian> = /*@__PURE__*/
  messageDesc(file_horse_managements_models_veterinarian, 0);

