// @generated by protoc-gen-es v2.2.3 with parameter "target=ts"
// @generated from file horse_managements/horse_training_record_service.proto (package connectrpc.management.horse_managements.v1, syntax proto3)
/* eslint-disable */

import type { GenFile, GenMessage, GenService } from "@bufbuild/protobuf/codegenv1";
import { fileDesc, messageDesc, serviceDesc } from "@bufbuild/protobuf/codegenv1";
import type { FurlongTime, HorseTrainingRecord, PoolTrainingType, TrainingPartner } from "./models/horse_training_record_pb";
import { file_horse_managements_models_horse_training_record } from "./models/horse_training_record_pb";
import type { Message } from "@bufbuild/protobuf";

/**
 * Describes the file horse_managements/horse_training_record_service.proto.
 */
export const file_horse_managements_horse_training_record_service: GenFile = /*@__PURE__*/
  fileDesc("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", [file_horse_managements_models_horse_training_record]);

/**
 * @generated from message connectrpc.management.horse_managements.v1.ListHorseTrainingRecordsRequest
 */
export type ListHorseTrainingRecordsRequest = Message<"connectrpc.management.horse_managements.v1.ListHorseTrainingRecordsRequest"> & {
  /**
   * @generated from field: int64 horse_id = 1 [deprecated = true];
   * @deprecated
   */
  horseId: bigint;

  /**
   * @generated from field: optional int32 skip = 2;
   */
  skip?: number;

  /**
   * @generated from field: optional int64 optional_horse_id = 3;
   */
  optionalHorseId?: bigint;
};

/**
 * Describes the message connectrpc.management.horse_managements.v1.ListHorseTrainingRecordsRequest.
 * Use `create(ListHorseTrainingRecordsRequestSchema)` to create a new message.
 */
export const ListHorseTrainingRecordsRequestSchema: GenMessage<ListHorseTrainingRecordsRequest> = /*@__PURE__*/
  messageDesc(file_horse_managements_horse_training_record_service, 0);

/**
 * @generated from message connectrpc.management.horse_managements.v1.ListHorseTrainingRecordsResponse
 */
export type ListHorseTrainingRecordsResponse = Message<"connectrpc.management.horse_managements.v1.ListHorseTrainingRecordsResponse"> & {
  /**
   * @generated from field: repeated connectrpc.management.horse_managements.v1.HorseTrainingRecord horse_training_records = 1;
   */
  horseTrainingRecords: HorseTrainingRecord[];
};

/**
 * Describes the message connectrpc.management.horse_managements.v1.ListHorseTrainingRecordsResponse.
 * Use `create(ListHorseTrainingRecordsResponseSchema)` to create a new message.
 */
export const ListHorseTrainingRecordsResponseSchema: GenMessage<ListHorseTrainingRecordsResponse> = /*@__PURE__*/
  messageDesc(file_horse_managements_horse_training_record_service, 1);

/**
 * @generated from message connectrpc.management.horse_managements.v1.GetHorseTrainingRecordRequest
 */
export type GetHorseTrainingRecordRequest = Message<"connectrpc.management.horse_managements.v1.GetHorseTrainingRecordRequest"> & {
  /**
   * @generated from field: string training_record_uuid = 1;
   */
  trainingRecordUuid: string;
};

/**
 * Describes the message connectrpc.management.horse_managements.v1.GetHorseTrainingRecordRequest.
 * Use `create(GetHorseTrainingRecordRequestSchema)` to create a new message.
 */
export const GetHorseTrainingRecordRequestSchema: GenMessage<GetHorseTrainingRecordRequest> = /*@__PURE__*/
  messageDesc(file_horse_managements_horse_training_record_service, 2);

/**
 * @generated from message connectrpc.management.horse_managements.v1.GetHorseTrainingRecordResponse
 */
export type GetHorseTrainingRecordResponse = Message<"connectrpc.management.horse_managements.v1.GetHorseTrainingRecordResponse"> & {
  /**
   * @generated from field: connectrpc.management.horse_managements.v1.HorseTrainingRecord horse_training_record = 1;
   */
  horseTrainingRecord?: HorseTrainingRecord;
};

/**
 * Describes the message connectrpc.management.horse_managements.v1.GetHorseTrainingRecordResponse.
 * Use `create(GetHorseTrainingRecordResponseSchema)` to create a new message.
 */
export const GetHorseTrainingRecordResponseSchema: GenMessage<GetHorseTrainingRecordResponse> = /*@__PURE__*/
  messageDesc(file_horse_managements_horse_training_record_service, 3);

/**
 * @generated from message connectrpc.management.horse_managements.v1.PostHorseTrainingRecordRequest
 */
export type PostHorseTrainingRecordRequest = Message<"connectrpc.management.horse_managements.v1.PostHorseTrainingRecordRequest"> & {
  /**
   * @generated from field: int64 horse_id = 1;
   */
  horseId: bigint;

  /**
   * @generated from field: int32 year = 2;
   */
  year: number;

  /**
   * @generated from field: int32 month = 3;
   */
  month: number;

  /**
   * @generated from field: int32 day = 4;
   */
  day: number;

  /**
   * @generated from field: bool is_gait_abnormal = 5;
   */
  isGaitAbnormal: boolean;

  /**
   * @generated from field: optional string gait_abnormal_description = 6;
   */
  gaitAbnormalDescription?: string;

  /**
   * @generated from field: optional string training_type = 7;
   */
  trainingType?: string;

  /**
   * @generated from field: optional string rider_uuid = 8;
   */
  riderUuid?: string;

  /**
   * @generated from field: optional string training_menu_uuid = 9;
   */
  trainingMenuUuid?: string;

  /**
   * @generated from field: optional string facility_id = 10;
   */
  facilityId?: string;

  /**
   * @generated from field: optional string facility_name = 11;
   */
  facilityName?: string;

  /**
   * @generated from field: optional string course_id = 12;
   */
  courseId?: string;

  /**
   * @generated from field: optional string course_name = 13;
   */
  courseName?: string;

  /**
   * @generated from field: optional string course_going = 14;
   */
  courseGoing?: string;

  /**
   * @generated from field: repeated connectrpc.management.horse_managements.v1.FurlongTime furlong_times = 15;
   */
  furlongTimes: FurlongTime[];

  /**
   * @generated from field: optional string furlong_time_position = 16;
   */
  furlongTimePosition?: string;

  /**
   * @generated from field: optional double lactate_level = 17;
   */
  lactateLevel?: number;

  /**
   * @generated from field: optional string training_comment = 18;
   */
  trainingComment?: string;

  /**
   * @generated from field: repeated connectrpc.management.horse_managements.v1.TrainingPartner training_partners = 19;
   */
  trainingPartners: TrainingPartner[];

  /**
   * @generated from field: optional string gate_training_type = 20;
   */
  gateTrainingType?: string;

  /**
   * @generated from field: optional connectrpc.management.horse_managements.v1.PoolTrainingType pool_training_type = 21;
   */
  poolTrainingType?: PoolTrainingType;
};

/**
 * Describes the message connectrpc.management.horse_managements.v1.PostHorseTrainingRecordRequest.
 * Use `create(PostHorseTrainingRecordRequestSchema)` to create a new message.
 */
export const PostHorseTrainingRecordRequestSchema: GenMessage<PostHorseTrainingRecordRequest> = /*@__PURE__*/
  messageDesc(file_horse_managements_horse_training_record_service, 4);

/**
 * @generated from message connectrpc.management.horse_managements.v1.PostHorseTrainingRecordResponse
 */
export type PostHorseTrainingRecordResponse = Message<"connectrpc.management.horse_managements.v1.PostHorseTrainingRecordResponse"> & {
  /**
   * @generated from field: string horse_training_record_id = 1;
   */
  horseTrainingRecordId: string;
};

/**
 * Describes the message connectrpc.management.horse_managements.v1.PostHorseTrainingRecordResponse.
 * Use `create(PostHorseTrainingRecordResponseSchema)` to create a new message.
 */
export const PostHorseTrainingRecordResponseSchema: GenMessage<PostHorseTrainingRecordResponse> = /*@__PURE__*/
  messageDesc(file_horse_managements_horse_training_record_service, 5);

/**
 * @generated from message connectrpc.management.horse_managements.v1.UpdateHorseTrainingRecordRequest
 */
export type UpdateHorseTrainingRecordRequest = Message<"connectrpc.management.horse_managements.v1.UpdateHorseTrainingRecordRequest"> & {
  /**
   * @generated from field: string horse_training_record_id = 1;
   */
  horseTrainingRecordId: string;

  /**
   * @generated from field: bool is_gait_abnormal = 2;
   */
  isGaitAbnormal: boolean;

  /**
   * @generated from field: optional string gait_abnormal_description = 3;
   */
  gaitAbnormalDescription?: string;

  /**
   * @generated from field: optional string training_type = 4;
   */
  trainingType?: string;

  /**
   * @generated from field: optional string rider_uuid = 5;
   */
  riderUuid?: string;

  /**
   * @generated from field: optional string training_menu_uuid = 6;
   */
  trainingMenuUuid?: string;

  /**
   * @generated from field: optional string facility_id = 7;
   */
  facilityId?: string;

  /**
   * @generated from field: optional string facility_name = 8;
   */
  facilityName?: string;

  /**
   * @generated from field: optional string course_id = 9;
   */
  courseId?: string;

  /**
   * @generated from field: optional string course_name = 10;
   */
  courseName?: string;

  /**
   * @generated from field: optional string course_going = 11;
   */
  courseGoing?: string;

  /**
   * @generated from field: repeated connectrpc.management.horse_managements.v1.FurlongTime furlong_times = 12;
   */
  furlongTimes: FurlongTime[];

  /**
   * @generated from field: optional string furlong_time_position = 13;
   */
  furlongTimePosition?: string;

  /**
   * @generated from field: optional double lactate_level = 14;
   */
  lactateLevel?: number;

  /**
   * @generated from field: optional string training_comment = 15;
   */
  trainingComment?: string;

  /**
   * @generated from field: repeated connectrpc.management.horse_managements.v1.TrainingPartner training_partners = 16;
   */
  trainingPartners: TrainingPartner[];

  /**
   * @generated from field: optional string gate_training_type = 17;
   */
  gateTrainingType?: string;

  /**
   * @generated from field: optional int64 horse_id = 18;
   */
  horseId?: bigint;

  /**
   * @generated from field: optional int32 year = 19;
   */
  year?: number;

  /**
   * @generated from field: optional int32 month = 20;
   */
  month?: number;

  /**
   * @generated from field: optional int32 day = 21;
   */
  day?: number;

  /**
   * @generated from field: optional connectrpc.management.horse_managements.v1.PoolTrainingType pool_training_type = 22;
   */
  poolTrainingType?: PoolTrainingType;
};

/**
 * Describes the message connectrpc.management.horse_managements.v1.UpdateHorseTrainingRecordRequest.
 * Use `create(UpdateHorseTrainingRecordRequestSchema)` to create a new message.
 */
export const UpdateHorseTrainingRecordRequestSchema: GenMessage<UpdateHorseTrainingRecordRequest> = /*@__PURE__*/
  messageDesc(file_horse_managements_horse_training_record_service, 6);

/**
 * @generated from message connectrpc.management.horse_managements.v1.UpdateHorseTrainingRecordResponse
 */
export type UpdateHorseTrainingRecordResponse = Message<"connectrpc.management.horse_managements.v1.UpdateHorseTrainingRecordResponse"> & {
  /**
   * @generated from field: string training_record_uuid = 1;
   */
  trainingRecordUuid: string;
};

/**
 * Describes the message connectrpc.management.horse_managements.v1.UpdateHorseTrainingRecordResponse.
 * Use `create(UpdateHorseTrainingRecordResponseSchema)` to create a new message.
 */
export const UpdateHorseTrainingRecordResponseSchema: GenMessage<UpdateHorseTrainingRecordResponse> = /*@__PURE__*/
  messageDesc(file_horse_managements_horse_training_record_service, 7);

/**
 * @generated from message connectrpc.management.horse_managements.v1.DeleteHorseTrainingRecordRequest
 */
export type DeleteHorseTrainingRecordRequest = Message<"connectrpc.management.horse_managements.v1.DeleteHorseTrainingRecordRequest"> & {
  /**
   * @generated from field: string training_record_uuid = 1;
   */
  trainingRecordUuid: string;
};

/**
 * Describes the message connectrpc.management.horse_managements.v1.DeleteHorseTrainingRecordRequest.
 * Use `create(DeleteHorseTrainingRecordRequestSchema)` to create a new message.
 */
export const DeleteHorseTrainingRecordRequestSchema: GenMessage<DeleteHorseTrainingRecordRequest> = /*@__PURE__*/
  messageDesc(file_horse_managements_horse_training_record_service, 8);

/**
 * @generated from message connectrpc.management.horse_managements.v1.DeleteHorseTrainingRecordResponse
 */
export type DeleteHorseTrainingRecordResponse = Message<"connectrpc.management.horse_managements.v1.DeleteHorseTrainingRecordResponse"> & {
};

/**
 * Describes the message connectrpc.management.horse_managements.v1.DeleteHorseTrainingRecordResponse.
 * Use `create(DeleteHorseTrainingRecordResponseSchema)` to create a new message.
 */
export const DeleteHorseTrainingRecordResponseSchema: GenMessage<DeleteHorseTrainingRecordResponse> = /*@__PURE__*/
  messageDesc(file_horse_managements_horse_training_record_service, 9);

/**
 * @generated from service connectrpc.management.horse_managements.v1.HorseTrainingRecordService
 */
export const HorseTrainingRecordService: GenService<{
  /**
   * @generated from rpc connectrpc.management.horse_managements.v1.HorseTrainingRecordService.ListHorseTrainingRecords
   */
  listHorseTrainingRecords: {
    methodKind: "unary";
    input: typeof ListHorseTrainingRecordsRequestSchema;
    output: typeof ListHorseTrainingRecordsResponseSchema;
  },
  /**
   * @generated from rpc connectrpc.management.horse_managements.v1.HorseTrainingRecordService.GetHorseTrainingRecord
   */
  getHorseTrainingRecord: {
    methodKind: "unary";
    input: typeof GetHorseTrainingRecordRequestSchema;
    output: typeof GetHorseTrainingRecordResponseSchema;
  },
  /**
   * @generated from rpc connectrpc.management.horse_managements.v1.HorseTrainingRecordService.PostHorseTrainingRecord
   */
  postHorseTrainingRecord: {
    methodKind: "unary";
    input: typeof PostHorseTrainingRecordRequestSchema;
    output: typeof PostHorseTrainingRecordResponseSchema;
  },
  /**
   * @generated from rpc connectrpc.management.horse_managements.v1.HorseTrainingRecordService.UpdateHorseTrainingRecord
   */
  updateHorseTrainingRecord: {
    methodKind: "unary";
    input: typeof UpdateHorseTrainingRecordRequestSchema;
    output: typeof UpdateHorseTrainingRecordResponseSchema;
  },
  /**
   * @generated from rpc connectrpc.management.horse_managements.v1.HorseTrainingRecordService.DeleteHorseTrainingRecord
   */
  deleteHorseTrainingRecord: {
    methodKind: "unary";
    input: typeof DeleteHorseTrainingRecordRequestSchema;
    output: typeof DeleteHorseTrainingRecordResponseSchema;
  },
}> = /*@__PURE__*/
  serviceDesc(file_horse_managements_horse_training_record_service, 0);

