// @generated by protoc-gen-es v2.2.3 with parameter "target=ts"
// @generated from file horse_managements/master_horse_service.proto (package connectrpc.management.horse_managements.v1, syntax proto3)
/* eslint-disable */

import type { GenFile, GenMessage, GenService } from "@bufbuild/protobuf/codegenv1";
import { fileDesc, messageDesc, serviceDesc } from "@bufbuild/protobuf/codegenv1";
import type { MasterHorse } from "./models/master_horse_pb";
import { file_horse_managements_models_master_horse } from "./models/master_horse_pb";
import type { Message } from "@bufbuild/protobuf";

/**
 * Describes the file horse_managements/master_horse_service.proto.
 */
export const file_horse_managements_master_horse_service: GenFile = /*@__PURE__*/
  fileDesc("Cixob3JzZV9tYW5hZ2VtZW50cy9tYXN0ZXJfaG9yc2Vfc2VydmljZS5wcm90bxIqY29ubmVjdHJwYy5tYW5hZ2VtZW50LmhvcnNlX21hbmFnZW1lbnRzLnYxIjcKGVNlYXJjaE1hc3RlckhvcnNlc1JlcXVlc3QSEQoEbmFtZRgBIAEoCUgAiAEBQgcKBV9uYW1lImwKGlNlYXJjaE1hc3RlckhvcnNlc1Jlc3BvbnNlEk4KDW1hc3Rlcl9ob3JzZXMYASADKAsyNy5jb25uZWN0cnBjLm1hbmFnZW1lbnQuaG9yc2VfbWFuYWdlbWVudHMudjEuTWFzdGVySG9yc2UyugEKEk1hc3RlckhvcnNlU2VydmljZRKjAQoSU2VhcmNoTWFzdGVySG9yc2VzEkUuY29ubmVjdHJwYy5tYW5hZ2VtZW50LmhvcnNlX21hbmFnZW1lbnRzLnYxLlNlYXJjaE1hc3RlckhvcnNlc1JlcXVlc3QaRi5jb25uZWN0cnBjLm1hbmFnZW1lbnQuaG9yc2VfbWFuYWdlbWVudHMudjEuU2VhcmNoTWFzdGVySG9yc2VzUmVzcG9uc2ViBnByb3RvMw", [file_horse_managements_models_master_horse]);

/**
 * @generated from message connectrpc.management.horse_managements.v1.SearchMasterHorsesRequest
 */
export type SearchMasterHorsesRequest = Message<"connectrpc.management.horse_managements.v1.SearchMasterHorsesRequest"> & {
  /**
   * @generated from field: optional string name = 1;
   */
  name?: string;
};

/**
 * Describes the message connectrpc.management.horse_managements.v1.SearchMasterHorsesRequest.
 * Use `create(SearchMasterHorsesRequestSchema)` to create a new message.
 */
export const SearchMasterHorsesRequestSchema: GenMessage<SearchMasterHorsesRequest> = /*@__PURE__*/
  messageDesc(file_horse_managements_master_horse_service, 0);

/**
 * @generated from message connectrpc.management.horse_managements.v1.SearchMasterHorsesResponse
 */
export type SearchMasterHorsesResponse = Message<"connectrpc.management.horse_managements.v1.SearchMasterHorsesResponse"> & {
  /**
   * @generated from field: repeated connectrpc.management.horse_managements.v1.MasterHorse master_horses = 1;
   */
  masterHorses: MasterHorse[];
};

/**
 * Describes the message connectrpc.management.horse_managements.v1.SearchMasterHorsesResponse.
 * Use `create(SearchMasterHorsesResponseSchema)` to create a new message.
 */
export const SearchMasterHorsesResponseSchema: GenMessage<SearchMasterHorsesResponse> = /*@__PURE__*/
  messageDesc(file_horse_managements_master_horse_service, 1);

/**
 * @generated from service connectrpc.management.horse_managements.v1.MasterHorseService
 */
export const MasterHorseService: GenService<{
  /**
   * @generated from rpc connectrpc.management.horse_managements.v1.MasterHorseService.SearchMasterHorses
   */
  searchMasterHorses: {
    methodKind: "unary";
    input: typeof SearchMasterHorsesRequestSchema;
    output: typeof SearchMasterHorsesResponseSchema;
  },
}> = /*@__PURE__*/
  serviceDesc(file_horse_managements_master_horse_service, 0);

