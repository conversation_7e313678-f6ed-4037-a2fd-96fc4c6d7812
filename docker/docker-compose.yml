version: "3.8"
services:
  management-api:
    build:
      context: ../
      dockerfile: ./docker/api/Dockerfile
    working_dir: /var/equtum-management-api
    volumes:
      - ../:/var/equtum-management-api
      - api_node_modules:/var/equtum-management-api/node_modules
    environment:
      NODE_ENV: local
      DATABASE_URL: mysql://root@management-db:3306/equtum_test
      FIREBASE_AUTH_EMULATOR_HOST: management_firebase_emulator:9099
    tty: true
    command: sleep infinity
    depends_on:
      - management-db
      - management_firebase_emulator
  management-db:
    image: mysql:9.4.0
    volumes:
      - ./mysql/init:/docker-entrypoint-initdb.d
    environment:
      - MYSQL_ALLOW_EMPTY_PASSWORD=1
    ports:
      - "3306:3306"
    healthcheck:
      test: ["CMD", "mysqladmin", "ping", "-h", "localhost"]
      interval: 10s
      timeout: 5s
      retries: 5
  management_firebase_emulator:
    build:
      context: .
      dockerfile: ./firebase-emulator/Dockerfile
    environment:
      PROJECT_ID: equtum-local
    ports:
      - "9099:9099"
    volumes:
      - ./firebase-emulator:/workspace:cached

volumes:
  api_node_modules:
    driver: local
