FROM node:22.18.0

ENV APP_ROOT /var/equtum-management-api/
WORKDIR $APP_ROOT

RUN apt-get update && apt-get install -y wget \
  gnupg \
  default-mysql-client \
  apt-transport-https

RUN wget https://github.com/golang-migrate/migrate/releases/download/v4.17.1/migrate.linux-arm64.deb

RUN dpkg -i migrate.linux-arm64.deb

COPY package.json package-lock.json $APP_ROOT/
RUN npm install

COPY . $APP_ROOT/

CMD ['NODE_OPTIONS="--import ./src/services/instrument.mjs"', "npm", "run", "start"]
