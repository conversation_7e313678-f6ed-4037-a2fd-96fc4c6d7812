FROM node:22.18.0-alpine

ARG VERSION=13.12.0
ARG PROJECT_ID

ENV HOME=/workspace

# Emulator Suite UI
EXPOSE 4000
# Firebase Authentication
EXPOSE 9099

RUN apk --no-cache add openjdk17-jdk bash && \
    npm i -g firebase-tools@$VERSION && \
    npm cache clean --force && \
    firebase -V && \
    java -version

WORKDIR $HOME

ENTRYPOINT firebase emulators:start \
    --project "$PROJECT_ID" \
    --import=./export
