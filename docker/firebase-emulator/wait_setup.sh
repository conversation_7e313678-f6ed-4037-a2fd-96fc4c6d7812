#!/bin/bash

URL="http://0.0.0.0:4000/auth"
MAX_TRIES=10
SLEEP_TIME=5

count=0

while [ $count -lt $MAX_TRIES ]; do
  curl -s $URL > /dev/null

  if [ $? -eq 0 ]; then
    echo "Server is up and running!"
    exit 0
  else
    echo "Server is not yet available. Retrying in $SLEEP_TIME seconds..."
    count=$((count+1))
    sleep $SLEEP_TIME
  fi
done

echo "Server did not start after $MAX_TRIES attempts. Exiting."
exit 1
