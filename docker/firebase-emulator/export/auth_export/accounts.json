{"kind": "identitytoolkit#DownloadAccountResponse", "users": [{"localId": "firebaseUidForUsers1", "displayName": "EQUTUM firebaseUidForUsers1", "email": "<EMAIL>", "emailVerified": true, "passwordHash": "fakeHash:salt=fakeSalt8qs7VygvbbBagihMHKql:password=abel1234", "salt": "fakeSalt8qs7VygvbbBagihMHKql", "passwordUpdatedAt": **********784, "providerUserInfo": [{"providerId": "password", "email": "<EMAIL>", "federatedId": "<EMAIL>", "rawId": "<EMAIL>", "displayName": "EQUTUM firebaseUidForUsers1"}], "disabled": false, "createdAt": "*************", "lastLoginAt": "*************", "validSince": "**********"}, {"localId": "firebaseUidForUsers2", "displayName": "EQUTUM firebaseUidForUsers2", "email": "<EMAIL>", "emailVerified": true, "passwordHash": "fakeHash:salt=fakeSalt8qs7VygvbbBagihMHKql:password=abel1234", "salt": "fakeSalt8qs7VygvbbBagihMHKql", "passwordUpdatedAt": **********784, "providerUserInfo": [{"providerId": "password", "email": "<EMAIL>", "federatedId": "<EMAIL>", "rawId": "<EMAIL>", "displayName": "EQUTUM firebaseUidForUsers2"}], "disabled": false, "createdAt": "*************", "lastLoginAt": "*************", "validSince": "**********"}, {"localId": "firebaseUidForUsers3", "displayName": "EQUTUM firebaseUidForUsers3", "email": "<EMAIL>", "emailVerified": true, "passwordHash": "fakeHash:salt=fakeSalt8qs7VygvbbBagihMHKql:password=abel1234", "salt": "fakeSalt8qs7VygvbbBagihMHKql", "passwordUpdatedAt": **********784, "providerUserInfo": [{"providerId": "password", "email": "<EMAIL>", "federatedId": "<EMAIL>", "rawId": "<EMAIL>", "displayName": "EQUTUM firebaseUidForUsers3"}], "disabled": false, "createdAt": "*************", "lastLoginAt": "*************", "validSince": "**********"}, {"localId": "firebaseUidForUsers4", "displayName": "EQUTUM firebaseUidForUsers4", "email": "<EMAIL>", "emailVerified": true, "passwordHash": "fakeHash:salt=fakeSalt8qs7VygvbbBagihMHKql:password=abel1234", "salt": "fakeSalt8qs7VygvbbBagihMHKql", "passwordUpdatedAt": **********784, "providerUserInfo": [{"providerId": "password", "email": "<EMAIL>", "federatedId": "<EMAIL>", "rawId": "<EMAIL>", "displayName": "EQUTUM firebaseUidForUsers4"}], "disabled": false, "createdAt": "*************", "lastLoginAt": "*************", "validSince": "**********"}, {"localId": "firebaseUidForOwners1", "displayName": "EQUTUM firebaseUidForOwners1", "email": "<EMAIL>", "emailVerified": true, "passwordHash": "fakeHash:salt=fakeSalt8qs7VygvbbBagihMHKql:password=abel1234", "salt": "fakeSalt8qs7VygvbbBagihMHKql", "passwordUpdatedAt": **********784, "providerUserInfo": [{"providerId": "password", "email": "<EMAIL>", "federatedId": "<EMAIL>", "rawId": "<EMAIL>", "displayName": "EQUTUM firebaseUidForOwners1"}], "disabled": false, "createdAt": "*************", "lastLoginAt": "*************", "validSince": "**********"}, {"localId": "firebaseUidForOwners2", "displayName": "EQUTUM firebaseUidForOwners2", "email": "<EMAIL>", "emailVerified": true, "passwordHash": "fakeHash:salt=fakeSalt8qs7VygvbbBagihMHKql:password=abel1234", "salt": "fakeSalt8qs7VygvbbBagihMHKql", "passwordUpdatedAt": **********784, "providerUserInfo": [{"providerId": "password", "email": "<EMAIL>", "federatedId": "<EMAIL>", "rawId": "<EMAIL>", "displayName": "EQUTUM firebaseUidForOwners2"}], "disabled": false, "createdAt": "*************", "lastLoginAt": "*************", "validSince": "**********"}, {"localId": "firebaseUidForOwners3", "displayName": "EQUTUM firebaseUidForOwners3", "email": "<EMAIL>", "emailVerified": true, "passwordHash": "fakeHash:salt=fakeSalt8qs7VygvbbBagihMHKql:password=abel1234", "salt": "fakeSalt8qs7VygvbbBagihMHKql", "passwordUpdatedAt": **********784, "providerUserInfo": [{"providerId": "password", "email": "<EMAIL>", "federatedId": "<EMAIL>", "rawId": "<EMAIL>", "displayName": "EQUTUM firebaseUidForOwners3"}], "disabled": false, "createdAt": "*************", "lastLoginAt": "*************", "validSince": "**********"}, {"localId": "firebaseUidNotEmailVerified", "displayName": "EQUTUM firebaseUidNotEmailVerified", "email": "<EMAIL>", "emailVerified": false, "salt": "fakeSalt8qs7VygvbbBagihMHKql", "disabled": false, "createdAt": "*************"}, {"localId": "firebaseUidGoogleProvider1", "displayName": "EQUTUM firebaseUidGoogleProvider1", "email": "<EMAIL>", "emailVerified": true, "providerUserInfo": [{"providerId": "google.com", "email": "<EMAIL>", "federatedId": "123456789012345678901", "rawId": "123456789012345678901", "displayName": "EQUTUM Google User", "photoUrl": "https://example.com/photo.jpg"}], "disabled": false, "createdAt": "*************", "lastLoginAt": "*************", "validSince": "**********"}]}